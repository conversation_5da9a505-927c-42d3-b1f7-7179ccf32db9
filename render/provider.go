package render

import (
	"context"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	// "git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type ProductProvider interface {
	GetProductId() int64
	GetProductCategory() int64
	GetBusinessID() int64
	GetCarpoolType() int64
	GetComboType() int64
	GetRequireLevel() string
	GetCarpoolPriceType() int32
	GetSubGroupId() int32
	IsDualCarpoolPrice() bool
	IsSpecialPrice() bool
	GetOrderType() int16
	GetCountPriceType() int32
	GetLevelType() int32
}

type BaseProvider interface {
	GetCityID() int
	GetAccessKeyId() int32
	//GetMenuId() string
	GetLang() string
	//GetAppVersion() string
	GetUserPID() int64
	GetAppVersion() string
}

type ApolloProvider interface {
	GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string)
}

type RequestProvider interface {
	GetUserChoosePayment() int32
	GetCarpoolSeatNum() int32
	GetPassengerCount() *int32
}

type BillInfoProvider interface {
	GetBillFeeDetailInfo() map[string]float64
	GetBillDetail() *PriceApi.EstimateNewFormBillInfo
	GetBillDriverMetre() int64
	GetBillInfoCurrency() string
	GetEstimateFee() float64
	GetCarpoolFailEstimateFee() (float64, bool) // 获取未拼成价格
	GetCapPrice() float64
	GetMemberDynamicProtectFee() float64 // 获取溢价保护
	GetDisplayLines() map[string]*PriceApi.DisplayLine
	GetPersonalEstimateFee() float64
	GetDynamicTotalFee() float64
	GetTotalFeeWithoutDiscount() float64
}

type ActivityInfoProvider interface {
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo                  // Discount 券
	GetBonusAmount() float64                                             // Discount 打车金
	GetHkBonus() *PriceApi.EstimateNewFormBonusInfo                      // Discount 香港打车金
	GetVCard() *PriceApi.EstimateNewFormVCardInfo                        // Discount 省钱卡
	GetRevolvingAccountDiscount() *PriceApi.RevolvingAccountDiscountInfo // Discount 泛快内循环折扣
	GetRevolvingAccountRebate() *PriceApi.RevolvingAccountRebateInfo     // Discount 泛快内循环返利
	GetSpecZiYouBao() *PriceApi.EstimateNewFormSpecZiYoubao              // Discount 自由宝
	GetRight() *PriceApi.EstimateNewFormRightInfo                        // Discount 权益
	GetCard() *PriceApi.EstimateNewFormCardInfo                          // Discount 畅行卡
	GetBusCard() *PriceApi.NewFormBusCard                                // Discount 大车出行卡
	GetMemberDiscountCard() *PriceApi.DiscountCard                       // Discount 折上折
	GetAlipayCoupon() *PriceApi.EstimateNewFormCouponInfo                // Discount 支付宝支付券
	GetTCDiscountFee() float64
	GetRenderDiscount() []*PriceApi.RenderDiscount               // Discount 货运/火车票券
	GetCarpoolFailedCoupon() *PriceApi.EstimateNewFormCouponInfo // 未拼成券
	GetExtendList() []*PriceApi.EstimateNewFormExtend            // 顺风车的券
	GetCashBack() *PriceApi.CashbackXinZhuInfo                   // 返现金
}

type PaymentInfoProvider interface {
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	IsBusinessPay() bool // 判断是否是企业付
}

type CarpoolInfoProvider interface {
	IsCarpoolV3Merge(context.Context) bool   // 判断是否是拼车一口价
	IsHaveCarpoolVCard(context.Context) bool // 判断是否使用省钱卡
	GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer
	GetExactSeatNum() int32
	GetComboID() int64
	IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool
	GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee
}

type MemberInfoProvider interface {
	GetMemberDpaInfo() *GoMember.PrivInfo
	GetLevelID() int32
}

type TaxiInfoProvider interface {
	GetTaxiSps() *hestia_charge.TaxiSpsData

	GetPricingByMeterPrice() float64
}

type BargainInfoProvider interface {
	GetBargainRecommendInfo() *bargain.RecommendPriceInfo
}

// QuotationInfoProvider 与price-api返回值天然互斥所以有重名
type QuotationInfoProvider interface {
	GetEstimateFee() float64
	GetDynamicTimes() float64
	GetPreTotalFee() float64
	GetCapPrice() float64
	GetOrderType() int16
	GetLevelID() int64

	GetDiscountDescByTypes(context.Context, []string) *price_api.DiscountItem
	GetFeeDetailInfo() map[string]float64
	GetAlipayCoupon() *PriceApi.CouponInfoV2

	GetMixedPaymentInfo(context.Context) *price_api.MixedPaymentInfo
	IsBusinessPay() bool

	GetTCDiscountFee() float64

	GetTunnelFeeDetail() map[string]float64
}

type DynamicIconABProvider interface {
	IsHitDynamicIconAb(ctx context.Context) bool
}
