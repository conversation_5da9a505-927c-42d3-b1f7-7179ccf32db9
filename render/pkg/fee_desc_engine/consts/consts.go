package consts

const (
	DefaultForm          = 0  // 默认表单
	SimpleForm           = 1  // 简化表单
	AnyCarForm           = 2  // 等待应答表单
	RecCarpoolOthersForm = 3  // 默认拼表单接口,其他车型的
	InterCityForm        = 4  // 城际拼车表单页
	AnyCarV3Form         = 5  // 等待应答V3表单
	RouteEstimateForm    = 6  // 路线预估表单
	OrderEstimateForm    = 7  // 行中预估表单
	CombinedTravelForm   = 10 // 组合出行
	MiniBusForm          = 11 // 小巴独立页
	DidiMiniForm         = 12 // 小神车独立页
	PetsTravelFrom       = 13 //宠物出行独立页
	SmartBusForm         = 14 // 智能小巴独立页
	XinZhuAppendCarForm  = 15 // 新竹追加车型表单
	HarbourForm          = 16 // Harbour表单 eg:香港

	DynamicBox = 99 // 动态盒子表单
)

const (
	SharpCap   = 0
	NoSharpCap = 1
)

const (
	PopeActivityNFold = 1
)

const (
	FeeDescription = "fee_description" // 配置apollo

	EstimateV3FeeDescNewStyle  = "anycar_v3-fee_detail_desc"
	EstimateV3FeeDescHK        = "anycar_v3-fee_detail_desc_hk"
	EstimateV4FeeDesc          = "estimate_form_v3-fee_desc"                   // 主表单
	EstimateV3FeeDesc          = "estimate_form_v3-fee_desc_v2"                // 表单信息收敛
	EstimateBigFontSizeFeeDesc = "estimate_form_v3-fee_desc_big_big_font_size" // 表单信息收敛大字版

	EstimateV4MemberFeeDesc    = "estimate_form_v3-fee_desc_member_coupon_info" // 主表单会员券
	FeeDetailDesc              = "simple_estimate-fee_detail_desc"              // 简化表单
	AnyCarFeeDetailDesc        = "anycar_estimate-fee_detail_desc"              // 等待应答配置
	AnyCarV3FeeDesc            = "anycar_v3-fee_detail_desc"                    // 等待应答v3
	XinZhuAppendCarFormFeeDesc = "xinzhu_form-fee_detail_desc"                  // 新竹追加车型表单
	RecCarpoolFeeDesc          = "rec_carpool-price_desc_list"                  // 默认拼
	DidiMiniFeeDesc            = "didi_mini-fee_desc"                           // 小神车
	OverseaEstimateDesc        = "oversea_estimate-fee_desc"                    // 境外打车

	RouteEstimateFeeDesc              = "route_estimate-fee_desc"                // 路线预估
	OrderEstimatePriceAssemblyFeeDesc = "order_estimate_price_assembly-fee_desc" // 价格组件预估

	RouteEstimateMemberFeeDesc = "route_estimate-fee_desc_member_coupon_info"

	AnyCarV3TaxiGapInfo = "anycar_v3-taxi_gap_info" // 出租车差距信息

	SimpleEstimateMemberCoupon    = "simple_estimate-member_coupon"
	SimpleEstimateCustomTagCoupon = "simple_estimate-custom_tag_coupon"

	AnyCarCustomTagCoupon = "anycar_estimate-custom_tag_coupon"

	CommonFeeDetailInfoHK = "common-fee_detail_info_hk"

	DynamicBoxFeeDetailInfo         = "dynamic_box-fee_detail_info"
	DynamicBoxFeeDetailInfoNewStyle = "dynamic_box-fee_detail_info_new_style"
	DynamicBoxFeeDetailInfoWithUnit = "dynamic_box-fee_detail_info_with_unit"
	FeeBusinessMax                  = "business_max"
	FeeDecrMax                      = "decr_max"
	FeeIncrMax                      = "incr_max"

	FeeBusinessMaxV3 = "business_max_v3"
	FeeDecrMaxV3     = "decr_max_v3"
	FeeIncrMaxV3     = "incr_max_v3"

	FeeBusinessMaxAnyCar = "business_max_any_car"
	FeeDecrMaxAnyCar     = "decr_max_any_car"
	FeeIncrMaxAnyCar     = "incr_max_any_car"
	FeeDynamicMaxAnyCar  = "dynamic_max_any_car"

	BusinessPay = "business_pay_deduction" // 企业付

	FeeCoupon                  = "coupon"                          // 券
	FeeBonus                   = "bonus"                           // 打车金
	FeeBusCard                 = "bus_card"                        // 打车金
	FeeRight                   = "right"                           // 权益
	FeeCard                    = "card"                            // 畅行卡
	FeeZiyoubao                = "ziyoubao"                        // 自由宝
	FeeLanKeBao                = "lankebao"                        // 揽客宝
	FeeTimeLimit               = "time_limit"                      // 出租车愿等
	FeeRevolvingDiscount       = "revolving_account_discount"      // 泛快内循环抵扣
	FeeRevolvingRebate         = "revolving_account_rebate"        // 泛快内循环返利
	FeeRevolvingRebateNFold    = "revolving_account_rebate_n_fold" // 泛快内循环N倍返
	FeeCarpoolChildTicketFee   = "carpool_child_ticket_fee"        // 儿童票
	FeeCarpoolInfantsTicketFee = "carpool_infants_ticket_fee"      // 携童票
	FeeComparePricingByMeter   = "compare_pricing_by_meter"        // 比打表省
	FeeMemberDiscountCard      = "member_discount_card"            // 折上折
	FeeTaxiPricingByOnline     = "taxi_pricing_by_online"          // 在线计价标签
	FeeAlipayCoupon            = "alipay_coupon"                   // 支付宝支付券
	FeeCashBackXinZhu          = "cashback_xin_zhu"                // 新竹返现

	FeeMemberProtect = "member_protect" // 溢价保护

	FeeRedPacket        = "red_packet"            // 春节服务费
	DynamicDiffPrice    = "dynamic_price"         // 动调费
	FeeEnergyConsume    = "energy_consume_fee"    // 能耗费
	FeeDesignatedDriver = "designated_driver_fee" // 豪华车指定司务员费用
	FeeCrossCity        = "cross_city_fee"        // 跨城费
	FeeInfoFee          = "info_fee"              // 信息费
	FeeHighway          = "highway_fee"           // 高速费

	FeeBarrierFreeCar   = "sps_barrier_free_car_fee" // 无障碍车服务费
	FeePetSeat          = "sps_pet_fee"              // 宠物专车服务
	FeePetPlatform      = "sps_pet_platform_fee"     // 宠物专车-专项保险和运营费
	FeePetCleanDiscount = "sps_pet_fee_discount"     // 宠物出行-清洁费减免
	FeeStationGuide     = "sps_pick_up_guide_fee"    // 接送机引导费用名
	FeePickUpGuideFree  = "sps_pick_up_guide_free"

	FeeTaxiHolidayPrice = "taxi_holiday_price" // 出租车节假日
	FeeTaxiPeak         = "taxi_peak_price"    // 出租车高峰期
	FeeSpecialPrice     = "taxi_special"       // 出租车特殊优惠

	CarpoolDiffFast    = "carpool_diff_fast"    // 比快车省
	CarpoolBusinessPay = "carpool_business_pay" // 拼车合并企业付

	FeeNormalDiscount = "normal_discount" // 合并优惠
	FeeNormalIncr     = "normal_increase" // 合并加价项

	MemberCoupon         = "member_coupon"       // 会员券
	FeeCarpoolPaidMember = "carpool_paid_member" // 拼车会员券

	TCDiscountFee           = "trip_cloud_discount_fee" // 网开台三方优惠
	MiniBusFeeDetailDesc    = "minibus-fee_desc"        // 小巴
	PetsTravelFeeDetailDesc = "pet_travel-fee_desc"     //宠物
	SmartBusFeeDetailDesc   = "smart_bus-fee_desc"      // 智能小巴

	CheaperThanFastCar = "cheaper_than_fast_car" //比快车省

	FeeKeyTunnelFeeDetail = "BILL2API_tunnel_fee_detail"
	FeeHkTunnelFee        = "hk_tunnel_fee"    // 香港隧道费
	FeeHkBonus            = "hk_bonus"         // 香港打车金
	FeeHkCoupon           = "hk_coupon"        // 香港券
	HkDynamicPrice        = "hk_dynamic_price" //香港动调感知

	DcmpKeyHarbourFeeDescAggregation = "harbour-form_fee_desc_aggregation"
	DcmpKeyHarbourFeeDesc            = "harbour-form_fee_desc"
)

type Type int32

const (
	TypeIncrement          = 1  // 加价项
	TypeNeutral            = 2  // 中性项
	TypeDecrement          = 3  // 减价项
	TypeBusinessPay        = 4  // 企业付
	TypeZiyoubao           = 5  // 自由宝
	TypeDynamic            = 6  // 动态调价
	TypeRevolvingDiscount  = 9  // 内循环账户折扣
	TypeFeeRevolvingRebate = 10 // 内循环账户返利
)

const (
	MergePriorityDefault = 999
)
