package category_bargain

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
)

type BargainNeedPayFeeProvider interface {
	render.BillInfoProvider
	GetBargainBubbleRecommendFee() float64
	GetDynamicTotalFee() float64
	GetProductCategory() int64
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetNeedPayFeeAmount(ctx context.Context, prov BargainNeedPayFeeProvider) *float64 {
	if estimate_pc_id.EstimatePcIdBargain != prov.GetProductCategory() {
		return nil
	}

	if prov.GetPrivateBizInfo().BargainData.FixPrice != nil {
		// 券前价返回
		amount := cast.ToFloat64(prov.GetPrivateBizInfo().BargainData.FixPrice.DynamicTotalFee)
		price.CheckSingle(ctx, "BargainGetNeedPayFeeAmount", "BargainDynamicTotalFee", "FixPrice.DynamicTotalFee", amount)
		return &amount
	}

	//amount := prov.GetBargainBubbleRecommendFee()
	//if amount > 0 {
	//	amount = util.RoundAbs(amount, 2)
	//	price.CheckSingle(ctx, "BargainGetNeedPayFeeAmount", "GetBargainBubbleRecommendFee", "RecommendFee", amount)
	//	return &amount
	//}
	// 推荐价为空，兜底使用券前价
	amount := util.RoundAbs(prov.GetDynamicTotalFee(), 2)
	price.CheckSingle(ctx, "BargainGetNeedPayFeeAmount", "GetDynamicTotalFee", "DynamicTotalFee", amount)
	return &amount
}
