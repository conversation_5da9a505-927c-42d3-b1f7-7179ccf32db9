package car_info

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/combo_type"
	"strconv"
	"sync"

	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	TaxiUtil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const CarIconDcmpConf = "anycar_estimate-car_info"
const CarIconSwitch = "car_icon_config_switch"
const SinkFormExpName = "athena_form_anycar_sinking_switch"
const TripCloudCarIconType = "tripcloud_car_icon_type"
const DefaultCarIcon = "https://pt-starimg.didistatic.com/static/starimg/img/XtE5lO2oQq1641537142732.png"
const CarIconTypeNormal int32 = 0
const CarIconTypeNew int32 = 1

type CarIconProviderV2 interface {
	GetProductId() int64
	GetProductCategory() int64
	GetSubGroupId() int32
	GetCityID() int
	GetCountyID() int
	GetCountyName() string
	GetTabId() string
	GetPageType() int32
	GetEstimateStyleType() int32
	GetUserPhone() string
	GetAppVersion() string
	GetLang() string
	GetAccessKeyId() int32
	GetUserPID() int64
	GetPricingBoxData() *TaxiUtil.PricingBoxData
	GetPrivateBizInfo() *models.PrivateBizInfo
	GetCarpoolType() int64
	GetRouteType() int64
	IsTripcloud(context.Context) bool

	render.ProductProvider
	render.ApolloProvider
}

type CarIconItem struct {
	Name string `json:"_name"`
	Icon string `json:"icon"`
}

type RegionalConf struct {
	Icon   string `json:"icon"`
	Cities []int  `json:"cities"`
}

func GetCarIconV2(ctx context.Context, prov CarIconProviderV2) string {
	var carIcon string

	//if estimate_pc_id.EstimatePcIdBargain == prov.GetProductCategory() {
	//	if prov.GetPrivateBizInfo() != nil && prov.GetPrivateBizInfo().BargainData.SenseConfig != nil &&
	//		prov.GetPrivateBizInfo().BargainData.SenseConfig.Icon != "" {
	//		return prov.GetPrivateBizInfo().BargainData.SenseConfig.Icon
	//	}
	//}

	// 从青铜门获取
	carIcon = getCarIconFromBronzeDoor(ctx, prov)
	if carIcon != "" {
		return carIcon
	}

	// 三方表单
	if ClassTab == prov.GetTabId() {
		carInfo := GetCarInfoForClassifyTab(ctx, prov)
		carIcon = carInfo.Icon
	}

	// 兜底配置
	if "" == carIcon {
		carIcon = getDefaultCarIcon(ctx, prov)
	}

	return carIcon
}

func GetCarIconType(ctx context.Context, prov CarIconProviderV2) int32 {
	// 非三方表单
	if prov.GetTabId() != ClassTab {
		return CarIconTypeNormal
	}

	// 非主表单
	if prov.GetPageType() != page_type.PageTypeDefault {
		return CarIconTypeNormal
	}

	if prov.GetComboType() == combo_type.ComboTypeInterCity && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolInter {
		return CarIconTypeNormal
	}

	// 智能小巴
	if prov.GetCarpoolType() == carpool_type.CarpoolTypeSmartCarpool {
		return CarIconTypeNormal
	}

	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithTabID)
	if prov.IsTripcloud(ctx) && apollo.FeatureToggle(ctx, TripCloudCarIconType, key, params) {
		return CarIconTypeNew
	}

	return CarIconTypeNormal
}

func getDefaultCarIcon(ctx context.Context, prov CarIconProviderV2) string {
	// 出租车计价盒子
	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox {
		icon := TaxiUtil.GetCarIconByPcId(ctx, prov.GetProductCategory())
		if len(icon) > 0 {
			return icon
		}
	}

	icon := getCarIconByFormdataMateriel(ctx, prov)
	if icon != "" {
		return icon
	}

	if prov.GetCarpoolType() == consts.CarPoolTypeInterCityStation {
		config := dcmp.GetDcmpContent(ctx, "config_text-station_bus_icon", nil)
		var stationBusIcon = make(map[string]string)
		if err := json.Unmarshal([]byte(config), &stationBusIcon); err != nil {
			log.Trace.Warnf(ctx, "station_bus_icon", "Unmarshal station_bus_icon error : %v", err)
			return ""
		}
		return stationBusIcon["icon"]
	} else {
		return DefaultCarIcon
	}
}

func getCarIconByFormdataMateriel(ctx context.Context, prov CarNormalIconProvider) string {
	var icon = ""
	// 按照pcid、lang获取物料配置
	condition := map[string]string{"product_category": strconv.Itoa(int(prov.GetProductCategory())), "lang": "zh-CN"}
	configs, err := apollo.GetConfigsByNamespaceAndConditionsWithLang(ctx, NSMaterial, condition, "zh-CN")

	if configs == nil || err != nil || len(configs) < 1 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return icon
	}

	// 品类默认icon
	icon, _ = configs[0].GetStringValue("car_icon")

	var regionConf = make([]*RegionalConf, 0)
	err = configs[0].GetJsonValue("icon_regional_configs", &regionConf)

	if err == nil {
		// 按城市配置的icon
		for _, cityConfig := range regionConf {
			for _, city := range cityConfig.Cities {
				if prov.GetCityID() == city {
					return cityConfig.Icon
				}
			}
		}
	}

	return icon
}

var carIconConf = make(map[string]CarIconItem)
var lock sync.RWMutex

func GetCarIconByDcmp(ctx context.Context, prov CarNormalIconProvider) string {
	lock.Lock()
	defer lock.Unlock()

	if item, ok := carIconConf[cast.ToString(prov.GetProductCategory())]; ok && len(item.Icon) > 0 {
		return item.Icon
	} else {
		// 判读是否已有内容
		if len(carIconConf) > 0 {
			item, ok := carIconConf["default"]
			if ok && len(item.Icon) > 0 {
				return item.Icon
			}
		}

		// dcmp配置中的车型图标
		config := dcmp.GetDcmpContent(ctx, CarIconDcmpConf, nil)
		if err := json.Unmarshal([]byte(config), &carIconConf); err != nil {
			log.Trace.Warnf(ctx, "car_icon_config", "Unmarshal car_icon_config error : %v", err)
		}
		// 读取dcmp失败，返回空
		if len(carIconConf) == 0 {
			return ""
		}

		if item, ok := carIconConf[cast.ToString(prov.GetProductCategory())]; ok && len(item.Icon) > 0 {
			return item.Icon
		} else {
			item, ok := carIconConf["default"]
			if ok && len(item.Icon) > 0 {
				return item.Icon
			}
		}
	}

	return ""
}

// 物料配置迁移灰度
func switchIconConfig(ctx context.Context, prov CarNormalIconProvider) bool {
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, CarIconSwitch, key, params)
}
