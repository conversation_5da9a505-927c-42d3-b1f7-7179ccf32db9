package user_pay_info

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const (
	PaymentTypeComponent = "payment_type_component"
	DCMPUserPayInfo      = "payment-user_pay_info"
	Title                = "title"
	SubTitle             = "sub_title"
)

type PaymentTypeList struct {
	Language        string        `json:"__language"`
	PaymentItemInfo []PaymentItem `json:"payment_type_list"`
}

type PaymentItem struct {
	PaymentType    string `json:"payment_type"`
	CarSupportDesc string `json:"car_support_desc"`
	SortScore      string `json:"sort_score"`
}

type UserPayInfoProvider interface {
	render.BaseProvider
	render.RequestProvider
	render.ProductProvider
}

type userPayInfo struct {
	UserPayInfoProvider
	baseReq  *models.BaseReqData
	products []*biz_runtime.ProductInfoFull
}

func NewUserPayInfo(baseReq *models.BaseReqData, products []*biz_runtime.ProductInfoFull) *userPayInfo {
	return &userPayInfo{
		baseReq:  baseReq,
		products: products,
	}
}

func (u *userPayInfo) GetUserPayInfo(ctx context.Context) *proto.PaymentOptionModule {
	var (
		allDefaultPayType    = make(map[int32]int)
		allDefaultPayInfoArr = make([]*proto.PaymentOption, 0, 0)
		allPaymentInfo       = make(map[int32]*proto.PaymentOption)

		userSelectPayType int32
	)

	if u.baseReq != nil {
		userSelectPayType = u.baseReq.CommonInfo.PaymentsType
	}
	for _, p := range u.products {
		paymentInfo := p.GetPaymentInfo()
		if paymentInfo == nil {
			continue
		}

		allDefaultPayType[paymentInfo.DefaultPayType] = 1
		PaymentList := paymentInfo.Payment
		for _, paymentItem := range PaymentList {
			if paymentItem == nil || *paymentItem.Disabled == 1 {
				continue
			}

			payType := paymentItem.PayType
			if _, ok := allPaymentInfo[payType]; !ok {
				payment := &proto.PaymentOption{
					Msg: *paymentItem.ChannelName,
					Tag: payType,
				}

				allPaymentInfo[payType] = payment
				allDefaultPayInfoArr = append(allDefaultPayInfoArr, payment)
			}
		}
	}

	// 选择默认支付方式
	if userSelectPayType == 0 {
		if _, ok := allPaymentInfo[userSelectPayType]; ok {
			allPaymentInfo[userSelectPayType].IsSelected = 1
			allPaymentInfo[userSelectPayType].Selected = true
		}
	} else if len(allDefaultPayType) == 1 {
		for payType := range allDefaultPayType {
			if _, ok := allPaymentInfo[payType]; ok {
				allPaymentInfo[payType].IsSelected = 1
				allPaymentInfo[payType].Selected = true
			}
		}
	}

	weightMap := u.getPayTypeSortConf(ctx)
	sort.Slice(allDefaultPayInfoArr, func(i, j int) bool {
		payTypeOne := allDefaultPayInfoArr[i]
		payTypeTwo := allDefaultPayInfoArr[j]

		return weightMap[payTypeOne.Tag] > weightMap[payTypeTwo.Tag]
	})

	return &proto.PaymentOptionModule{
		Title:       dcmp.GetJSONContentWithPath(ctx, DCMPUserPayInfo, nil, Title),
		SubTitle:    dcmp.GetJSONContentWithPath(ctx, DCMPUserPayInfo, nil, SubTitle),
		PaymentList: allDefaultPayInfoArr,
	}
}

func (u *userPayInfo) getPayTypeSortConf(ctx context.Context) map[int32]int {
	var paymentScoreMap = make(map[int32]int)
	config, err := apollo.GetConfigsByNamespace(ctx, PaymentTypeComponent)
	if err != nil {
		log.Trace.Warnf(ctx, "getPayTypeSortConf", "get pay type sort conf fail,err:%v", err)
		return nil
	}

	paymentTypeList := []*PaymentTypeList{}
	err = json.Unmarshal(config, &paymentTypeList)
	if err != nil || len(paymentTypeList) == 0 {
		log.Trace.Warnf(ctx, "getPayTypeSortConf", "unmarshal fail, err:%v, config:%v", err, string(config))
		return nil
	}

	paymentInfo := paymentTypeList[0]
	for _, payment := range paymentTypeList {
		if payment.Language == u.baseReq.CommonInfo.Lang {
			paymentInfo = payment
		}
	}

	for _, item := range paymentInfo.PaymentItemInfo {
		paymentType, err := strconv.Atoi(item.PaymentType)
		if err != nil {
			log.Trace.Warnf(ctx, "getPayTypeSortConf", "payment type atoi fail, err:%v", err)
			continue
		}
		score, err := strconv.Atoi(item.SortScore)
		if err != nil {
			log.Trace.Warnf(ctx, "getPayTypeSortConf", "score atoi fail, err:%v", err)
			continue
		}

		paymentScoreMap[int32(paymentType)] = score
	}

	return paymentScoreMap
}
