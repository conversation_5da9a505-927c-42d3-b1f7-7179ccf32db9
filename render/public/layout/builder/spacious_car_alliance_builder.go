package builder

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"strconv"
)

const (
	singleLayoutDcmpKey = "estimate_form_v3-link_product_desc"
)

type SpaciousCarAllianceBuilder struct {
	baseBuilder
	product            *biz_runtime.ProductInfoFull
	linkToProduct      *biz_runtime.ProductInfoFull
	pcIDs              []string
	isSelected         int32
	linkFromSelected   int32
	hasCarSelectionFee bool
	topList            []int64
	linkToRecPos       int32
}

func NewSpaciousBuilder(product *biz_runtime.ProductInfoFull,
	linkToProduct *biz_runtime.ProductInfoFull,
	data *models.BaseReqData,
	estimateData map[int64]*LayoutConsts.SimpleEstimateData,
	themeData *proto.NewFormThemeData) IBuilder {
	return &SpaciousCarAllianceBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
			themeData:    themeData,
		},
		product:       product,
		linkToProduct: linkToProduct,
	}
}

func (s *SpaciousCarAllianceBuilder) Access() bool {
	return true
}

func (s *SpaciousCarAllianceBuilder) GetName() string {
	return "SpaciousCarAllianceBuilder"
}

func (s *SpaciousCarAllianceBuilder) PreBuild(ctx context.Context) {
	var (
		pcIDs = make([]string, 0)
	)

	pcIDs = append(pcIDs, util.ToString(s.linkToProduct.GetProductCategory()))
	s.pcIDs = pcIDs
	s.isSelected = int32(s.linkToProduct.Product.BizInfo.CheckStatus)
	s.linkFromSelected = int32(s.product.Product.BizInfo.CheckStatus)
	if s.product != nil && s.product.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar { //车大有选车费
		s.hasCarSelectionFee = s.hitCarSelectionFee()
	}
	if s.product != nil && s.product.BaseReqData != nil && s.product.BaseReqData.CommonBizInfo.TopData != nil {
		s.topList = s.product.BaseReqData.CommonBizInfo.TopData.TopPcIdList
	}

	if s.reqData.CommonBizInfo.RecPosMap != nil {
		if item, ok := s.reqData.CommonBizInfo.RecPosMap[int32(s.linkToProduct.GetProductCategory())]; ok {
			s.linkToRecPos = item
		} else {
			// 兜底推荐区域
			s.linkToRecPos = consts.RecPosRecommendArea
		}
	} else {
		// 兜底推荐区域
		s.linkToRecPos = consts.RecPosRecommendArea
	}
}

func (s *SpaciousCarAllianceBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)
	group := &proto.NewFormGroup{
		Products: s.pcIDs,
		Type:     consts.SpaciousGroupType,
		GroupId:  s.BuildGroupID(consts.SingleType, s.linkToProduct.GetProductCategory()),
	}

	if (s.product.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar && s.hasCarSelectionFee) || s.product.GetProductCategory() == estimate_pc_id.EstimatePcIdFastHandpicked {
		group.IsSelected = s.isSelected
		group.LinkInfo = s.getLinkInfo(ctx)
	}

	groups = append(groups, group)
	s.groups = groups
	return s.groups
}

func (s *SpaciousCarAllianceBuilder) BuildFormShowType(ctx context.Context) int32 {
	if util.InArrayInt64(s.product.GetProductCategory(), s.topList) ||
		util.InArrayInt64(s.linkToProduct.GetProductCategory(), s.topList) {
		return consts.TopArea
	}

	return s.athenaRecommendAreaToFormShowType(s.linkToRecPos)
}

func (s *SpaciousCarAllianceBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	item := s.GetThemeDataSingleCar(ctx, s.product.GetProductCategory())
	if item == nil {
		item = s.GetThemeDataSingleCar(ctx, s.linkToProduct.GetProductCategory())
	}

	return item
}

func (s *SpaciousCarAllianceBuilder) BuildWeight(ctx context.Context) int64 {
	s.buildCommonWeight(ctx)
	return s.weight
}

func (s *SpaciousCarAllianceBuilder) hitCarSelectionFee() bool {
	if s.product.GetBillFeeDetailInfo() == nil {
		return false
	}

	_, ok := s.product.GetBillFeeDetailInfo()["talos_spacious_car_selection_fee"]
	if ok {
		return true
	}

	return false
}

func (s *SpaciousCarAllianceBuilder) getLinkInfo(ctx context.Context) *proto.NewFormLinkInfo {
	// 单勾场景在高版本全量，不考虑车大非单勾场景 构建甄选下挂
	return s.buildSingleStyleLinkInfo(ctx)
}

func (s *SpaciousCarAllianceBuilder) buildSingleStyleLinkInfo(ctx context.Context) *proto.NewFormLinkInfo {
	var (
		tagItem  = &proto.NewFormPreferDataTag{}
		tagList  = make([]*proto.NewFormPreferDataTag, 0)
		linkInfo = &proto.NewFormLinkInfo{}
	)
	conf := dcmp.GetJSONMap(ctx, singleLayoutDcmpKey, util.ToString(s.product.GetProductCategory()))
	if len(conf) <= 0 {
		return nil
	}
	tagIcon := conf["tag_icon"].String()
	tagContent := conf["tag_content"].String()
	desc := conf["desc"].String()
	feeMsg := conf["fee_msg"].String()
	infoUrl := conf["info_url"].String()

	tagItem = &proto.NewFormPreferDataTag{
		Icon:    tagIcon,
		Content: tagContent,
	}
	tagList = append(tagList, tagItem)

	linkInfo.PreferData = &proto.NewFormPreferData{
		Desc:    desc,
		TagList: tagList,
	}
	linkInfo.InfoUrl = s.buildUrl(ctx, infoUrl)
	linkInfo.IsSelected = s.linkFromSelected
	linkInfo.LinkProduct = util.ToString(s.product.GetProductCategory())
	linkInfo.IsStrength = 1
	linkInfo.SelectStyle = 1
	linkInfo.FeeMsg = s.buildSingleFeeMsg(ctx, feeMsg)
	linkInfo.FeeDescList = s.getFeeDescList(ctx, []int64{s.product.GetProductCategory()}, []int32{fee_consts.TypeIncrement, fee_consts.TypeDecrement, fee_consts.TypeBusinessPay})

	return linkInfo
}

func (s *SpaciousCarAllianceBuilder) buildUrl(ctx context.Context, info_url string) string {
	eid := s.product.GetEstimateID()
	token := s.product.BaseReqData.PassengerInfo.Token
	lang := s.product.GetLang()
	appVersion := s.product.GetAppVersion()
	accessKeyID := strconv.FormatInt(int64(s.product.GetAccessKeyId()), 10)

	if eid != "" && token != "" && info_url != "" {
		return info_url + "?estimate_id=" + eid + "&lang=" + lang +
			"&app_version=" + appVersion + "&access_key_id=" + accessKeyID +
			"&token=" + token
	}
	return ""
}

func (s *SpaciousCarAllianceBuilder) buildSingleFeeMsg(ctx context.Context, template string) string {
	feeAmount := s.product.GetEstimateFee()
	if s.product.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		feeAmount = s.product.GetPersonalEstimateFee()
	}
	apolloKey, apolloParam := s.product.GetApolloParams(biz_runtime.WithPIDKey)
	fee := util2.PriceFormat(ctx, apolloParam, apolloKey, feeAmount, consts.FeeTypeDefault)
	price.CheckSingle(ctx, "SpaciousCarAllianceBuilder", "buildSingleFeeMsg", "feeAmount", feeAmount)
	return dcmp.TranslateTemplate(template, map[string]string{
		"fee_amount": fee,
	})
}
