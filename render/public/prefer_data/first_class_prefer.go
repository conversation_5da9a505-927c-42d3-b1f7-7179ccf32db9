package prefer_data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/driver/car_level"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/passenger-common/common/carlevel"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant"
)

const PreferInfo = "perfer_info"
const displayIconSelectDcmpKey = "prefer_info-display_icon_select"
const displayNameSelectDefaultDcmpKey = "prefer_info-display_name_select_default"
const miniAppDisplayNameSelectDefaultDcmpKey = "prefer_info-mini_app_display_name_select_default"
const displayNamePreferDefaultDcmpKey = "prefer_info-display_name_prefer_default"
const designatedDriverConfigDcmpKey = "prefer_info-designated_driver_config"
const displayNamePreferFilterDcmpKey = "config_luxury-display_name_prefer_filter"
const supportPreferInfoToggle = "gs_is_support_prefer_info_toggle"
const luxurySupportPreferInfoToggle = "gs_luxury_prefer_url"
const miniAppSupportPreferInfoToggle = "gs_first_car_prefer_toggle"
const sixSeatLuxurySupportToggle = "six_seat_lux_toggle"
const sixSeatLuxury = "six_seat_lux_judge"

type UfsPreferData struct {
	DisplayTags string `json:"display_tags"`
}

func GetPreferDataOfFirstClass(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {
	var firstClassPreferData *proto.NewFormPreferData
	// 是小程序
	if isMiniApp(prov) {
		firstClassPreferData = getMiniAppPreferData(ctx, prov)
	} else {
		firstClassPreferData = getNormalPreferData(ctx, prov)
	}

	return firstClassPreferData
}

func getNormalPreferData(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {

	var selectDesc = dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)
	var tagList = make([]*proto.NewFormPreferDataTag, 0)
	var jumpUrl string

	tagList = append(tagList, &proto.NewFormPreferDataTag{
		Icon:    dcmp.GetDcmpContent(ctx, displayIconSelectDcmpKey, nil),
		Content: dcmp.GetDcmpContent(ctx, displayNameSelectDefaultDcmpKey, nil),
	})

	tagList = append(tagList, &proto.NewFormPreferDataTag{
		Content: dcmp.GetDcmpContent(ctx, displayNamePreferDefaultDcmpKey, nil),
	})

	// 可选服务，已选服务
	availableService := getAllAvailableCustomService(prov)
	chosenOption := getCustomFeatures(prov)
	userChosenCustomService := make(map[int]int, 0)
	for _, feature := range chosenOption {
		userChosenCustomService[int(feature.ID)] = int(feature.Count)
	}

	// ufs 记录的服务
	preferDataMix := getPreferOptionData(ctx, prov)

	if len(availableService) > 0 || "" != preferDataMix {
		driverTag, driverSelect := getDesignatedDriverTag(ctx, prov)
		preferTag, preferSelect := getPreferOptionTag(ctx, preferDataMix)
		availAndChosenTags := getCustomServiceTag(availableService, userChosenCustomService)
		customDisplayNames, desc := getCustomDisplayNames(ctx, availAndChosenTags, preferTag, preferSelect, driverSelect)

		tagList = []*proto.NewFormPreferDataTag{driverTag}

		if len(customDisplayNames) > 0 {
			tagList = append(tagList, &proto.NewFormPreferDataTag{
				Icon:    preferTag.Icon,
				Content: strings.Join(customDisplayNames, "/"),
			})
		}

		selectDesc = desc
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	allow, parameter := apollo.GetParameters(luxurySupportPreferInfoToggle, pidKey, params)
	if allow && parameter != nil && len(parameter) > 0 {
		// 支持六座豪华
		if apollo.FeatureToggle(ctx, sixSeatLuxurySupportToggle, pidKey, params) {
			// 当前是六座豪华车
			if apollo.FeatureToggle(ctx, sixSeatLuxury, pidKey, params) {
				jumpUrl = parameter["jump_url_six_seat"]
			} else {
				jumpUrl = parameter["jump_url_normal"]
			}
		} else {
			jumpUrl = parameter["jump_url"]
		}
	}

	return &proto.NewFormPreferData{
		Desc:    selectDesc,
		TagList: tagList,
		JumpUrl: &jumpUrl,
	}

}

func getMiniAppPreferData(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {
	var selectDesc string
	var tagList = make([]*proto.NewFormPreferDataTag, 0)
	var jumpUrl string

	pidKey, param := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(ctx, miniAppSupportPreferInfoToggle, pidKey, param) {
		selectDesc = dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)

		tagList = append(tagList, &proto.NewFormPreferDataTag{
			Icon:    dcmp.GetDcmpContent(ctx, displayIconSelectDcmpKey, nil),
			Content: dcmp.GetDcmpContent(ctx, miniAppDisplayNameSelectDefaultDcmpKey, nil),
		})

		tagList = append(tagList, &proto.NewFormPreferDataTag{
			Content: dcmp.GetDcmpContent(ctx, displayNamePreferDefaultDcmpKey, nil),
		})

		// 可选服务，已选服务
		availableService := getAllAvailableCustomService(prov)
		chosenOption := getOptionSettings(prov)
		userChosenCustomService := make(map[int]int, 0)
		if chosenOption != nil && len(chosenOption.Options) > 0 {
			for _, feature := range chosenOption.Options {
				userChosenCustomService[int(feature.ID)] = int(feature.Count)
			}
		}

		// ufs 记录的服务
		preferDataMix := getPreferOptionData(ctx, prov)

		if len(availableService) > 0 || "" != preferDataMix {
			driverTag, driverSelect := getDesignatedDriverTag(ctx, prov)
			preferTag, preferSelect := getPreferOptionTag(ctx, preferDataMix)
			availAndChosenTags := getCustomServiceTag(availableService, userChosenCustomService)
			customDisplayNames, desc := getCustomDisplayNames(ctx, availAndChosenTags, preferTag, preferSelect, driverSelect)

			tagList = []*proto.NewFormPreferDataTag{driverTag}

			if len(customDisplayNames) > 0 {
				tagList = append(tagList, &proto.NewFormPreferDataTag{
					Icon:    driverTag.Icon,
					Content: strings.Join(customDisplayNames, "/"),
				})
			}

			selectDesc = desc
		}

		allow, parameter := apollo.GetParameters(luxurySupportPreferInfoToggle, pidKey, param)
		if allow && parameter != nil && len(parameter) > 0 {
			// 支持六座豪华
			if apollo.FeatureToggle(ctx, sixSeatLuxurySupportToggle, pidKey, param) {
				// 当前是六座豪华车
				if apollo.FeatureToggle(ctx, sixSeatLuxury, pidKey, param) {
					jumpUrl = parameter["jump_url_six_seat"]
				} else {
					jumpUrl = parameter["jump_url_normal"]
				}
			}
		}

		return &proto.NewFormPreferData{
			Desc:    selectDesc,
			TagList: tagList,
			JumpUrl: &jumpUrl,
		}
	}

	preferData := &proto.NewFormPreferData{
		Desc: dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil),
		TagList: []*proto.NewFormPreferDataTag{{
			Icon:    dcmp.GetDcmpContent(ctx, displayIconSelectDcmpKey, nil),
			Content: dcmp.GetDcmpContent(ctx, miniAppDisplayNameSelectDefaultDcmpKey, nil),
		}},
	}

	driverTag, driverSelect := getDesignatedDriverTag(ctx, prov)
	if driverSelect {
		preferData.SetTagList([]*proto.NewFormPreferDataTag{driverTag})
		preferData.SetDesc(dcmp.GetDcmpContent(ctx, selectDesc, nil))
	}

	return preferData
}

func getPreferOptionData(ctx context.Context, prov PreferDataProvider) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if !apollo.FeatureToggle(ctx, supportPreferInfoToggle, pidKey, params) {
		return ""
	}

	conditions := map[string]string{"passenger_id": prov.GetPassengerId()}

	ret, err := ufs.GetFeature(ctx, "passenger.luxury", []string{PreferInfo}, conditions, "")
	if err != nil || ret[PreferInfo] == nil || ret[PreferInfo].Errno != 0 {
		return ""
	}

	if ret[PreferInfo] != nil {
		return *ret[PreferInfo].Value
	}

	return ""
}

func getDesignatedDriverTag(ctx context.Context, prov PreferDataProvider) (*proto.NewFormPreferDataTag, bool) {
	var isSelect = false

	var tag = &proto.NewFormPreferDataTag{
		Icon:    dcmp.GetDcmpContent(ctx, displayIconSelectDcmpKey, nil),
		Content: dcmp.GetDcmpContent(ctx, displayNameSelectDefaultDcmpKey, nil),
	}

	if isMiniApp(prov) {
		tag.SetContent(dcmp.GetDcmpContent(ctx, miniAppDisplayNameSelectDefaultDcmpKey, nil))
	}

	designatedDriver, _ := strconv.ParseInt(prov.GetDesignatedDriver(), 10, 64)
	carLvl, _ := strconv.Atoi(prov.GetRequireLevel())

	driverDesc := dcmp.GetJSONContentWithPath(ctx, designatedDriverConfigDcmpKey, nil, prov.GetDesignatedDriver())
	if designatedDriver < 0 && "" != driverDesc {
		tag.SetContent(driverDesc)
		isSelect = true
	} else if car_level.CarLevelXingzhengji != carLvl {
		carInfo := carlevel.GetGCarLevel(constant.CarLevel(carLvl))
		if carInfo == nil {
			return tag, isSelect
		}
		var carName string
		if prov.GetLang() == ENUS {
			carName = carInfo.LevelNameEn
		} else {
			carName = carInfo.LevelName
		}
		if "" != carName {
			tag.SetContent(carName)
		}
		isSelect = true
	}

	return tag, isSelect
}

func getPreferOptionTag(ctx context.Context, preferDataMix string) (*proto.NewFormPreferDataTag, bool) {
	tag := &proto.NewFormPreferDataTag{
		Content: dcmp.GetDcmpContent(ctx, displayNamePreferDefaultDcmpKey, nil),
	}

	preferData := &UfsPreferData{}
	err := json.Unmarshal([]byte(preferDataMix), preferData)
	if err != nil {
		return tag, false
	}

	if preferData.DisplayTags == "" {
		return tag, false
	}

	splitDisplayNames := strings.Split(preferData.DisplayTags, "/")
	needFilterTag := dcmp.GetDcmpContent(ctx, displayNamePreferFilterDcmpKey, nil)
	var displayNames = make([]string, 0)
	var filters = make([]string, 0)

	err = json.Unmarshal([]byte(needFilterTag), &filters)
	if err != nil {
		return tag, false
	}

	// 过滤的偏好设置
	for _, name := range splitDisplayNames {
		if len(filters) <= 0 {
			break
		}

		if !util.InArrayStr(name, filters) {
			displayNames = append(displayNames, name)
		}
	}

	if len(displayNames) > 0 {
		tag.SetContent(strings.Join(displayNames, "/"))
		return tag, true
	}

	return tag, false
}

func getCustomServiceTag(allAvailableCustomService map[int]*hundunClient.PcServiceData, userChosenCustomService map[int]int) map[string][]*proto.NewFormPreferDataTag {
	var tags = make(map[string][]*proto.NewFormPreferDataTag, 0)
	var chosen = make([]*proto.NewFormPreferDataTag, 0)
	var available = make([]*proto.NewFormPreferDataTag, 0)

	if len(allAvailableCustomService) <= 0 {
		return tags
	}

	for serviceId, service := range allAvailableCustomService {
		tag := &proto.NewFormPreferDataTag{
			Content: service.Title,
			Icon:    service.Icon,
		}

		if choose, ok := userChosenCustomService[serviceId]; ok {
			tag.SetIcon(service.LightIcon)

			if service.Max > 1 {
				tag.SetContent(fmt.Sprintf("%s x%d", tag.GetContent(), choose))
			}
			chosen = append(chosen, tag)
		}

		available = append(available, tag)
	}

	tags["chosenTags"] = chosen
	tags["availableTags"] = available

	return tags
}

func getCustomDisplayNames(ctx context.Context, chosenAndavailableCustomTags map[string][]*proto.NewFormPreferDataTag, preferTag *proto.NewFormPreferDataTag, preferSelect bool, driverSelect bool) ([]string, string) {
	var customDisplayNames = make([]string, 0)
	var desc string

	chosentags := chosenAndavailableCustomTags["chosenTags"]
	availabletags := chosenAndavailableCustomTags["availableTags"]

	if isExistAvailableCustom(chosentags, preferSelect, driverSelect) {
		if chosentags != nil && len(chosentags) > 0 {
			for _, chosentag := range chosentags {
				customDisplayNames = append(customDisplayNames, chosentag.Content)
			}
		}

		if preferSelect {
			customDisplayNames = append(customDisplayNames, preferTag.Content)
		}

		desc = dcmp.GetDcmpContent(ctx, selectedDcmpKey, nil)

	} else {

		if availabletags != nil && len(availabletags) > 0 {
			for _, availabletag := range availabletags {
				customDisplayNames = append(customDisplayNames, availabletag.Content)
			}
		}

		customDisplayNames = append(customDisplayNames, preferTag.Content)

		desc = dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)

	}

	return customDisplayNames, desc
}

func isExistAvailableCustom(chosenTags []*proto.NewFormPreferDataTag, preferSelect bool, driverSelect bool) bool {
	if driverSelect || preferSelect {
		return true
	}

	if chosenTags != nil && len(chosenTags) > 0 {
		return true
	}

	return false
}

func isMiniApp(prov PreferDataProvider) bool {
	return util.InArrayInt32(prov.GetAccessKeyId(), []int32{9, 22})
}
