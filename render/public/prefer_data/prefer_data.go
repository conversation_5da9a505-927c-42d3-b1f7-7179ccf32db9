package prefer_data

import (
	"context"
	"encoding/json"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"strconv"
	"strings"

	"git.xiaojukeji.com/nuwa/trace"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const optionOpenControl = "pre_sale_option_open_control"
const optionServiceConfig = "option_service_config"
const cityServiceConfig = "city_support_service_config"
const selectedDcmpKey = "prefer_info-prefer_desc_select"
const canSelectDcmpKey = "prefer_info-prefer_desc"
const priorityFeatureDcmpKey = "prefer_info-priority_feature"
const preferUrlToggle = "gs_prefer_url"
const ENUS = "en-US"
const ZHHK = "zh-HK"

type PreferDataProvider interface {
	GetLang() string
	GetCityId() int
	GetCountyID() int
	GetPageType() int32
	GetPassengerId() string
	GetDesignatedDriver() string
	GetAccessKeyId() int32
	GetEstimateId() string
	GetTaxiSpsData() *hestia_charge.TaxiSpsData
	GetBillTaxiPeakFee() float64
	GetIntelTaxiPeakFee() int64
	IsSwitchTaxiPeakIntel() bool
	GetMultiRequireProduct() []models.RequireProduct
	GetOrderType() int16
	GetDepartureTime() int64
	GetServiceList() []*hundunClient.PcServiceData
	GetAthenaRecommend() int32
	render.ProductProvider
	render.ApolloProvider
}

type PreferOptionRsp struct {
	Title          string         `json:"title"`
	SetOptionTitle string         `json:"set_option_title"`
	Icon           string         `json:"icon"`
	ServiceList    map[int]string `json:"service_list"`
	ImContact      string         `json:"im_contact"`
}

type PreferOptionFullConfig struct {
	Title            *LangCfg         `json:"title"`
	SetOptionTitle   *LangCfg         `json:"set_option_title"`
	ServiceList      []*OptionService `json:"service_list"`
	Head             *LangCfg         `json:"head"`
	HeadLink         string           `json:"head_link"`
	HeadLinkCity     string           `json:"head_link_city"`
	IsSupportTitle   int              `json:"is_support_title"`
	IsSupportRemark  int              `json:"is_support_remark"`
	IsImDirectSend   int              `json:"is_im_direct_send"`
	HeadTitle        *LangCfg         `json:"head_title"`
	HeadTitleCity    *LangCfg         `json:"head_title_city"`
	HeadSubTitle     *LangCfg         `json:"head_sub_title"`
	HeadSubTitleCity *LangCfg         `json:"head_sub_title_city"`
	HeadImg          string           `json:"head_img"`
	HeadImgCity      string           `json:"head_img_city"`
	StartBgColor     string           `json:"start_bg_color"`
	EndBgColor       string           `json:"end_bg_color"`
	Boardcast        string           `json:"boardcast"`
	ImContact        string           `json:"im_contact"`
	ImContactEn      string           `json:"im_contact_en"`
	Icon             string           `json:"icon"`
}

type OptionService struct {
	OptionId int      `json:"option_id"`
	Title    *LangCfg `json:"title"`
}

type LangCfg struct {
	Chinese  string `json:"zh-CN"`
	English  string `json:"en-US"`
	Hongkong string `json:"zh-HK"`
}

type PreferOptionCityConfig struct {
	City          string   `json:"city"`
	OptionService []string `json:"option_service"`
}

type PriorityDcmpInfo struct {
	PriorityServiceId []int  `json:"priority_service_id"`
	Conjunction       string `json:"conjunction"`
}

func GetPreferData(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {
	if product_id.ProductIdDefault == prov.GetProductId() {
		return GetPreferDataOfCustomService(ctx, prov)
	}

	if product_id.ProductIdHLTaxiCar == prov.GetProductId() {
		return GetPreferDataOfHkTaxiFeature(ctx, prov)
	}

	if product_id.ProductIdFirstClassCar == prov.GetProductId() {
		return GetPreferDataOfFirstClass(ctx, prov)
	}

	if product_id.ProductIdUniOne == prov.GetProductId() {
		return GetPreferDataOfUniTaxiFeature(ctx, prov)
	}

	return nil
}

func GetPreferDataOfCustomService(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {

	// ssse avaliale service
	s3eServiceMap := getAllAvailableCustomService(prov)

	// city config service
	preferOption := getPreferOptionConfig(ctx, prov)
	serviceConfig := getServiceConfigByLang(preferOption, prov.GetLang())

	// empty return nil
	if len(s3eServiceMap) <= 0 && (serviceConfig == nil || len(serviceConfig.ServiceList) <= 0) {
		return nil
	}

	// custome_features
	chosenFeatures := getCustomFeatures(prov)
	userCustomFeatureMap := make(map[int]int, 0)
	if chosenFeatures != nil && len(chosenFeatures) > 0 {
		for _, feature := range chosenFeatures {
			userCustomFeatureMap[int(feature.ID)] = int(feature.Count)
		}
	}

	// option_settings
	chosenOptions := getOptionSettings(prov)
	chosenPreferOption := getUserChosenPreferOption(chosenOptions, serviceConfig.ServiceList)

	// getSelectDesc 可选服务 已选服务
	selectDesc := ""
	if (len(userCustomFeatureMap) <= 0) && (chosenPreferOption == nil || len(chosenPreferOption) <= 0) {
		selectDesc = dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)
	} else {
		selectDesc = dcmp.GetDcmpContent(ctx, selectedDcmpKey, nil)
	}

	// tryGetPreferContent
	preferDataTag := getTagList(ctx, s3eServiceMap, userCustomFeatureMap, chosenPreferOption, serviceConfig)

	// $oFeatureToggle => jump url
	jumpUrl := ""
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	allow, parameter := apollo.GetParameters(preferUrlToggle, pidKey, params)
	if allow && parameter != nil && len(parameter) > 0 {
		jumpUrl = parameter["jump_url"]
	}

	return &proto.NewFormPreferData{
		Desc:    selectDesc,
		TagList: []*proto.NewFormPreferDataTag{preferDataTag},
		JumpUrl: &jumpUrl,
	}
}

func getTagList(ctx context.Context, customServiceMap map[int]*hundunClient.PcServiceData, userChosenFeature map[int]int, chosenPreferOption map[int]string, serviceConfig *PreferOptionRsp) *proto.NewFormPreferDataTag {
	var chosenContent = make([]string, 0)
	for serviceId, service := range customServiceMap {
		if _, ok := userChosenFeature[serviceId]; !ok {
			continue
		}
		chosenContent = append(chosenContent, service.Title)
	}

	if len(chosenPreferOption) > 0 {
		for _, optionTitle := range chosenPreferOption {
			chosenContent = append(chosenContent, optionTitle)
		}
	}

	if len(chosenContent) > 0 {
		return &proto.NewFormPreferDataTag{
			Content: strings.Join(chosenContent, "/"),
			Icon:    serviceConfig.Icon,
		}
	}

	return &proto.NewFormPreferDataTag{
		Content: getMaxPriorityServiceTitle(ctx, customServiceMap) + serviceConfig.Title,
		Icon:    serviceConfig.Icon,
	}

}

func getUserChosenPreferOption(chosenOption *models.OptionSetting, serviceList map[int]string) map[int]string {

	if chosenOption == nil || len(chosenOption.Options) <= 0 {
		return nil
	}

	var chosenPreferOption = make(map[int]string, 0)
	for _, option := range chosenOption.Options {
		if len(serviceList[int(option.OptionId)]) <= 0 || option.Count <= 0 {
			continue
		}
		chosenPreferOption[int(option.OptionId)] = serviceList[int(option.OptionId)]
	}

	return chosenPreferOption
}

func getAllAvailableCustomService(prov PreferDataProvider) map[int]*hundunClient.PcServiceData {
	customServiceList := prov.GetServiceList()
	customServiceMap := make(map[int]*hundunClient.PcServiceData, 0)

	for _, service := range customServiceList {
		customServiceMap[int(service.ServiceId)] = service
	}

	return customServiceMap
}

func getServiceConfigByLang(config *PreferOptionFullConfig, lang string) *PreferOptionRsp {
	var rsp = &PreferOptionRsp{
		ServiceList: make(map[int]string),
	}

	if config == nil || len(config.ServiceList) <= 0 {
		return rsp
	}

	switch lang {

	case ZHHK:
		rsp.Title = config.Title.Hongkong
		rsp.SetOptionTitle = config.SetOptionTitle.Hongkong
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.Hongkong
		}

	case ENUS:
		rsp.Title = config.Title.English
		rsp.SetOptionTitle = config.SetOptionTitle.English
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.English
		}

	default:
		rsp.Title = config.Title.Chinese
		rsp.SetOptionTitle = config.SetOptionTitle.Chinese
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.Chinese
		}
	}

	rsp.Icon = config.Icon

	if "en-US" == lang {
		rsp.ImContact = config.ImContactEn
	}

	return rsp
}

func getPreferOptionConfig(ctx context.Context, prov PreferDataProvider) *PreferOptionFullConfig {
	// 城市灰度
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["product_id"] = strconv.FormatInt(prov.GetProductId(), 10)
	if !apollo.FeatureToggle(ctx, optionOpenControl, pidKey, params) {
		return nil
	}

	// get full config
	var configInfofull = &PreferOptionFullConfig{}
	condition := model.NewCondition(map[string]string{"product_id": strconv.Itoa(int(prov.GetProductId()))})
	if rawFullCfg := getConfig(ctx, optionServiceConfig, condition); rawFullCfg != nil && len(rawFullCfg) > 0 {
		configs := make([]PreferOptionFullConfig, 0)
		if err := json.Unmarshal(rawFullCfg, &configs); err == nil {
			configInfofull = &configs[0]
		}

	}

	// get city config
	var configInfoCity = &PreferOptionCityConfig{}
	condition = model.NewCondition(map[string]string{"city_id": strconv.Itoa(prov.GetCityId())})
	if rawCityCfg := getConfig(ctx, cityServiceConfig, condition); rawCityCfg != nil && len(rawCityCfg) > 0 {
		configs := make([]PreferOptionCityConfig, 0)
		if err := json.Unmarshal(rawCityCfg, &configs); err == nil {
			configInfoCity = &configs[0]
		}
	}

	if len(configInfoCity.OptionService) <= 0 {
		return configInfofull
	}

	// get intersection of full and city
	newServiceList := make([]*OptionService, 0)
	for _, service := range configInfofull.ServiceList {
		if util.InArrayStr(strconv.Itoa(service.OptionId), configInfoCity.OptionService) {
			newServiceList = append(newServiceList, service)
		}
	}

	configInfofull.ServiceList = newServiceList

	return configInfofull
}

func getConfig(ctx context.Context, configName string, condition *model.Condition) []byte {

	configs, err := apollo.GetConfigsByNamespaceAndConditions(ctx, configName, condition)

	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo config read err with %v and conf: %v", err, configName)
		return nil
	}

	return configs
}

func getMaxPriorityServiceTitle(ctx context.Context, availableCustomService map[int]*hundunClient.PcServiceData) string {
	var selectTile string
	if len(availableCustomService) <= 0 {
		return ""
	}

	rawCfg := dcmp.GetDcmpContent(ctx, priorityFeatureDcmpKey, nil)

	dcmpInfo := &PriorityDcmpInfo{}
	err := json.Unmarshal([]byte(rawCfg), dcmpInfo)
	if err != nil {
		return ""
	}

	for _, serviceId := range dcmpInfo.PriorityServiceId {
		if service, ok := availableCustomService[serviceId]; ok {
			selectTile = service.Title
		}
	}

	if selectTile != "" {
		selectTile = selectTile + dcmpInfo.Conjunction
	}

	return selectTile
}

func getOptionSettings(prov PreferDataProvider) *models.OptionSetting {
	options := &models.OptionSetting{}

	requireProducts := prov.GetMultiRequireProduct()
	if requireProducts != nil && len(requireProducts) > 0 {
		for _, product := range requireProducts {
			if prov.GetProductCategory() == product.ProductCategory {
				_ = json.Unmarshal([]byte(product.OptionSettings), &options)
				return options

			}
		}
	}
	return nil
}

func getCustomFeatures(prov PreferDataProvider) []*models.FeatureItem {
	features := make([]*models.FeatureItem, 0)

	requireProducts := prov.GetMultiRequireProduct()
	if requireProducts != nil && len(requireProducts) > 0 {
		for _, product := range requireProducts {
			if prov.GetProductCategory() == product.ProductCategory {
				_ = json.Unmarshal([]byte(product.CustomFeature), &features)
				return features
			}
		}
	}

	return features
}
