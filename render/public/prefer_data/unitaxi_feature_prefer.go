package prefer_data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	ApolloSDK "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/is_hide_price"
)

const (
	customServiceTaxiPeak = 107
	taxiPeakFeeConfig     = "taxi_peak_fee"
	taxiPeakFeeConfigV2   = "taxi_peak_fee-peak_passenger_config"

	tagServiceNS = "tag_service"
	tagName      = "new_user"
)

type InteractiveDcmpInfo struct {
	NoInteractive                *InteractiveItem `json:"no_interactive"`
	NoInteractiveWithoutEstimate *InteractiveItem `json:"no_interactive_without_estimate"`
	Interactive                  *InteractiveItem `json:"interactive"`
	InteractiveWithoutEstimate   *InteractiveItem `json:"interactive_without_estimate"`
	InteractiveWithDiscount      *InteractiveItem `json:"interactive_with_discount"`
}

type InteractiveItem struct {
	Title       string `json:"title"`
	Content     string `json:"content"`
	BorderColor string `json:"border_color"`
	InfoUrl     string `json:"info_url"`
	Icon        string `json:"icon"`
}

func GetPreferDataOfUniTaxiFeature(ctx context.Context, prov PreferDataProvider) *proto.NewFormPreferData {
	taxiPeakService := getSSSEData(prov)
	if taxiPeakService == nil {
		return nil
	}

	// 峰值加价数据
	taxiSpsData := prov.GetTaxiSpsData()
	if taxiSpsData == nil || prov.GetOrderType() == 1 {
		return nil
	}

	// 已选
	isChoose := false
	chosenOption := getCustomFeatures(prov)
	for _, item := range chosenOption {
		if item == nil {
			continue
		}
		if item.ID == customServiceTaxiPeak && item.Count > 0 {
			isChoose = true
		}
	}

	if taxiSpsData.CanSelect == 1 {
		isSelect := isChoose || firstDefaultSelection(ctx, prov)

		var price string
		selectFlag := 0
		if isSelect {
			selectFlag = 1
		}
		if isSelect {
			price = util.FormatPrice(prov.GetBillTaxiPeakFee(), 2)
			selectFlag = 1
		} else {
			//峰期智能出价，需要切数据源
			isSwitch := prov.IsSwitchTaxiPeakIntel()
			if isSwitch {
				price = util.FormatFenToYuan(prov.GetIntelTaxiPeakFee(), 2)
			} else {
				price = util.FormatFenToYuan(int64(taxiSpsData.PassengerPrice+taxiSpsData.PassengerDiscount), 2)
			}
		}

		if util.ToFloat64(price) == 0 {
			return nil
		}
		interactiveConf := getInteractiveDcmpInfo(ctx, prov)
		textConf := getWayOutConf(ctx, taxiSpsData, interactiveConf, prov)

		return &proto.NewFormPreferData{
			Desc:       textConf.Title,
			FeeMsg:     fmt.Sprintf(textConf.Content, price),
			InfoUrl:    getInfoUrl(ctx, prov, interactiveConf),
			Id:         strconv.Itoa(customServiceTaxiPeak),
			Count:      1,
			IsSelected: int32(selectFlag),
		}

	}

	return nil
}

func getInteractiveDcmpInfo(ctx context.Context, prov PreferDataProvider) *InteractiveDcmpInfo {

	m := map[string]string{"product_category": strconv.Itoa(int(prov.GetProductCategory())), "city_id": "0"}
	configs, err := apollo.GetConfigsByNamespaceAndConditions(taxiPeakFeeConfig, model.NewCondition(m))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo taxi_peak_fee-way_out_conf read err with %v and conf %v", err, configs)
		return nil
	}

	allConfig := configs.GetAllConfigs()
	if len(allConfig) > 0 {
		apoInfo := make(map[string]interface{}, 0)
		rawData, ok := allConfig[0].GetValue("dcmp_key")
		if !ok {
			return nil
		}
		err = json.Unmarshal([]byte(rawData), &apoInfo)
		if err != nil || apoInfo["way_out"] == nil {
			return nil
		}

		interactiveConf := &InteractiveDcmpInfo{}
		dcmpKey, ok := apoInfo["way_out"].(string)
		if !ok {
			return nil
		}
		if prov.GetTaxiSpsData().IsHoliday == 1 {
			dcmpKey = "taxi_peak_fee-way_out_conf_holiday"
		}
		dcmpInfo := dcmp.GetDcmpContent(ctx, dcmpKey, nil)
		err = json.Unmarshal([]byte(dcmpInfo), interactiveConf)
		if err != nil {
			return nil
		}

		if interactiveConf.Interactive != nil {
			return interactiveConf
		}
	}

	return nil
}

func getWayOutConf(ctx context.Context, taxiSpsData *hestia_charge.TaxiSpsData, interactiveConf *InteractiveDcmpInfo, prov PreferDataProvider) *InteractiveItem {
	if taxiSpsData.CanSelect != 1 {
		if 0 == *is_hide_price.GetResult(ctx, prov) {
			return interactiveConf.NoInteractive
		} else {
			return interactiveConf.InteractiveWithoutEstimate
		}
	} else {
		if taxiSpsData.PassengerDiscount != 0 && prov.GetTaxiSpsData().IsHoliday == 1 {
			return interactiveConf.InteractiveWithDiscount
		} else {
			return interactiveConf.Interactive
		}
	}
}

func getInfoUrl(ctx context.Context, prov PreferDataProvider, interactiveConf *InteractiveDcmpInfo) string {
	var infoUrl string
	urlParam := map[string]string{
		"city_id":          strconv.Itoa(prov.GetCityId()),
		"product_category": strconv.FormatInt(prov.GetProductCategory(), 10),
		"product_id":       strconv.FormatInt(prov.GetProductId(), 10),
		"estimate_id":      prov.GetEstimateId(),
	}

	if interactiveConf != nil && interactiveConf.Interactive != nil {
		infoUrl = interactiveConf.Interactive.InfoUrl
	}

	//是否展示v2版本的信息费说明
	infoUrl, urlParam = getInfoUrlV2(ctx, prov, infoUrl, urlParam)
	if "" != infoUrl {
		return fmt.Sprintf(infoUrl, httpBuildQuery(urlParam))
	}

	return ""
}

func getInfoUrlV2(ctx context.Context, prov PreferDataProvider, url string, params map[string]string) (string, map[string]string) {
	var (
		infoUrl  string
		IsChange = false
	)

	apolloParam := map[string]string{
		"pid":          prov.GetPassengerId(),
		"passenger_id": prov.GetPassengerId(),
		"city":         strconv.Itoa(prov.GetCityId()),
		"from":         "1",
	}

	//Apollo控制是否要展示
	IsChange = ApolloSDK.FeatureToggle(ctx, "taxi_peak_fee_rule_propaganda", cast.ToString(prov.GetPassengerId()), apolloParam)
	if !IsChange {
		return url, params
	}

	infoUrl = dcmp.GetDcmpContent(ctx, taxiPeakFeeConfigV2, nil)
	if infoUrl == "" {
		return url, params
	}

	urlParam := map[string]string{
		"departure_time":   cast.ToString(prov.GetDepartureTime()),
		"product_category": cast.ToString(prov.GetProductCategory()),
		"product_id":       cast.ToString(prov.GetProductId()),
		"estimate_id":      cast.ToString(prov.GetEstimateId()),
		"county_id":        cast.ToString(prov.GetCountyID()),
		"trip_cityid":      cast.ToString(prov.GetCityId()),
	}

	return infoUrl, urlParam
}

func isFirstEstimate(prov PreferDataProvider) bool {
	return prov.GetMultiRequireProduct() == nil || len(prov.GetMultiRequireProduct()) == 0
}

func getSSSEData(prov PreferDataProvider) *hundunClient.PcServiceData {
	availableService := getAllAvailableCustomService(prov)

	for _, service := range availableService {
		if customServiceTaxiPeak == service.ServiceId {
			return service
		}
	}

	return nil
}

func httpBuildQuery(params map[string]string) string {
	paramArr := make([]string, 0, len(params))

	for k, v := range params {
		paramArr = append(paramArr, fmt.Sprintf("%s=%s", k, v))
	}

	if len(paramArr) > 0 {
		return strings.Join(paramArr, "&")
	}

	return ""
}

// 首次预估选择
func firstDefaultSelection(ctx context.Context, prov PreferDataProvider) bool {
	if !isFirstEstimate(prov) {
		return false
	}
	athenaRecommed := prov.GetAthenaRecommend()
	if athenaRecommed != consts.Checked {
		return true
	}
	isNewStrategy := ApolloSDK.FeatureToggle(ctx, "taxi_peak_fee_select_new_strategy", prov.GetPassengerId(), map[string]string{
		"key":  prov.GetPassengerId(),
		"pid":  prov.GetPassengerId(),
		"city": strconv.FormatInt(int64(prov.GetCityId()), 10),
	})
	if !isNewStrategy {
		return true
	}
	// 新用户直接返回true
	if isNewUser(ctx, prov.GetPassengerId()) {
		return true
	}
	feature, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, "unione_taxi.peak_fee_selection",
		map[string]string{"passenger_id": prov.GetPassengerId()})
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrUFS, "get ufs unione_taxi.peak_fee_selection err=%v, pid=%s", err, prov.GetPassengerId())
		return true
	}
	if feature == "1" {
		return true
	} else {
		return false
	}
}

// isNewUser 是否是新用户
func isNewUser(ctx context.Context, pid string) bool {
	conditions := model.NewCondition(
		map[string]string{
			"tag_name": tagName,
		})
	configs, err := ApolloSDK.GetConfigsByNamespaceAndConditions(ctx, tagServiceNS, conditions)
	if err != nil {
		log.Trace.Warnf(ctx, "taxi_peak_fee", "tag_name: %s not config", tagName)
		return false
	}
	config := []*struct {
		TagNumber string `json:"tag_number"`
	}{}
	if err := json.Unmarshal(configs, &config); err != nil {
		log.Trace.Warnf(ctx, "taxi_peak_fee", "tag_name: %s unmarshal err: %s", tagName, err)
		return false
	}
	if len(config) <= 0 {
		return false
	}
	tagNumber := config[0].TagNumber
	return tag_service.IsHitTags(ctx, pid, tagNumber)
}
