package toast_tip

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const toastTipDcmpKey = "minibus-toast"

type ToastTipDcmpInfo struct {
	Pcid     []int64 `json:"productcategorys"`
	ToastTip string  `json:"toast_tip"`
}

func GetToastTip(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) *string {
	var (
		toastTip       string
		preAppear      bool
		currentAppear  bool
		callCarType    int32
		departureTime  int64
		stopOverPoints []*price_api.StopoverPointInfo
	)

	// 预估后提示文案, 针对小巴渲染
	dcmpInfo := &ToastTipDcmpInfo{}
	dcmpConf := dcmp.GetDcmpContent(ctx, toastTipDcmpKey, nil)
	err := json.Unmarshal([]byte(dcmpConf), dcmpInfo)
	if err != nil || len(estimateProducts) <= 0 {
		return &toastTip
	}

	req := estimateProducts[0].BaseReqData
	// 首次预估有小巴
	for _, product := range req.CommonBizInfo.MultiRequireProduct {
		if util.InArrayInt64(product.ProductCategory, dcmpInfo.Pcid) {
			preAppear = true
			break
		}
	}

	// 当次预估有小巴
	for _, prod := range estimateProducts {
		if util.InArrayInt64(prod.GetProductCategory(), dcmpInfo.Pcid) {
			currentAppear = true
			break
		}
	}

	stopOverPoints = req.AreaInfo.StopoverPointInfo
	callCarType = req.CommonInfo.CallCarType
	departureTime = req.CommonInfo.DepartureTime

	// 首次预估包含 二次预估不包含 无途经点
	if preAppear && !currentAppear && len(stopOverPoints) <= 0 && callCarType == 0 && departureTime == 0 {
		toastTip = dcmpInfo.ToastTip
	}

	return &toastTip
}
