package order_button_info

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	"math"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

const (
	EtdDurationDcmpKey = "config_text-onestop85_etd_des"
)

func GetOrderButtonInfo(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) *proto.OrderButtonInfo {
	var minEtd int32 = math.MaxInt32

	for _, product := range estimateProducts {
		prov := &data.EstimateV4Adapter{ProductInfoFull: product}
		if estimate_v4.GetSelection(ctx, prov) != consts.Checked {
			continue
		}
		if etpInfo, ok := product.BaseReqData.CommonBizInfo.ETInfoMap[int32(product.GetProductCategory())]; ok {
			if etpInfo.Etd != 0 && etpInfo.Etd < minEtd {
				minEtd = etpInfo.Etd
			}
		}
	}

	if minEtd == math.MaxInt32 || minEtd <= 0 {
		return nil
	}
	return &proto.OrderButtonInfo{
		ExpectInfoText: dcmp.GetDcmpContent(ctx, EtdDurationDcmpKey, map[string]string{
			"min": cast.ToString(minEtd),
		}),
	}
}
