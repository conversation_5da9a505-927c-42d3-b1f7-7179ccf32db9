package adapter

import (
	"context"
	"fmt"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"github.com/spf13/cast"
)

type Adapter struct {
	*biz_runtime.QuotationV2
	*proto.PEstimateByOrderRequest

	orderInfo *order_info.OrderInfo
	userInfo  *passport.UserInfo

	_ap *apollo_model.ParamsConnector
}

func (a *Adapter) GetUserPID() int64 {
	if a.userInfo == nil {
		return 0
	}

	return int64(a.userInfo.PID)
}

func NewAdapter(request *model.ServiceRequest) *Adapter {
	if request == nil {
		return nil
	}

	return &Adapter{
		QuotationV2:             request.QuotationInfo,
		PEstimateByOrderRequest: request.Req,
		orderInfo:               request.OrderInfo,

		userInfo: request.UserInfo,
	}
}

func (a *Adapter) GetBillDetail() *PriceApi.EstimateNewFormBillInfo {
	return nil
}

func (a *Adapter) GetCarpoolFailEstimateFee() (float64, bool) {
	return 0, false
}

func (a *Adapter) GetCapPrice() float64 {
	return a.QuotationV2.GetCapPrice()
}

func (a *Adapter) GetMemberDynamicProtectFee() float64 {
	return 0
}

func (a *Adapter) GetDisplayLines() map[string]*PriceApi.DisplayLine {
	return nil
}

func (a *Adapter) GetRequireLevel() string {
	return a.QuotationV2.GetRequireLevel()
}

func (a *Adapter) GetSubGroupId() int32 {
	return 0
}

func (a *Adapter) IsDualCarpoolPrice() bool {
	return a.QuotationV2.GetIsDualCarpoolPrice()
}

func (a *Adapter) IsSpecialPrice() bool {
	return a.QuotationV2.GetIsSpecialPrice()
}

func (a *Adapter) GetCountPriceType() int32 {
	return a.QuotationV2.GetCountPriceType()
}

func (a *Adapter) IsCarpoolV3Merge(ctx context.Context) bool {
	return false
}

func (a *Adapter) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key string, value string)) (key string, params map[string]string) {
	if a._ap == nil {
		a._ap = &apollo_model.ParamsConnector{
			City:        strconv.Itoa(a.GetCityID()),
			Phone:       a.GetPhone(),
			UID:         strconv.Itoa(a.GetUid()),
			PID:         strconv.Itoa(a.GetPid()),
			Lang:        a.GetLang(),
			AccessKeyID: strconv.FormatInt(int64(a.GetAccessKeyId()), 10),
			AppVersion:  a.GetAppVersion(),
		}

		var productCategory, county, isCrossCity, menuID string
		county = cast.ToString(a.GetCounty())
		isCrossCity = util.Bool2string(a.GetCityID() != a.GetToCityID())
		menuID = consts.MenuIDDaCheAnyCar

		productCategory = cast.ToString(a.GetProductCategory())

		a._ap.SetProductCategory(productCategory).SetCounty(county).SetMenuID(menuID).SetIsCrossCity(isCrossCity)

	}

	return a._ap.ApolloParamsGen(keyFunc, paramsFunc...)
}

func (a *Adapter) GetApolloParams(f func(full *biz_runtime.ProductInfoFull) string, f2 ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string) {
	key := fmt.Sprintf("%d", a.userInfo.UID)
	ret := map[string]string{
		"city":             fmt.Sprintf("%d", a.GetCityID()),
		"phone":            a.userInfo.Phone,
		"uid":              fmt.Sprintf("%d", a.GetUid()),
		"pid":              fmt.Sprintf("%d", a.GetPid()),
		"city_id":          fmt.Sprintf("%d", a.GetCityID()),
		"lang":             a.GetLang(),
		"access_key_id":    fmt.Sprintf("%d", a.GetAccessKeyId()),
		"app_version":      a.GetAppVersion(),
		"product_category": strconv.FormatInt(a.GetProductCategory(), 10),
		"menu_id":          consts.MenuIDDaCheAnyCar,
	}

	return key, ret
}

func (a *Adapter) GetCarpoolType() int64 {
	return a.QuotationV2.GetCarpoolType()
}

func (a *Adapter) GetLevelType() int32 {
	return a.QuotationV2.GetLevelType()
}

func (a *Adapter) GetBusinessID() int64 {
	return int64(a.QuotationV2.GetBusinessID())
}

func (a *Adapter) GetComboType() int64 {
	return int64(a.QuotationV2.GetComboType())
}

func (a *Adapter) GetUid() int {
	if a.userInfo == nil {
		return 0
	}

	return int(a.userInfo.UID)
}

func (a *Adapter) GetPid() int {
	if a.userInfo == nil {
		return 0
	}

	return int(a.userInfo.PID)
}

func (a *Adapter) GetProductCategory() int64 {
	if a.QuotationV2 == nil {
		return 0
	}

	return a.QuotationV2.ProductCategory
}

func (a *Adapter) GetPhone() string {
	if a.userInfo == nil {
		return ""
	}

	return a.userInfo.Phone
}

func (a *Adapter) GetCounty() int32 {
	if a.QuotationV2 == nil {
		return 0
	}

	return a.QuotationV2.GetFromCounty()
}
