package multi_estimate_by_order

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	consts2 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	feeDescConst "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/multi_estimate_by_order/adapter"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"github.com/spf13/cast"
)

func Render(ctx context.Context, serviceReq *model.ServiceRequest, product *biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	if serviceReq == nil || product == nil {
		return nil, NewErrors.NewBizError(errors.New("service request is nil"), NewErrors.ErrnoSystemError)
	}

	feePrefix, feeAmount, feeType := buildFeeInfo(ctx, serviceReq, product)

	//更新账单
	updateTravel(ctx, serviceReq, feeAmount)

	return &proto.PEstimateByOrderData{
		EstimateId:   product.GetEstimateID(),
		EstimateFee:  util.String2float64(ctx, feeAmount),
		FeePrefix:    feePrefix,
		FeeType:      int32(feeType),
		FeeDetailUrl: fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), product.GetEstimateID()),
		DiscountMsg:  buildDiscountMsg(ctx, serviceReq, product),
	}, nil
}

func updateTravel(ctx context.Context, serviceReq *model.ServiceRequest, feeAmount string) {
	if serviceReq.OrderInfo != nil && cast.ToInt(serviceReq.OrderInfo.GetOrderStatus()) == int(order_info.StartBilling) && serviceReq.OrderInfo.GetCapPrice() > 0 {
		req := &Plutus.UpdateTravelReq{
			TravelId:     cast.ToInt64(serviceReq.OrderInfo.TravelId),
			DiscountInfo: map[int64][]*Plutus.DiscountInfo{},
			OrderId:      proto.Int64Ptr(cast.ToInt64(serviceReq.OrderInfo.OrderId)),
			PushPrice: &Plutus.PushPrice{
				BubbleSubTitle: cast.ToString(feeAmount),
				UseFlag:        "1",
			},
		}
		_ = plutus.UpdateTravel(ctx, req)
	}
}

// needBuildDiscountMsg 通用判断
func needBuildDiscountMsg(ctx context.Context, serviceReq *model.ServiceRequest, product *biz_runtime.ProductInfoFull) bool {

	if product == nil {
		return false
	}

	// 宠物出行需要展示优惠信息
	apolloParam := map[string]string{
		"product_category": strconv.FormatInt(product.GetProductCategory(), 10),
		"pid":              strconv.FormatInt(product.GetUserPID(), 10),
	}
	isAllow := apollo.FeatureToggle(ctx, "pet_travel_fee_prefix", strconv.FormatInt(product.GetUID(), 10), apolloParam)
	if isAllow {
		return true
	}

	// 一口价不展示优惠
	if product.GetCapPrice() > 0 {
		return false
	}

	// 升舱不展示优惠
	if serviceReq.IsUpgrade {
		return false
	}

	products := tripcloud.GetTripcloudProducts()
	isTC := tripcloud.IsTripcloudProduct(consts2.ProductID(product.GetProductId()), products)
	isStationBus := tripcloud.IsIntercityStationBus(product.GetCarpoolType())
	isSmallCar := tripcloud.IsIntercitySmallCar(product.GetComboType(), product.GetCarpoolType())
	// 三方 && 非客企大巴 && 非客企小车
	if isTC && !isStationBus && !isSmallCar {
		apolloParams := map[string]string{
			"pid":              serviceReq.OrderInfo.PassengerId,
			"level_type":       cast.ToString(product.GetLevelType()),
			"product_id":       cast.ToString(product.GetProductId()),
			"business_id":      cast.ToString(product.GetBusinessID()),
			"require_level":    cast.ToString(product.GetRequireLevel()),
			"product_category": cast.ToString(product.GetProductCategory()),
		}

		isAllow := apollo.FeatureToggle(ctx, "gs_tc_need_build_discount_msg", strconv.FormatInt(product.GetUID(), 10), apolloParams)
		return isAllow
	}

	return true // 默认需要展示优惠信息
}

func buildDiscountMsg(ctx context.Context, serviceReq *model.ServiceRequest, product *biz_runtime.ProductInfoFull) *string {
	if product == nil {
		return nil
	}

	needBuild := needBuildDiscountMsg(ctx, serviceReq, product)
	if !needBuild {
		return nil
	}

	env := fee_desc_engine.NewEnv(feeDescConst.OrderEstimateForm).SetApolloParams(product).SetDcmpKey(feeDescConst.OrderEstimatePriceAssemblyFeeDesc)
	feeDescList := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, product, feeDescConst.OrderEstimateForm), env).SetProductCategory(product.GetProductCategory()).Do(ctx)
	if len(feeDescList) > 0 && feeDescList[0] != nil {
		return &feeDescList[0].Content
	}

	return nil
}

func RenderByIntercity(ctx context.Context, serviceReq *model.ServiceRequest, product *biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	if serviceReq == nil || serviceReq.QuotationInfo == nil {
		return nil, NewErrors.NewBizError(errors.New("service request is nil"), NewErrors.ErrnoSystemError)
	}
	if serviceReq.QuotationInfo.EstimateFee <= 0 {
		return nil, NewErrors.NewBizError(errors.New("invalid estimate fee"), NewErrors.ErrnoSystemError)
	}
	//从dcmp获取价格容器title，兜底使用`固定价`
	feePreFix := dcmp.GetJSONContentWithPath(ctx, "intercity_new-price_container_text_conf", nil, "fee_pre_fix")
	if feePreFix == "" {
		feePreFix = "一口价"
	}
	return &proto.PEstimateByOrderData{
		EstimateId:  serviceReq.QuotationInfo.EstimateId,
		EstimateFee: serviceReq.QuotationInfo.EstimateFee,
		FeePrefix:   feePreFix,
		FeeType:     consts.FeeTypeDefault, //城际拼车走一口价逻辑
	}, nil
}

// RenderByFavorable 惠选车Render
func RenderByFavorable(ctx context.Context, serviceReq *model.ServiceRequest) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	if serviceReq == nil || serviceReq.OrderInfo == nil {
		return nil, NewErrors.NewBizError(errors.New("service request is nil"), NewErrors.ErrnoSystemError)
	}
	prefix := dcmp.GetDcmpContent(ctx, "cap_price-price_desc_favorable_title", nil)
	if prefix == "" {
		prefix = "一口价"
	}
	redPacket := serviceReq.QuotationInfo.RedPacket
	return &proto.PEstimateByOrderData{
		FeePrefix:   prefix,
		EstimateFee: util.Float64Add(util.String2float64(ctx, serviceReq.OrderInfo.CapPrice), redPacket),
		FeeType:     int32(consts.FeeTypeCapPrice),
	}, nil
}

func RenderByCache(ctx context.Context, serviceReq *model.ServiceRequest) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	if serviceReq.CacheInfo != nil {
		resp := &proto.PEstimateByOrderData{}
		if serviceReq.CacheInfo.EstimateID != nil {
			resp.EstimateId = *serviceReq.CacheInfo.EstimateID
			resp.FeeDetailUrl = getFeeDetailFromCacheInfo(ctx, serviceReq.CacheInfo)
		}

		if serviceReq.CacheInfo.EstimateFee != nil {
			resp.EstimateFee = *serviceReq.CacheInfo.EstimateFee
		}

		if serviceReq.CacheInfo.FeePrefix != nil {
			resp.FeePrefix = *serviceReq.CacheInfo.FeePrefix
		}

		if serviceReq.CacheInfo.FeeType != nil {
			resp.FeeType = *serviceReq.CacheInfo.FeeType
		}

		resp.DiscountMsg = serviceReq.CacheInfo.DiscountMsg

		return resp, nil
	}

	return nil, NewErrors.NewBizError(errors.New("service request is nil"), NewErrors.ErrnoSystemError)
}

// getFeeDetailFromCacheInfo 超值达/远必省 走特殊的逻辑
func getFeeDetailFromCacheInfo(ctx context.Context, cacheInfo *model.CacheInfo) string {
	if cacheInfo.FeeDetailOriginEID == nil {
		return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), *cacheInfo.EstimateID)
	}

	if cacheInfo.LevelType == nil {
		return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), *cacheInfo.EstimateID)
	}

	if isWellWorthPriceBox(*cacheInfo.LevelType) {
		return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), *cacheInfo.FeeDetailOriginEID)
	}

	if isFarMustCheaperBox(*cacheInfo.LevelType) {
		return fmt.Sprintf("%s?source=far_must_cheaper_box&estimate_ids=%s", fee_detail_info.GetFeeDetail4Box(ctx), *cacheInfo.FeeDetailOriginEID)
	}

	return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), *cacheInfo.EstimateID)
}

// getFeeDetailUrlFromEstimateQuotation ...
func getFeeDetailUrlFromEstimateQuotation(ctx context.Context, quotation *biz_runtime.QuotationV2) string {
	if isWellWorthPriceBox(quotation.GetLevelType()) && quotation.FeeDetailOriginEid != nil {
		return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), *quotation.FeeDetailOriginEid)
	}

	if isFarMustCheaperBox(quotation.GetLevelType()) && quotation.FeeDetailOriginEid != nil {
		return fmt.Sprintf("%s?source=far_must_cheaper_box&estimate_ids=%s", fee_detail_info.GetFeeDetail4Box(ctx), *quotation.FeeDetailOriginEid)
	}

	return fmt.Sprintf("%s?estimate_id=%s", fee_detail_info.GetDetailUrl(ctx), quotation.EstimateId)
}

// isWellWorthPriceBox 超值达
func isWellWorthPriceBox(levelType int32) bool {
	return levelType == level_type.WellWorthPriceLevelType
}

// isFarMustCheaperBox 远必省
func isFarMustCheaperBox(levelType int32) bool {
	return levelType == level_type.FarMustCheaperLevelType
}

func RenderByTripCloud(ctx context.Context, serviceReq *model.ServiceRequest, product []*biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	if serviceReq == nil || serviceReq.QuotationInfo == nil {
		return nil, NewErrors.NewBizError(errors.New("service request is nil"), NewErrors.ErrnoSystemError)
	}

	ad := adapter.NewAdapter(serviceReq)
	feeMsg, _, feeType := fee_info_render.GetFeeInfo(ctx, ad)
	prefix := fee_info_render.GetFeeMsgPrefix(feeMsg)
	quotation := serviceReq.QuotationInfo

	resp := &proto.PEstimateByOrderData{
		EstimateId:   quotation.EstimateId,
		EstimateFee:  quotation.EstimateFee,
		FeePrefix:    prefix,
		FeeType:      int32(feeType),
		FeeDetailUrl: getFeeDetailUrlFromEstimateQuotation(ctx, quotation),
	}

	if product != nil && len(product) > 0 && product[0] != nil {
		resp.DiscountMsg = buildDiscountMsg(ctx, serviceReq, product[0])
	}

	return resp, nil
}

func buildFeeInfo(ctx context.Context, serviceReq *model.ServiceRequest, product *biz_runtime.ProductInfoFull) (string, string, int) {
	feeMsg, feeAmount, feeType := fee_info_render.GetFeeInfo(ctx, product)

	feeMsg = fee_info_render.GetFeeMsgPrefix(feeMsg)
	if serviceReq != nil && serviceReq.IsUpgrade {
		feeMsg = dcmp.GetJSONContentWithPath(ctx, "common-upgrade_price_desc", nil, strconv.Itoa(serviceReq.UpgradeID))
		feeType = consts.FeeTypeUpgrade
	}

	if len(feeMsg) <= 0 {
		feeMsg = dcmp.GetJSONContentWithPath(ctx, "common-price_description", nil, "order_estimate_prefix_default")
	}

	return feeMsg, feeAmount, feeType
}
