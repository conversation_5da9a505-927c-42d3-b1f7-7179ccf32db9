package multi_estimate_by_order

import (
	"context"
	"fmt"
	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"testing"
)

func TestRenderByFavorable(t *testing.T) {
	req := &model.ServiceRequest{
		OrderInfo: &order_info.OrderInfo{
			OrderInfo: &sdk.OrderInfo{
				CapPrice: "12",
			},
		},
		QuotationInfo: &biz_runtime.QuotationV2{
			Quotation: &biz_runtime.Quotation{
				RedPacket: 3,
			},
		},
	}
	res, _ := RenderByFavorable(context.TODO(), req)
	fmt.Println(res)
}
