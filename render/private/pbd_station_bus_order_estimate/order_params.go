package pbd_station_bus_order_estimate_render

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_order_estimate/data"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// GetOrderParams ... 获取发单参数
func GetOrderParams(ctx context.Context, product *data.AdapterPbdOrderStationBus) *proto.PsoParams {
	if product == nil {
		return nil
	}
	//使用起终站点的位置信息
	fromAreaInfo := new(proto.PsoAreaInfo)
	fromStationInfo := product.GetFromStationInfo()
	fromAreaInfo.Lng = fromStationInfo.StationLng
	fromAreaInfo.Lat = fromStationInfo.StationLat
	fromAreaInfo.CityId = cast.ToInt64(fromStationInfo.City)
	fromAreaInfo.StationId = cast.ToInt64(fromStationInfo.StationId)

	toAreaInfo := new(proto.PsoAreaInfo)
	toStationInfo := product.GetDestStationInfo()
	toAreaInfo.Lng = toStationInfo.StationLng
	toAreaInfo.Lat = toStationInfo.StationLat
	toAreaInfo.CityId = cast.ToInt64(toStationInfo.City)
	toAreaInfo.StationId = cast.ToInt64(toStationInfo.StationId)
	//品类信息
	productParams := make([]OrderParamProduct, 0)
	productParam := new(OrderParamProduct)
	productParam.ProductCategory = product.GetProductCategory()
	productParam.ComboId = product.GetComboID()
	productParam.ComboType = product.GetComboType()
	productParam.RequireLevel = cast.ToInt64(product.GetRequireLevel())
	productParam.BusinessId = product.GetBusinessID()
	productParam.PageType = product.GetPageType()
	productParam.RouteType = cast.ToInt32(product.GetRouteType())
	carpoolSeatNum := product.GetBizInfo().CarpoolSeatNum
	productParam.CarpoolSeatNum = &carpoolSeatNum
	productParams = append(productParams, *productParam)
	productParamBytes, err := json.Marshal(productParams)
	if err != nil {
		log.Trace.Warnf(ctx, "pbd station bus GetOrderParams err||%v", err.Error())
	}
	return &proto.PsoParams{
		RouteId:           product.GetComboID(),
		BusServiceShiftId: product.GetShiftID(),
		PoolSeat:          cast.ToInt64(carpoolSeatNum),
		Origin:            fromAreaInfo,
		Destination:       toAreaInfo,
		EstimateId:        product.GetEstimateID(),
		DepartureTime:     product.GetDepartureTime(),
		Product:           string(productParamBytes),
	}
}

type OrderParamProduct struct {
	ProductCategory int64  `json:"product_category" `
	ComboId         int64  `json:"combo_id" `
	ComboType       int64  `json:"combo_type" `
	RequireLevel    int64  `json:"require_level" `
	BusinessId      int64  `json:"business_id" `
	PageType        int32  `json:"page_type" `
	RouteType       int32  `json:"route_type" `
	CarpoolSeatNum  *int32 `json:"carpool_seat_num,omitempty"`
}
