package alert_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

type alertInfo struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

func NewAlertInfo(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *alertInfo {
	return &alertInfo{
		product: product,
		text:    text,
	}
}

func (a *alertInfo) Render(ctx context.Context) *proto.AlertInfo {

	return &proto.AlertInfo{
		Title:       a.text["title"].String(),
		Description: a.text["description"].String(),
		LeftButton:  a.text["left_button"].String(),
		RightButton: a.text["right_button"].String(),
	}
}
