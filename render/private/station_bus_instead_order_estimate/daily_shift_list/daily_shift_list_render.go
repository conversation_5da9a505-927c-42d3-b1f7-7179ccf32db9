package daily_shift_list

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type dailyShiftList struct {
	targetProduct *biz_runtime.ProductInfoFull   //选定的班次
	productsFull  []*biz_runtime.ProductInfoFull //所有班次
}

// NewHeadCard ...
func NewDailyShiftList(productsFull []*biz_runtime.ProductInfoFull) *dailyShiftList {
	return &dailyShiftList{
		productsFull: productsFull,
	}
}

func (d *dailyShiftList) Render(ctx context.Context) []*proto.DailyBusShiftList {
	return d.getDailyShiftList(ctx)
}

func (d *dailyShiftList) getDailyShiftList(ctx context.Context) []*proto.DailyBusShiftList {
	if d.productsFull == nil || len(d.productsFull) <= 0 {
		return nil
	}
	allList := []*proto.DailyBusShiftList{}
	dayTimeStart := int32(util.GetNowEarlyTimeStamp())
	dayTimeEnd := int32(util.GetNowLastTimeStamp())
	index := int(0)
	for i := 0; i < 14; i++ {
		time0 := int64(dayTimeStart + int32(i*24*60*60))
		time24 := int64(dayTimeEnd + int32(i*24*60*60))
		dailyList := &proto.DailyBusShiftList{
			DayTime:   int32(time0),
			ShiftList: []*proto.ShiftList{},
		}
		shiftList := []*proto.ShiftList{}
		for ; index < len(d.productsFull); index++ {
			product := d.productsFull[index]
			if product == nil ||
				product.BaseReqData == nil ||
				product.Product == nil || product.Product.BizInfo == nil ||
				product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil ||
				product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo == nil {
				continue
			}
			biz := product.Product.BizInfo
			if time0 > biz.DepartureTime || biz.DepartureTime >= time24 {
				break
			}
			shift := &proto.ShiftList{
				ShiftId:                biz.StationInventoryInfo.SelectInfo.ShiftID,
				DepartureTime:          biz.DepartureTime,
				RemainSeats:            biz.MaxCarpoolSeatNum,
				CarryChildrenInventory: biz.StationInventoryInfo.SelectInfo.CarryChildrenMaxInventory,
				IsNeedVerified:         biz.IntercityData.RouteDetailV2.IsNeedVerified(),
			}
			for _, rule := range product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules {
				if rule.RuleName == seat_selection_consts.ChildTicket && rule.IsSupport == 1 {
					shift.SupportChild = true
				}
				if rule.RuleName == seat_selection_consts.CarryChildTicket && rule.IsSupport == 1 {
					shift.SupportCarryChild = true
				}
				if rule.RuleName == seat_selection_consts.CarryChildTicket && rule.OccupySeat == 1 {
					shift.ChildOccupySeat = true
				}
				if rule.RuleName == seat_selection_consts.CouponTicket && rule.IsSupport == 1 {
					shift.SupportCouponTicket = true
				}
			}
			if biz.StationInventoryInfo != nil && biz.StationInventoryInfo.ChildOccupySeat != nil {
				shift.ChildOccupySeat = *biz.StationInventoryInfo.ChildOccupySeat
			}
			shiftList = append(shiftList, shift)
		}
		dailyList.ShiftList = shiftList
		allList = append(allList, dailyList)
	}
	return allList
}
