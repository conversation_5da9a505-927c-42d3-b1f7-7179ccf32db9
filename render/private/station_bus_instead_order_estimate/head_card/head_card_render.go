package head_card

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	Default = "default"
)

type headCard struct {
	targetProduct *biz_runtime.ProductInfoFull   //选定的班次
	productsFull  []*biz_runtime.ProductInfoFull //所有班次
	text          map[string]gjson.Result
}

// NewHeadCard ...
func NewHeadCard(product *biz_runtime.ProductInfoFull, productsFull []*biz_runtime.ProductInfoFull, text map[string]gjson.Result) *headCard {
	return &headCard{
		targetProduct: product,
		productsFull:  productsFull,
		text:          text,
	}
}

func (hc *headCard) Render(ctx context.Context) *proto.BusHeadCard {
	return hc.getHeadCard(ctx)
}

// GetHeadCard ...
func (hc *headCard) getHeadCard(ctx context.Context) *proto.BusHeadCard {
	if hc.text == nil {
		return nil
	}
	businessName, icon := car_info.GetBusinessNameAndIcon(ctx, hc.targetProduct)
	title, backgroundImg := hc.GetBaseInfo(ctx, businessName)
	stationInfo := hc.buildStationInfo(ctx)
	if stationInfo == nil {
		return nil
	}
	return &proto.BusHeadCard{
		Icon:          icon,
		Title:         title,
		BackgroundImg: backgroundImg,
		StationInfo:   stationInfo,
	}
}

// buildStationInfo ...
func (hc *headCard) buildStationInfo(ctx context.Context) *proto.BusStationFullInfo {
	if hc.text == nil || hc.targetProduct == nil || hc.targetProduct.GetBizInfo() == nil || hc.targetProduct.BaseReqData == nil {
		return nil
	}

	var (
		startStationID int64
		endStationID   int64
		startIndex     int
		endIndex       int
		startStation   *Prfs.StationInfo
		endStation     *Prfs.StationInfo
	)

	startStationID = hc.targetProduct.BaseReqData.CommonBizInfo.StartStationId
	endStationID = hc.targetProduct.BaseReqData.CommonBizInfo.EndStationId

	routeDetail := hc.targetProduct.GetBizInfo().RouteDetailV2
	if routeDetail.RouteBasicInfo == nil || len(routeDetail.RouteBasicInfo.StationList) <= 0 {
		return nil
	}

	stationList := routeDetail.RouteBasicInfo.StationList
	for index, stationItem := range stationList {
		if stationItem.StationId == int32(startStationID) {
			startStation = stationItem
			startIndex = index
		}

		if stationItem.StationId == int32(endStationID) {
			endStation = stationItem
			endIndex = index
		}
	}

	if startIndex > endIndex {
		log.Trace.Warnf(ctx, "buildStationInfo", "start index > end index")
		return nil
	}
	if startStation == nil || endStation == nil {
		return nil
	}

	return &proto.BusStationFullInfo{
		RecentBusServiceShiftId: hc.buildRecentShiftId(ctx),
		StartStation:            hc.buildSingleStation(ctx, startStation),
		EndStation:              hc.buildSingleStation(ctx, endStation),
	}
}

// buildMiddleData ...
func (hc *headCard) buildRecentShiftId(ctx context.Context) *proto.RecentBusServiceShiftId {
	if hc.text == nil {
		return nil
	}
	return &proto.RecentBusServiceShiftId{
		Icon: hc.text["icon"].String(),
		Title: util.ReplaceTag(ctx, hc.text["recent_bus_service_shift_id"].Map()["title"].String(), map[string]string{
			"num": time.Unix(hc.targetProduct.Product.BizInfo.DepartureTime, 0).Format("15:04"),
		}),
		RightTitle: util.ReplaceTag(ctx, hc.text["recent_bus_service_shift_id"].Map()["right_title"].String(), map[string]string{
			"num": cast.ToString(hc.targetProduct.Product.BizInfo.MaxCarpoolSeatNum),
		}),
		PopupDetail: hc.buildPopupDetail(ctx),
	}
}

// buildPopupDetail ...
func (hc *headCard) buildPopupDetail(ctx context.Context) *proto.BusPopupDetail {

	var (
		resStationList = make([]*proto.ShiftIdItem, 0)
	)

	for _, product := range hc.productsFull {
		if product == nil || product.Product == nil {
			continue
		}
		resStationList = append(resStationList, hc.buildStationItem(ctx, product))
	}
	if len(resStationList) <= 0 {
		return nil
	}
	return &proto.BusPopupDetail{
		Title:                 hc.text["recent_bus_service_shift_id"].Map()["popup_data"].Map()["title"].String(),
		ButtonText:            hc.text["recent_bus_service_shift_id"].Map()["popup_data"].Map()["button_text"].String(),
		BusServiceShiftIdList: resStationList,
	}
}

// buildSingleStation ...
func (hc *headCard) buildSingleStation(ctx context.Context, station *Prfs.StationInfo) *proto.BusStationDataInfo {
	return &proto.BusStationDataInfo{
		DisplayName: station.StationName,
		Lat:         util.Float64Ptr(util.String2float64(ctx, station.StationLat)),
		Lng:         util.Float64Ptr(util.String2float64(ctx, station.StationLng)),
	}
}

// buildStationItem ...
func (hc *headCard) buildStationItem(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.ShiftIdItem {
	if hc.text == nil {
		return nil
	}
	isSelected := 0
	//找到目标品类，设置为勾选状态
	if product.GetEstimateID() == hc.targetProduct.GetEstimateID() {
		isSelected = 1
	}
	resStationItem := &proto.ShiftIdItem{
		DepartureMsg: util.ReplaceTag(ctx, hc.text["recent_bus_service_shift_id"].Map()["popup_data"].Map()["departure_msg"].String(), map[string]string{
			"time": time.Unix(product.Product.BizInfo.DepartureTime, 0).Format("15:04"),
		}),
		InventoryMsg: util.ReplaceTag(ctx, hc.text["recent_bus_service_shift_id"].Map()["popup_data"].Map()["inventory_msg"].String(), map[string]string{
			"num": cast.ToString(product.Product.BizInfo.MaxCarpoolSeatNum),
		}),
		IsSelected:   int32(isSelected),
		ShiftId:      product.Product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID,
		InventoryNum: product.Product.BizInfo.MaxCarpoolSeatNum,
	}

	return resStationItem
}

// GetBaseInfo ...
func (hc *headCard) GetBaseInfo(ctx context.Context, businessName string) (title string, backgroundImg string) {
	if hc.targetProduct == nil {
		return "", ""
	}

	baseInfo := dcmp.GetJSONMap(ctx, "intercity_estimate-base_info", strconv.Itoa(int(hc.targetProduct.GetProductCategory())))
	if len(baseInfo) == 0 {
		baseInfo = dcmp.GetJSONMap(ctx, "intercity_estimate-base_info", Default)
		title = dcmp.TranslateTemplate(baseInfo["title"].String(), map[string]string{"product_name": businessName})
	} else {
		title = baseInfo["title"].String()
	}
	return title, baseInfo["background_img"].String()
}
