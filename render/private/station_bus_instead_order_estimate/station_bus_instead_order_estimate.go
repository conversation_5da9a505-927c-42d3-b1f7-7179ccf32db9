package station_bus_instead_order_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/alert_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/bottom_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/daily_shift_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/head_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/order_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/rule_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/station_bus_instead_order_estimate/seat_info_card"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/tidwall/gjson"
)

const (
	HeadCard       = "head_card"
	SeatInfoCard   = "seat_info_card"
	RuleCard       = "rule_card"
	DailyShiftList = "daily_shift_list"
	AlertInfo      = "alert_info"
)

type render struct {
}

// NewRender ...
func NewRender() *render {
	return &render{}
}

// Do ...
func (r *render) Do(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull) (data *proto.StationBusInsteadOrderEstimateData, e BizError.BizError) {
	text := r.getText(ctx)
	data = &proto.StationBusInsteadOrderEstimateData{
		EstimateTraceId: trace.GetTrace(ctx).GetTraceId(),
	}
	product := r.getTargetProduct(ctx, productsFull)
	if product == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "bus station  instead order estimate target product is null")
		return nil, BizError.ErrGetShiftEmpty
	}
	//prfs、brick的携童是否占座是强依赖，故若为空，则直接阻断
	if product.GetCommonBizInfo().IdentityPageInfo == nil {
		return nil, BizError.ErrGetTicketRuleEmpty
	}
	//乘车人无须渲染头部、座位数限制、规则信息。只渲染后边的品类价格信息和发单参数即可
	if product.GetCommonBizInfo().SceneType != consts.SceneTypeChangePassenger {
		if product.BaseReqData.CommonInfo.Channel == consts.WebTicketCollector {
			data.Mode = r.buildMode(ctx, product)
			data.HeadCard = head_card.NewHeadCard(product, productsFull, gjson.Get(text, HeadCard).Map()).Render(ctx)
			if data.HeadCard == nil {
				return nil, BizError.ErrGetShiftEmpty
			}
			data.SeatInfoCard = seat_info_card.NewSeatInfoCard(product, gjson.Get(text, SeatInfoCard).Map()).Render(ctx)
			data.RuleCard = rule_card.NewRuleCard(product, gjson.Get(text, RuleCard).Map()).Render(ctx)
		}
		if product.BaseReqData.CommonInfo.Channel == consts.WebPhoneCallOrder {
			data.DailyShiftList = daily_shift_list.NewDailyShiftList(productsFull).Render(ctx)
		}
	}
	data.BottomCard = bottom_card.NewBottomCard(product, gjson.Get(text, intercity_estimate_detail.BottomCard).Map()).Render(ctx)
	data.OrderParams = order_params.NewOrderParams(product, nil).Render(ctx)
	data.AlertInfo = alert_info.NewAlertInfo(product, gjson.Get(text, AlertInfo).Map()).Render(ctx)
	return data, nil
}

// getTargetProduct 乘车人变动，使用报价单的品类信息即可，非乘车人变动获取渲染的目标品类,优先级：用户选择班次>最近班次
func (r *render) getTargetProduct(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull) *biz_runtime.ProductInfoFull {
	busServiceShiftId := ""
	sceneType := int32(0)
	for _, product := range productsFull {
		if product == nil {
			continue
		}
		if product.GetCommonBizInfo().BusServiceShiftId != "" {
			busServiceShiftId = product.GetCommonBizInfo().BusServiceShiftId
		}
		sceneType = product.GetCommonBizInfo().SceneType
	}
	//1、乘车人变动，直接使用通过报价单获取的product即可
	if sceneType == consts.SceneTypeChangePassenger {
		for _, product := range productsFull {
			if product == nil {
				continue
			}
			return product
		}
	} else { //2、非乘车人变动
		//2.1、用户选择的班次
		if busServiceShiftId != "" {
			for _, product := range productsFull {
				if product == nil {
					continue
				}
				if product.GetPrivateBizInfo().StationInventoryInfo != nil && busServiceShiftId == product.GetPrivateBizInfo().StationInventoryInfo.SelectInfo.ShiftID {
					return product
				}
			}
		} else {
			//2.2、最近库存>0的班次
			for _, product := range productsFull {
				//库存>0
				if product != nil && product.GetPrivateBizInfo().MaxCarpoolSeatNum > 0 {
					return product
				}
			}
		}
	}
	return nil
}

// buildMode 模式
func (r *render) buildMode(ctx context.Context, product *biz_runtime.ProductInfoFull) int32 {
	if product == nil || product.GetBizInfo() == nil || product.GetBizInfo().RouteDetailV2 == nil {
		return models.Unknown.ToInt32()
	}

	// 需要实名制
	if product.GetBizInfo().RouteDetailV2.IsNeedVerified() {
		return models.RealName.ToInt32()
	} else {
		return models.NoRealName.ToInt32()
	}
}

// getText 获取全局文案
func (r *render) getText(ctx context.Context) string {
	raw := dcmp.GetDcmpContent(ctx, "intercity_estimate-instead_order", nil)

	return raw
}
