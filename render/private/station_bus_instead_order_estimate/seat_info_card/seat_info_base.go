package seat_info_card

import (
	"context"
	"github.com/shopspring/decimal"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"github.com/tidwall/gjson"
)

type seatInfoBase struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result

	supportTicketTypes map[int32]bool
}

func newSeatInfoBase(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *seatInfoBase {
	return &seatInfoBase{
		product: product,
		text:    text,
	}
}

// buildSupportTicketType 获取当前路线支持的票类型
func (sib *seatInfoBase) buildSupportTicketType(ctx context.Context) {
	supportTicketTypes := make(map[int32]bool)

	// 兜底成人
	supportTicketTypes[seat_selection_consts.Adult.ToInt32()] = true

	if sib.product == nil || sib.product.BaseReqData == nil || sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		sib.supportTicketTypes = supportTicketTypes
		return
	}

	if sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo != nil {
		ruleInfo := sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo
		if ruleInfo != nil && len(ruleInfo.SpecialSeatRules) > 0 {
			for _, rules := range ruleInfo.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.ChildTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.Children.ToInt32()] = true

					// 兼容老携童票逻辑
					for _, childTicket := range rules.GetTypePercent() {
						if childTicket.GetType() == seat_selection_consts.CarryChildren.ToInt64() {
							supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()] = true
							break
						}
					}
				}

				// 携童票新逻辑
				if rules.RuleName == seat_selection_consts.CarryChildTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()] = true
				}
				// 是否支持优待票
				if rules.RuleName == seat_selection_consts.CouponTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.PreferentialPeople.ToInt32()] = true
				}
			}
		}
	}

	sib.supportTicketTypes = supportTicketTypes
}

// getBusPassengerInfo ...
func (sic *seatInfoCard) getBusPassengerInfo(ctx context.Context) []*proto.BusPassengerInfo {
	ticketsInfo := sic.text["ticket_type"].Map()

	resSeatDetail := make([]*proto.BusPassengerInfo, 0)

	for _, ticketType := range sic.text["ticket_rank"].Array() {
		if _, support := sic.supportTicketTypes[int32(ticketType.Int())]; support {
			resSeatDetail = append(resSeatDetail, &proto.BusPassengerInfo{
				Type:       int32(ticketType.Int()),
				Title:      ticketsInfo[util.Int642String(ticketType.Int())].Map()["title"].String(),
				Subtitle:   ticketsInfo[util.Int642String(ticketType.Int())].Map()["sub_title"].String(),
				Label:      sic.buildLabel(ctx, ticketType.Int()),
				IconClass:  ticketsInfo[util.Int642String(ticketType.Int())].Map()["iconClass"].String(),
				BubbleText: util.String2PtrString(ticketsInfo[util.Int642String(ticketType.Int())].Map()["bubble_text"].String()),
			})
		}
	}

	return resSeatDetail
}

// buildSubTitleList ...
func (sic *seatInfoCard) buildLabel(ctx context.Context, ticketType int64) string {
	if sic.text == nil {
		return ""
	}

	if seatRule, exist := sic.ruleInfoMap[ticketType]; exist && seatRule != nil {
		percent := float64(seatRule.Percent) / 10
		var config map[string]gjson.Result

		if sic.text["label"].Map()[util.Int642String(seatRule.Percent)].Exists() {
			config = sic.text["label"].Map()[util.Int642String(seatRule.Percent)].Map()
		} else {
			config = sic.text["label"].Map()["default"].Map()
		}
		return util.ReplaceTag(ctx, config["content"].String(), map[string]string{
			"num": decimal.NewFromFloat(percent).Round(1).String(),
		})
	}

	return ""
}

func (sic *seatInfoCard) buildRealNameCarryChildrenTitle(ctx context.Context) string {
	if sic.text == nil {
		return ""
	}
	if _, support := sic.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; !support {
		return ""
	}

	//支持携童票下，携童不占座并且携童库存为0，则展示提醒文案
	if sic.product.GetBizInfo().CarryChildrenIsOccupySeat == 0 && sic.product.GetBizInfo().StationInventoryInfo != nil && sic.product.GetBizInfo().StationInventoryInfo.SelectInfo.CarryChildrenMaxInventory <= 0 {
		return sic.text["real_name_carry_children_title"].Map()["no_inventory"].String()
	}
	//命中儿童票优惠，展示优惠文案
	if seatRule, exist := sic.ruleInfoMap[seat_selection_consts.CarryChildren.ToInt64()]; exist && seatRule != nil {
		if seatRule.Percent == 0 {
			return sic.text["real_name_carry_children_title"].Map()["free"].String()
		}
	}
	//默认文案：需携带儿童
	return sic.text["real_name_carry_children_title"].Map()["default"].String()
}
