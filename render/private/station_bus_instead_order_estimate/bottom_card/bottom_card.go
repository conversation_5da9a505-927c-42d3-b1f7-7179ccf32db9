package bottom_card

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	intercity_sku2 "git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
)

type bottomCard struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

// NewBottomCard ...
func NewBottomCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *bottomCard {
	return &bottomCard{
		product: product,
		text:    text,
	}
}

func (bc *bottomCard) Render(ctx context.Context) *proto.BusBottomCard {
	return bc.getBottomCard(ctx)
}

// getBottomCard ...
func (bc *bottomCard) getBottomCard(ctx context.Context) *proto.BusBottomCard {
	if bc.text == nil {
		return nil
	}

	if bc.product == nil || bc.product.BaseReqData == nil {
		return nil
	}

	return &proto.BusBottomCard{
		EstimateInfo: bc.buildEstimateInfo(ctx),
	}
}

// buildEstimateInfo ...
func (bc *bottomCard) buildEstimateInfo(ctx context.Context) *proto.BusEstimateInfo {
	if bc.text == nil || bc.product == nil || bc.product.BaseReqData == nil {
		return nil
	}

	disable, disableToast := bc.buildBottomCardDisable(ctx)
	if disable == intercity_estimate_detail.Ban {
		return &proto.BusEstimateInfo{
			EstimateId:        bc.product.GetEstimateID(),
			FeeMsg:            bc.text["ban_fee_msg"].String(),
			ConfirmButtonText: bc.text["confirm_button"].Map()["text"].String(),
			Disable:           disable,
			DisableToast:      disableToast,
		}
	}
	return &proto.BusEstimateInfo{
		EstimateId:  bc.product.GetEstimateID(),
		FeeAmount:   decimal.NewFromFloat(bc.product.GetEstimateFee()).Round(2).String(),
		FeeMsg:      fee_info_render.FeeMsgV2(ctx, bc.product),
		FeeDescList: intercity_sku2.FeeDescList(ctx, bc.product, bc.product),
		FeeDetailUrl: dcmp.GetDcmpContent(ctx, "intercity_station-fee_detail_url_v4", map[string]string{
			"city_id": strconv.Itoa(int(bc.product.BaseReqData.AreaInfo.Area)),
		}),
		ExtraMap:          bc.buildExtraMap(ctx),
		ConfirmButtonText: bc.text["confirm_button"].Map()["text"].String(),
	}
}

// buildExtraMap ...
func (bc *bottomCard) buildExtraMap(ctx context.Context) *proto.IntercityNewOrderParam {
	if bc.product == nil || bc.product.GetBizInfo() == nil {
		return nil
	}

	return &proto.IntercityNewOrderParam{
		ProductCategory: bc.product.GetProductCategory(),
		ComboType:       bc.product.GetComboType(),
		ComboId:         bc.product.GetBizInfo().ComboID,
		RequireLevel:    bc.product.Product.RequireLevelInt,
		BusinessId:      bc.product.GetBusinessID(),
		PageType:        bc.product.GetPageType(),
		RouteType:       int32(bc.product.GetRouteType()),
		CarpoolSeatNum:  &bc.product.GetBizInfo().CarpoolSeatNum,
	}
}

// buildBottomCardDisable ...
func (bc *bottomCard) buildBottomCardDisable(ctx context.Context) (int32, *string) {
	if bc.product.BaseReqData.CommonInfo.Channel == consts.WebPhoneCallOrder {
		return intercity_estimate_detail.NotBan, nil
	}
	if bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo == nil {
		return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_passenger_info_toast"].String())
	}

	if bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo != nil && len(bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
		return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_check_people_toast"].String())
	}
	//用端上传递的人数入参做校验
	noCarryChildrenNum := int32(0)
	carryChildrenNum := int32(0)
	for _, info := range bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo.PassengerList {
		if info.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
			carryChildrenNum += info.PassengerCount
		} else {
			noCarryChildrenNum += info.PassengerCount
		}
	}
	//人数为空不能发单
	if noCarryChildrenNum == 0 && carryChildrenNum == 0 {
		return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_people_toast"].String())
	}
	//携童不能单独发单
	if noCarryChildrenNum == 0 && carryChildrenNum > 0 {
		return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_only_carry_children_toast"].String())
	}
	return intercity_estimate_detail.NotBan, nil
}
