package get_intercity_basic_fee_lite

import (
	"context"
	"encoding/json"

	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_intercity_basic_fee_lite/data"
	"github.com/spf13/cast"
)

func GenerateFeeDesc(ctx context.Context, adapter *data.IntercityBasicFeeLiteAdapter) *proto.IntercityBasicFeeLiteData {
	var feeDesc string
	var feeAmount float64

	var dcmpData map[string]string
	dcmpConf := dcmp.GetDcmpContent(ctx, "intercity_station-basic_fee_msg", nil)
	err := json.Unmarshal([]byte(dcmpConf), &dcmpData)

	if err != nil {
		return nil
	}

	if adapter.GetPriceType() == DetailConst.UnavaliablePrice { // 未配置定价
		feeAmount = -1.0
		feeDesc = dcmpData["default_msg"]
	} else if adapter.GetPriceType() == DetailConst.CapPrice { // 是否是一口价
		feeDesc, feeAmount = handleCapPrice(ctx, adapter, dcmpData)

	} else if adapter.GetPriceType() == DetailConst.TieredPrice { // 是否是阶梯定价
		feeDesc, feeAmount = handleTieredPrice(ctx, adapter, dcmpData)
	}

	return &proto.IntercityBasicFeeLiteData{
		FeeMsg:    feeDesc,
		FeeAmount: feeAmount,
	}
}
func handleCapPrice(ctx context.Context, adapter *data.IntercityBasicFeeLiteAdapter, dcmpData map[string]string) (string, float64) {
	feeAmount := adapter.GetBasicPrice()
	startStationId := adapter.GetStartStationId()

	var feeDesc string
	if startStationId != 0 {
		content := dcmpData["cap_price_show"]
		feeDesc = util.ReplaceTag(ctx, content, map[string]string{
			"fee": cast.ToString(feeAmount),
		})
	} else {
		feeDesc = dcmpData["cap_price_msg"]
	}

	return feeDesc, feeAmount
}

func handleTieredPrice(ctx context.Context, adapter *data.IntercityBasicFeeLiteAdapter, dcmpData map[string]string) (string, float64) {
	var feeDesc string
	var feeAmount float64

	startStationId := adapter.GetStartStationId()
	endStationId := adapter.GetEndStationId()
	stationElementList := adapter.GetStationElementList()

	if endStationId != 0 && startStationId != 0 {

		price := findPriceForStations(startStationId, endStationId, stationElementList)
		if price != nil {
			feeAmount = *price
			content := dcmpData["tiered_price_show"]
			feeDesc = util.ReplaceTag(ctx, content, map[string]string{
				"fee": cast.ToString(feeAmount),
			})
		} else {
			feeAmount = -1.0
			feeDesc = dcmpData["default_msg"] // 外层兜底一致
		}
	} else {
		feeAmount = 0.0
		feeDesc = dcmpData["tiered_price_msg"]
	}

	return feeDesc, feeAmount
}

func findPriceForStations(from, to int64, stationElementList []*data.StationElement) *float64 {
	for _, item := range stationElementList {
		if item.From == from && item.To == to {
			return &item.Price
		}
	}
	return nil
}
