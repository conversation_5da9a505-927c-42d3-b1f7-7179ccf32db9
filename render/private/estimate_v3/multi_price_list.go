package estimate_v3

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	util3 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	util2 "git.xiaojukeji.com/intercity/biz-api/intercity-common-go/util"
	"github.com/spf13/cast"
	"math"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	Apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"

	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
)

type MultiPriceProvider interface {
	fee_info_render.PriceInfoProvider
	IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool
	GetOpenCitySourceId() int32
}

// GetMultiPriceList 多价格展示 主要服务于拼车两口价同时展示 拼成/未拼成 两个价格信息 价格描述部分全在 feeDescList 中展示;
func GetMultiPriceList(ctx context.Context, prov MultiPriceProvider) []*proto.V3MultiPrice {
	var (
		multiPriceList = make([]*proto.V3MultiPrice, 0)
		succItem       = &proto.V3MultiPrice{}
		failItem       = &proto.V3MultiPrice{}
		succPrice      float64
		failPrice      float64
	)

	if !carpool.IsCarpoolDualPriceFull(prov) {
		return multiPriceList
	}

	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		// 当按照一口价样式显示时, 不再渲染multi_price_desc字段
		return multiPriceList
	}

	failEstimatePrice, ok := prov.GetCarpoolFailEstimateFee()
	if !ok {
		return multiPriceList
	}

	key, _ := prov.ApolloParamsGen(apollo_model.WithPIDKey, apollo_model.WithProductCategory)
	succPrice = carpool3.FormatPrice(prov.GetEstimateFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), key, util.RoundAbs(math.Ceil(prov.GetEstimateFee()*10)/10, 1)).FloatVal
	failPrice = carpool3.FormatPrice(failEstimatePrice, carpool3.PageDefault, util.ToString(prov.GetCityID()), key, util.RoundAbs(math.Ceil(failEstimatePrice*10)/10, 1)).FloatVal
	price.CheckSingle(ctx, "GetMultiPriceList", "carpool_multi_price_list_success", "succPrice", succPrice, price.WithCheckBelowZero())
	price.CheckSingle(ctx, "GetMultiPriceList", "carpool_multi_price_list_fail", "failPrice", failPrice, price.WithCheckBelowZero())

	// 两个价格, 不能根据两个价格抵扣算
	if prov.IsBusinessPay() {
		succPrice = 0.0
		failPrice = 0.0
	}

	succFeeMsg := fee_info_render.RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "carpool_two_price.succ", util.FormatPriceWithoutZero(succPrice, 1))
	failFeeMsg := fee_info_render.RenderFeeMsg(ctx, "estimate_form_v3-estimate_price_msg", "carpool_two_price.fail", util.FormatPriceWithoutZero(failPrice, 1))
	succFeeMsgTemplate := dcmp.GetJSONResultWithPath(ctx, "estimate_form_v3_new-multi_price_list", nil, "fee_msg_template").Get("carpool_two_price.succ").String()
	failFeeMsgTemplate := dcmp.GetJSONResultWithPath(ctx, "estimate_form_v3_new-multi_price_list", nil, "fee_msg_template").Get("carpool_two_price.fail").String()

	sucFontSize, failFontSize := DualPriceFontSize(ctx, prov)

	failItem = &proto.V3MultiPrice{
		FeeMsg:         failFeeMsg,
		FeeAmount:      failPrice,
		IsLargeFont:    consts2.CarpoolSmallFont,
		FontSize:       failFontSize,
		FeeMsgTemplate: failFeeMsgTemplate,
	}
	multiPriceList = append(multiPriceList, failItem)

	succItem = &proto.V3MultiPrice{
		FeeMsg:         succFeeMsg,
		FeeAmount:      succPrice,
		IsLargeFont:    consts2.CarpoolLargeFont,
		FontSize:       sucFontSize,
		FeeMsgTemplate: succFeeMsgTemplate,
	}
	multiPriceList = append(multiPriceList, succItem)

	// 极速拼车【拼成/未拼成】顺序实验
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation && len(multiPriceList) == 2 && common_logic.DualPriceOrderedOnAscV3(prov) {
		multiPriceList[0], multiPriceList[1] = multiPriceList[1], multiPriceList[0]
	}

	return multiPriceList
}

func DualPriceFontSize(ctx context.Context, prov apollo_model.ApolloParamsProvider) (int32, int32) {
	fontSizeCfg := dcmp.GetJSONMap(ctx, "estimate_form_v3-estimate_price_font_size", "dual_price_carpool")

	_, sucFontSize_, failFontSize_ := DualPriceFontSizeExp(ctx, prov)

	sucFontSize := util3.TernaryDelayExec(nil != sucFontSize_, func() interface{} { return *sucFontSize_ }, func() interface{} { return cast.ToInt32(fontSizeCfg["suc_font_size"].Int()) }).(int32)     //NOLINT
	failFontSize := util3.TernaryDelayExec(nil != failFontSize_, func() interface{} { return *failFontSize_ }, func() interface{} { return cast.ToInt32(fontSizeCfg["fail_font_size"].Int()) }).(int32) //NOLINT

	return sucFontSize, failFontSize
}

func DualPriceFontSizeExp(ctx context.Context, prov apollo_model.ApolloParamsProvider) (isHit bool, sucFontSize, failFontSize *int32) {
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)

	isHit, assignParams := Apollo2.GetParameters("carpool_pricestyle_cpo", key, param)

	if !isHit {
		return
	}

	if "" != assignParams["fail_font_size"] && "0" != assignParams["fail_font_size"] {
		failFontSize = util2.Int32Ptr(cast.ToInt32(assignParams["fail_font_size"]))
	}

	if "" != assignParams["suc_font_size"] && "0" != assignParams["suc_font_size"] {
		sucFontSize = util2.Int32Ptr(cast.ToInt32(assignParams["suc_font_size"]))
	}
	return
}
