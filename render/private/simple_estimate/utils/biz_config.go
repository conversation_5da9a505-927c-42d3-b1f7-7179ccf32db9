package utils

import (
	"encoding/json"
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/consts"
	"github.com/tidwall/gjson"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
)

func getBizConfigMap(name string, key string) (string, error) {
	return ApolloSDK.GetConfigItem("biz_config", name, key)
}

func GetConfigByProductIDAndPath(productID int64, key string) (gjson.Result, error) {
	configMap, err := getBizConfigMap("config_product_map", "product_map")
	if err != nil {
		return gjson.Result{}, err
	}

	path := fmt.Sprintf("%d.%s", productID, key)
	if result := gjson.Get(configMap, path); result.Exists() {
		return result, err
	}

	if result := gjson.Get(configMap, fmt.Sprintf("%d.%s", 0, key)); result.Exists() {
		return result, err
	}

	return gjson.Result{}, err
}

// IsUnTaxi 获取产品线是否属于unione 出租车
func IsUnTaxi(productID int64) bool {
	if result, err := GetConfigByProductIDAndPath(productID, consts.BelongsProductGroupFiled); err == nil && result.Exists() {
		productArr := []int64{}
		if err = json.Unmarshal([]byte(result.Raw), &productArr); err != nil {
			return false
		}

		if util.InArrayInt64(int64(consts.Unione), productArr) {
			return true
		}
	}

	return false
}

func GetCurrencyMap(currency string) (gjson.Result, error) {
	configMap, err := getBizConfigMap("currency", "currency")
	if err != nil {
		return gjson.Result{}, err
	}

	path := fmt.Sprintf("%s.%s", currency, "unit")
	if result := gjson.Get(configMap, path); result.Exists() {
		return result, err
	}

	if result := gjson.Get(configMap, fmt.Sprintf("%s.%s", "0", "unit")); result.Exists() {
		return result, err
	}

	return gjson.Result{}, err
}

func GetConfigH5URL(productID int64, configUrl string) (gjson.Result, error) {
	configMap, err := getBizConfigMap("config_h5_url", "h5_static_url")
	if err != nil {
		return gjson.Result{}, err
	}

	path := fmt.Sprintf("%d.%s", productID, configUrl)
	if result := gjson.Get(configMap, path); result.Exists() {
		return result, err
	}

	if result := gjson.Get(configMap, fmt.Sprintf("%d.%s", 0, configUrl)); result.Exists() {
		return result, err
	}

	return gjson.Result{}, err
}
