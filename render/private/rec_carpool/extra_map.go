package rec_carpool

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type ExtraMapProvider interface {
	GetNTuple() *biz_runtime.NTuple
	GetCountPriceType() int32
	GetPageType() int32
}

func ExtraMap(ctx context.Context, prov ExtraMapProvider) *proto.RecCarpoolExtraMap {
	requireLevel, _ := strconv.Atoi(prov.GetNTuple().RequireLevel)
	extraMap := &proto.RecCarpoolExtraMap{
		ProductId:       int32(prov.GetNTuple().ProductID),
		ProductCategory: int32(prov.GetNTuple().ProductCategory),
		BusinessId:      int32(prov.GetNTuple().BusinessID),
		ComboType:       int32(prov.GetNTuple().ComboType),
		RequireLevel:    int32(requireLevel),
		LevelType:       prov.GetNTuple().LevelType,
		CountPriceType:  prov.GetCountPriceType(),
		PageType:        prov.GetPageType(),
	}
	return extraMap
}
