package rec_carpool

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type UserPayInfoProvider interface {
	GetCurrentPaymentTypeWithBusiness() (int32, int32)
}

func UserPayInfo(ctx context.Context, prov UserPayInfoProvider) *proto.UserPayInfo {
	payInfo := &proto.UserPayInfo{
		PaymentId:        2,
		BusinessConstSet: 0,
	}
	payID, bussinessConstSet := prov.GetCurrentPaymentTypeWithBusiness()
	payInfo.PaymentId = int64(payID)
	payInfo.BusinessConstSet = int64(bussinessConstSet)
	return payInfo
}
