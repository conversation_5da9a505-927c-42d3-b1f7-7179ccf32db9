package rec_carpool

import (
	"context"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/ntuple"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type FeeProvider interface {
	IsTaxiPC() bool
	IsBusinessPay() bool
	GetEstimateFee() float64
	GetProductCategoryString() string
	TaxiNameApolloParam() *ApolloModel.User
	GetCityID() int
	GetLang() string
	GetBillInfoCurrency() string

	ntuple.Provider
}

func FeeMsg(ctx context.Context, prov FeeProvider) string {
	feeMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-fee_msg")
	generalTmp := util2.GetGeneralFeeMsgTemplate(ctx, int(prov.GetCityID()), int(prov.GetProductCategory()), prov.GetLang())
	currencyUnit, currencySymbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())

	// 出租车单独处理
	if prov.IsTaxiPC() {
		if !IsTaxiShowFee(ctx, prov) {
			// 不显示预估价
			return gjson.Get(feeMsgTemplate, "fee_msg."+prov.GetProductCategoryString()).String()
		}
		return dcmp.TranslateTemplate(
			gjson.Get(feeMsgTemplate, "common_fee_msg").String(),
			map[string]string{"fee": formatPrice(prov.GetEstimateFee())},
		)
	}
	// 企业支付
	if prov.IsBusinessPay() {
		feeMsg := dcmp.TranslateTemplate(
			gjson.Get(feeMsgTemplate, "business_fee_msg."+prov.GetProductCategoryString()).String(),
			map[string]string{"fee": formatPrice(0.0)},
		)
		if feeMsg != "" {
			return feeMsg
		}
		return dcmp.TranslateTemplate(
			gjson.Get(feeMsgTemplate, "business_common_fee_msg").String(),
			map[string]string{"fee": formatPrice(0.0)},
		)
	}
	// 快车一口价
	if order.IsCapFast(prov) {
		feeMsgTmp := gjson.Get(feeMsgTemplate, "cap_fee_msg").String()
		if generalTmp != "" {
			feeMsgTmp = generalTmp
		}
		return dcmp.TranslateTemplate(
			feeMsgTmp,
			map[string]string{
				"num":             formatPrice(prov.GetEstimateFee()),
				"fee":             formatPrice(prov.GetEstimateFee()),
				"currency_unit":   currencyUnit,
				"currency_symbol": currencySymbol,
			},
		)
	}
	feeMsgTmp := gjson.Get(feeMsgTemplate, "fee_msg."+prov.GetProductCategoryString()).String()
	if generalTmp != "" {
		feeMsgTmp = generalTmp
	}

	feeMsg := dcmp.TranslateTemplate(
		feeMsgTmp,
		map[string]string{
			"num":             formatPrice(prov.GetEstimateFee()),
			"fee":             formatPrice(prov.GetEstimateFee()),
			"currency_unit":   currencyUnit,
			"currency_symbol": currencySymbol,
		},
	)

	if feeMsg != "" {
		return feeMsg
	}

	feeMsgTmp = gjson.Get(feeMsgTemplate, "common_fee_msg").String()
	if generalTmp != "" {
		feeMsgTmp = generalTmp
	}
	return dcmp.TranslateTemplate(
		feeMsgTmp,
		map[string]string{
			"num":             formatPrice(prov.GetEstimateFee()),
			"fee":             formatPrice(prov.GetEstimateFee()),
			"currency_unit":   currencyUnit,
			"currency_symbol": currencySymbol,
		},
	)
}

func FeeAmount(ctx context.Context, prov FeeProvider) string {
	// 出租车单独处理
	if prov.IsTaxiPC() {
		if !IsTaxiShowFee(ctx, prov) {
			// 不显示预估价
			return "-1"
		}
		return formatPrice(prov.GetEstimateFee())
	}
	if prov.IsBusinessPay() {
		return "0"
	}
	return formatPrice(prov.GetEstimateFee())
}

func formatPrice(price float64) string {
	return strconv.FormatFloat(price, 'f', 1, 64)
}

func IsTaxiShowFee(ctx context.Context, prov FeeProvider) bool {
	toggle, err := ApolloSDK.FeatureToggle("estimate_form_unione_show_estimate_fee", prov.TaxiNameApolloParam()) // city, phone
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagApolloLoadErr, "ab estimate_form_unione_show_estimate_fee load error %s", err)
		return false
	}
	if !toggle.IsAllow() {
		return false
	}
	return true
}
