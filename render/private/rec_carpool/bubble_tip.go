package rec_carpool

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/tidwall/gjson"
)

type BubbleTipProvider interface {
	IsCarpool() bool
	IsBusinessPay() bool
	GetEstimateFee() float64
	IsCarpoolOneSeat() bool
}

func BubbleTip(ctx context.Context, prov BubbleTipProvider, minNonCarpoolEstimateFee float64) *string {
	if !(prov.IsCarpool() && prov.IsCarpoolOneSeat()) {
		return nil
	}
	if prov.IsBusinessPay() {
		return nil
	}
	if prov.GetEstimateFee() >= minNonCarpoolEstimateFee {
		return nil
	}
	// 比快车低时
	textTemplate := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-fee_msg")
	bubbleTip := dcmp.TranslateTemplate(gjson.Get(textTemplate, "fee_bubble_tip").String(), map[string]string{
		"fee": formatPrice(prov.GetEstimateFee()),
	})
	if bubbleTip == "" {
		return nil
	}
	return &bubbleTip

}
