package rec_carpool

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	//engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	// "strconv"
)

type FeeDetailInfoProvider interface {
	IsCarpool() bool
	GetBillFeeDetailInfo() map[string]float64
	IsUpdateFeeDescList() bool
	GetApolloParam() (string, map[string]string)
}

const (
	red_packet = "red_packet"
)

func ExtraPriceDesc(ctx context.Context, prov FeeDetailInfoProvider) string {
	//if prov.IsCarpool() {
	//	return ""
	//}
	if prov.IsUpdateFeeDescList() {
		return ""
	}
	feeDetailInfo := prov.GetBillFeeDetailInfo()
	key, param := prov.GetApolloParam()
	isShow := apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", key, param)
	if redPacket, ok := feeDetailInfo["red_packet"]; ok && redPacket > 0 {
		if !isShow {
			return ""
		}
		value := strconv.Itoa(int(redPacket))
		return dcmp.GetDcmpContent(ctx, "config_text-holiday_fee_msg", map[string]string{"num": value})
	}

	return ""
}

type FeeDescListProvider interface {
	IsCarpool() bool
	engine_model.FeeDescEngineInputProvider
	GetBonus() float64
	GetFastEstimatePrice() float64
	GetMixedDeductPrice() float64
	IsCarpoolV3Merge(context context.Context) bool
	IsUpdateFeeDescList() bool
	IsSelected() int32
	GetApolloParam() (string, map[string]string)
	GetDiscountCard() *PriceApi.DiscountCard
	GetCarpoolFailExactEstimateFee() float64
	GetExactEstimateFee() float64
	GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer
	GetComboID() int64
	IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool
	GetExactSeatNum() int32
	IsHaveCarpoolVCard(context.Context) bool // 判断是否使用省钱卡
	GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string)
	GetSceneDataBySceneMark(reqSceneMark map[string]string) *biz_runtime.SceneEstimateFee
	GetOpenCitySourceId() int32
	GetCurrentSeatNum() int32
	GetDirectEstimatePrice() biz_runtime.SceneEstimateFeeViewer
	GetCarpoolFailRawBill() *price_api.BillInfoV3
	GetCarpoolFailCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetBaseReqData() *models.BaseReqData
}

func FeeDescList(ctx context.Context, prov FeeDescListProvider, feeOutputMap map[int64][]*engine_model.FeeOutput, pageType int32) []*proto.NewFormFeeDesc {
	// 拼车不走渲染引擎
	if prov.IsCarpool() {
		if prov.IsUpdateFeeDescList() {
			return category_carpool.GetPriceInfoDescList(ctx, prov, pageType)
		}
		return nil

	}
	// 非拼车走引擎
	feeDescList := []*proto.NewFormFeeDesc{}
	//red_packet 展示加开关
	key, param := prov.GetApolloParam()
	isShow := apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", key, param)
	env := fee_desc_engine.NewEnv(consts.RecCarpoolOthersForm).SetCap(2).SetApolloParams(prov).SetDcmpKey(consts.RecCarpoolFeeDesc)
	feeOutput := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, prov, consts.RecCarpoolOthersForm), env).Do(ctx)
	descResult := make([]*engine_model.FeeOutput, 0, len(feeOutput))
	//如果未打开开关，过滤节假日服务费
	for _, feeItem := range feeOutput {
		if feeItem.Key == red_packet {
			if !isShow {
				continue
			}
		}

		descResult = append(descResult, feeItem)
	}

	if prov.IsSelected() == 1 && prov.IsUpdateFeeDescList() {
		feeOutputMap[prov.GetProductCategory()] = descResult
	}
	for _, fee := range descResult {
		if fee == nil {
			continue
		}

		feeDescList = append(feeDescList, &proto.NewFormFeeDesc{
			Content:     fee.Content,
			Icon:        fee.Icon,
			Amount:      fee.Fee.Amount,
			BorderColor: fee.BorderColor,
		})
	}

	return feeDescList

	//feeMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-fee_desc_list")
	//estimateFee := prov.GetEstimateFee()
	//goalDiscountFee := 0.0
	//dcmpKey := ""
	//if prov.IsBusinessPay() {
	//	// 企业
	//	goalDiscountFee = estimateFee
	//	dcmpKey = "business"
	//} else if prov.GetBonus() != 0 && prov.GetCoupon() != nil && prov.GetCoupon().Amount != 0 {
	//	// 省
	//	goalDiscountFee = prov.GetTotalFeeWithoutCouponSome() - prov.GetEstimateFee()
	//	dcmpKey = "save_money"
	//} else if prov.GetBonus() != 0 {
	//	// 金
	//	goalDiscountFee = prov.GetBonus()
	//	dcmpKey = "gold"
	//} else if prov.GetCoupon() != nil && prov.GetCoupon().Amount != 0 {
	//	// 券
	//	goalDiscountFee = prov.GetCoupon().Amount
	//	dcmpKey = "coupon"
	//}
	//if goalDiscountFee == 0 || dcmpKey == "" {
	//	return nil
	//}
	//
	//feeDescList = append(feeDescList, &proto.NewFormFeeDesc{
	//	Content: dcmp.TranslateTemplate(gjson.Get(feeMsgTemplate, dcmpKey+".content").String(), map[string]string{
	//		"reduce_fee": formatPrice(goalDiscountFee),
	//	}),
	//	Icon:        gjson.Get(feeMsgTemplate, dcmpKey+".icon").String(),
	//	BorderColor: gjson.Get(feeMsgTemplate, dcmpKey+".border_color").String(),
	//	Type:        int32(gjson.Get(feeMsgTemplate, dcmpKey+".type").Int()),
	//	Amount:      goalDiscountFee,
	//})
	//return feeDescList

}
