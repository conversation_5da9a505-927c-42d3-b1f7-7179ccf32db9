package rec_carpool

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
	// "git.xiaojukeji.com/gulfstream/mamba/render/fee_info_render"
	// "strconv"
)

type MultiPriceListProvider interface {
	IsCarpool() bool
	IsBusinessPay() bool
	GetEstimateFee() float64
	IsCarrpoolOnePrice() bool
	GetFailEstimateFee() float64
	// GetBonus() float64
	// GetCoupon() *fee_info_render.FeeItem
	// GetTotalFeeWithoutCouponSome() float64
	GetFastEstimatePrice() float64
	IsUpdateFeeDescList() bool
}

func MultiPriceList(ctx context.Context, prov MultiPriceListProvider) []*proto.NewFormMultiPrice {
	if !prov.IsCarpool() {
		return nil
	}
	textTemplate := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-fee_msg")
	newFormMultiPrice := []*proto.NewFormMultiPrice{}
	goalFee := 0.0
	dcmpKey := ""
	// fee_msg 渲染感知
	if prov.IsBusinessPay() {
		// 企业付
		dcmpKey = "business_carpool_fee_msg"
		goalFee = 0.0
	} else if prov.IsCarrpoolOnePrice() {
		// 一个价格或两个相同价格
		dcmpKey = "carpool_fee_msg"
		goalFee = prov.GetEstimateFee()
	} else if prov.GetFailEstimateFee() != 0 {
		// 两个不同价格
		goalFee = prov.GetEstimateFee()
		newFormMultiPrice = append(newFormMultiPrice, &proto.NewFormMultiPrice{
			FeeAmount: prov.GetEstimateFee(),
			FeeMsg: dcmp.TranslateTemplate(gjson.Get(textTemplate, "carpool_success_fee_msg").String(), map[string]string{
				"fee": formatPrice(goalFee),
			}),
		})
		dcmpKey = "carpool_fail_fee_msg"
		goalFee = prov.GetFailEstimateFee()
	}
	newFormMultiPrice = append(newFormMultiPrice, &proto.NewFormMultiPrice{
		FeeAmount: goalFee,
		FeeMsg: dcmp.TranslateTemplate(gjson.Get(textTemplate, dcmpKey).String(), map[string]string{
			"fee": formatPrice(goalFee),
		}),
	})
	if prov.IsUpdateFeeDescList() {
		return newFormMultiPrice
	}
	fnMax := func(f1, f2 float64) float64 {
		if f1 > f2 {
			return f1
		}
		return f2
	}
	// 其他感知
	textTemplate = dcmp.GetDcmpPlainContent(ctx, "rec_carpool-fee_desc_list")
	if prov.IsBusinessPay() {
		dcmpKey = "business"
		if prov.IsCarrpoolOnePrice() {
			goalFee = prov.GetEstimateFee()
		} else {
			goalFee = fnMax(prov.GetEstimateFee(), prov.GetFailEstimateFee())
		}
		newFormMultiPrice[0].FeeDesc = &proto.NewFormFeeDesc{
			Content: dcmp.TranslateTemplate(gjson.Get(textTemplate, dcmpKey+".content").String(), map[string]string{
				"reduce_fee": formatPrice(goalFee),
			}),
			Icon:        gjson.Get(textTemplate, dcmpKey+".icon").String(),
			BorderColor: gjson.Get(textTemplate, dcmpKey+".border_color").String(),
			Type:        int32(gjson.Get(textTemplate, dcmpKey+".type").Int()),
			Amount:      0.0,
		}
		return newFormMultiPrice
	}
	// 显示省
	if prov.GetFastEstimatePrice() == -1 {
		return newFormMultiPrice
	}
	fnDiff := func(f1, f2 float64) float64 {
		i1 := util.Float64Hundred(f1)
		i2 := util.Float64Hundred(f2)
		if i1 > i2 {

			return (float64(i1) - float64(i2)) / 100
		}
		return (float64(i2) - float64(i1)) / 100
	}
	dcmpKey = "save_money"
	for _, item := range newFormMultiPrice {
		if resDiffFee := fnDiff(item.FeeAmount, prov.GetFastEstimatePrice()); resDiffFee > 0 {
			item.FeeDesc = &proto.NewFormFeeDesc{
				Content: dcmp.TranslateTemplate(gjson.Get(textTemplate, dcmpKey+".content").String(), map[string]string{
					"reduce_fee": formatPrice(resDiffFee),
				}),
				Icon:        gjson.Get(textTemplate, dcmpKey+".icon").String(),
				BorderColor: gjson.Get(textTemplate, dcmpKey+".border_color").String(),
				Type:        int32(gjson.Get(textTemplate, dcmpKey+".type").Int()),
				Amount:      resDiffFee,
			}
		}
	}
	return newFormMultiPrice
}
