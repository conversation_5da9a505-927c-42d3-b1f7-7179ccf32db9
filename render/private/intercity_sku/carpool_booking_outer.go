package intercity_sku

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

type CarpoolBookingOuterData interface {
	GetDepartureRangeStr() string
}

func CarpoolBookingOuter(ctx context.Context, prov CarpoolBookingOuterData, carpoolBooking *proto.InterCityCarpoolBookingModule) *proto.InterCityCarpoolBookingSku {

	if carpoolBooking == nil {
		return nil
	}
	if len(carpoolBooking.TimeSpan) == 0 {
		return nil
	}
	skuTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_sku-time_span")
	timeSpan, isTop3 := GetTimeSpan(carpoolBooking, prov.GetDepartureRangeStr(), skuTemplate)
	signalText := ""
	if len(timeSpan) == 0 {
		// todo 迁移最外面 直接导流到normal
		signalText = gjson.Get(skuTemplate, "signal_text.no_sku").String()
	}
	if len(timeSpan) != 0 && !isTop3 {
		signalText = gjson.Get(skuTemplate, "signal_text.recommend").String()
	}

	if len(timeSpan) == 1 && isCurrentTimeSpan(prov.GetDepartureRangeStr(), timeSpan[0].Value) {
		signalText = ""
	}

	return &proto.InterCityCarpoolBookingSku{
		BottomText: gjson.Get(skuTemplate, "bottom_text").String(),
		SignalText: signalText,
		TimeSpan:   timeSpan,
	}
}

func isCurrentTimeSpan(span string, departureRangeStr string) bool {
	if span == departureRangeStr {
		return true
	}
	return false
}

func toOuterTimeSpan(timeRange []*TimeSpanWithDay) []*TimeSpanWithDay {
	res := []*TimeSpanWithDay{}
	for _, v := range timeRange {
		res = append(res, &TimeSpanWithDay{
			date: v.date,
			timeRange: &proto.InterCityTimeRange{
				Value:     v.timeRange.Value,
				Msg:       v.timeRange.Msg,
				OuterMsg:  v.timeRange.OuterMsg,
				OrderType: v.timeRange.OrderType,
				SkuDesc:   v.timeRange.SkuDesc,
				Icon:      v.timeRange.Icon,
				Available: v.timeRange.Available,
			},
		})
	}
	return res
}

type TimeSpanWithDay struct {
	timeRange *proto.InterCityTimeRange
	date      string
}

func GetTimeSpan(carpoolBooking *proto.InterCityCarpoolBookingModule, departureRange string, skuTemplate string) ([]*proto.InterCityTimeRange, bool) {
	// 前三个时间片
	timeSpan := []*TimeSpanWithDay{}
	// 推荐时间片
	recommendTimeSpan := []*TimeSpanWithDay{}
	// 当前时间片
	var now *TimeSpanWithDay
	// 前三个时间片是否有库存
	top3HaveSku := false
	// 前三个是否有库存
	isTop3 := true
	var res []*TimeSpanWithDay
	for _, v := range carpoolBooking.TimeSpan {
		if v == nil || len(v.Range) == 0 {
			continue
		}
		for _, span := range v.Range {
			// 当前时间片是否有库存
			isHaveSku := span.Available
			if isCurrentTimeSpan(span.Value, departureRange) {
				now = &TimeSpanWithDay{
					timeRange: span,
					date:      v.Date,
				}
			}

			if len(timeSpan) < 3 {
				top3HaveSku = top3HaveSku || isHaveSku
				timeSpan = append(timeSpan, &TimeSpanWithDay{
					timeRange: span,
					date:      v.Date,
				})
				continue
			}

			if len(recommendTimeSpan) < 2 && isHaveSku {
				recommendTimeSpan = append(recommendTimeSpan, &TimeSpanWithDay{
					timeRange: span,
					date:      v.Date,
				})
			}
			if len(recommendTimeSpan) == 2 && len(timeSpan) == 3 && now != nil {
				break
			}
		}
	}

	if len(timeSpan) != 0 && top3HaveSku {
		// 出前三个
		res = toOuterTimeSpan(timeSpan)
	} else if len(recommendTimeSpan) != 0 {
		// 出推荐
		isTop3 = false
		res = toOuterTimeSpan(recommendTimeSpan)
	} else {
		// todo 处理 异常情况
		return nil, isTop3
	}
	return processTimeSpan(res, isTop3, now, skuTemplate), isTop3
}

func processTimeSpan(timeSpan []*TimeSpanWithDay, isTop3 bool, now *TimeSpanWithDay, skuTemplate string) []*proto.InterCityTimeRange {
	res := []*proto.InterCityTimeRange{}
	haveCurrentTime := false
	for _, v := range timeSpan {
		v.timeRange.Msg = v.date + " " + v.timeRange.Msg
		if !isTop3 {
			v.timeRange.Icon = gjson.Get(skuTemplate, "icon_url").String()
		}
		res = append(res, v.timeRange)
		if now == nil || now.timeRange.Value == v.timeRange.Value || !now.timeRange.Available {
			haveCurrentTime = true
		}
	}
	if !haveCurrentTime {
		nowRe := &proto.InterCityTimeRange{
			Value:     now.timeRange.Value,
			Msg:       now.date + " " + now.timeRange.Msg,
			OuterMsg:  now.timeRange.OuterMsg,
			OrderType: now.timeRange.OrderType,
			SkuDesc:   now.timeRange.SkuDesc,
			Icon:      now.timeRange.Icon,
			Available: now.timeRange.Available,
		}
		return []*proto.InterCityTimeRange{nowRe}
	}
	return res
}
