package intercity_sku

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

type FeeItem struct {
	Tag    string
	Amount float64
}

type FeeItemList interface {
	//GetProductCategory() int64
	//GetCoupon() *FeeItem
	//GetBusinessPayAmount() float64
	//GetBonus() float64
	//GetApolloParam() map[string]string
}

func FeeDescList(ctx context.Context, ap render.ApolloProvider, provider model.FeeDescEngineInputProvider) []*proto.TagWithIconAndBorder {
	feeDescList := make([]*proto.TagWithIconAndBorder, 0)
	feeEnginEnv := fee_desc_engine.NewEnv(consts.InterCityForm).SetCap(2).SetApolloParams(provider).SetDcmpKey("intercity_sku-price_desc_list")
	feeInput := input.BuildNormalFeeInput(ctx, provider, consts.InterCityForm).
		WithCarpoolChildTicketFee(ctx, provider.GetDisplayLines()).
		WithCarpoolInfantsTicketFee(ctx, provider.GetDisplayLines())
	feeOutput := fee_desc_engine.NewFeeEngine(feeInput, feeEnginEnv).Do(ctx)
	for _, fee := range feeOutput {
		if fee != nil {
			feeDescList = append(feeDescList, &proto.TagWithIconAndBorder{
				Icon:        fee.Icon,
				Content:     fee.Content,
				BorderColor: fee.BorderColor,
				TextColor:   fee.TextColor,
			})
		}
	}

	timeSliceIcon := getPreferentialIcon(ctx, ap)
	// 如果有券的话，就在头部追加"预约优惠"的标识
	if len(feeDescList) != 0 && timeSliceIcon != "" {
		feeDescList = append([]*proto.TagWithIconAndBorder{{
			Content:     "",
			Icon:        timeSliceIcon,
			BorderColor: "",
		}}, feeDescList...)
	}
	return feeDescList
}

func formatPrice(price float64) string {
	return strconv.FormatFloat(price, 'f', 1, 64)
}

// getPreferentialIcon 获取添加"预约优惠"标识
func getPreferentialIcon(ctx context.Context, prov render.ApolloProvider) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithComboID, biz_runtime.WithRouteGroup, biz_runtime.WithProductID)
	ok, toggle := apollo.FeatureExp(ctx, "intercity_carpool_subscribe_preferential_toggle", pidKey, params)
	if !ok {
		return ""
	}
	timeSliceIcon := toggle.GetParameter("fee_desc_icon", "")
	return timeSliceIcon
}
