package intercity_new

import (
	"context"
	"strconv"

	// "strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	// "git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	Default = "default"
)

type SubTitleProv interface {
	GetProductCategory() int64
}

func SubTitle(ctx context.Context, prov SubTitleProv, businessName string) string {
	var subTitle string
	subTitleTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_new-sub_title_pc")
	subTitle = gjson.Get(subTitleTemplate, strconv.Itoa(int(prov.GetProductCategory()))).String()
	if len(subTitle) == 0 && len(businessName) != 0 {
		subTitle = gjson.Get(subTitleTemplate, Default).String()
	}
	return dcmp.TranslateTemplate(subTitle, map[string]string{"product_name": businessName})
}
