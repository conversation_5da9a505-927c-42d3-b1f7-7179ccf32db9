package get_form_real_expect_info

import (
	"context"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/model"
)

func BuildGlobalSceneExpect(ctx context.Context, info *model.BaseReq, totalExpectInfo *AthenaApiv3.GlobalOrderMatchExpectInfo) *proto.GlobalOrderMatchExpectInfo {
	if totalExpectInfo == nil {
		return nil
	}

	return &proto.GlobalOrderMatchExpectInfo{
		ShowType:     totalExpectInfo.ShowType,
		Scene:        totalExpectInfo.Scene,
		TimeStart:    totalExpectInfo.TimeStart,
		TimeEnd:      totalExpectInfo.TimeEnd,
		OriginScene:  totalExpectInfo.OriginScene,
		ProductInfos: formatProductInfos(totalExpectInfo.ProductInfos),
		ExtraInfo:    totalExpectInfo.ExtraInfo,
	}
}

func BuildProductExpect(ctx context.Context, info *model.BaseReq, totalExpectInfo *AthenaApiv3.ProductExpectInfo) *proto.ProductOrderMatchExpectInfo {
	if totalExpectInfo == nil {
		return nil
	}

	return &proto.ProductOrderMatchExpectInfo{
		ProductInfos: formatProductInfos(totalExpectInfo.ProductInfos),
		ExtraInfo:    totalExpectInfo.ExtraInfo,
	}
}

func BuildSaveTimeSceneExpect(ctx context.Context, info *model.BaseReq, totalExpectInfo *AthenaApiv3.SaveTimeExpectInfo) *proto.SaveTimeExpectInfo {
	if totalExpectInfo == nil {
		return nil
	}

	return &proto.SaveTimeExpectInfo{
		WaitTimeSaved: totalExpectInfo.WaitTimeSaved,
		ExtraInfo:     totalExpectInfo.ExtraInfo,
	}
}
