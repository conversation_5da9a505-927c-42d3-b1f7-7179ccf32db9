package multi_station_price

import (
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_multi_station_price/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"time"
)

func BuildSchedules(products []*biz_runtime.ProductInfoFull, shiftId2StationPrice map[string]*StationPriceRender, firstStationSumMinutes int32) []*proto.Schedule {
	//构建班次列表
	schedules := make([]*proto.Schedule, 0)
	for _, product := range products {
		prodAdapter := &data.AdapterPbdStationBus{ProductInfoFull: product}
		schedule := new(proto.Schedule)
		schedule.ScheduleId = prodAdapter.GetShiftID()
		schedule.LeftTicketCnt = cast.ToInt32(prodAdapter.GetMaxInventory())
		shiftDepartureTime := prodAdapter.GetInventoryExtraInfo()[data.ShiftDepartureTime]
		if shiftDepartureTime != "" {
			//fromTime=班次出发时间+可用首发站的eta累计时间,背景：根据区县查询的时候，会过滤可首发站，导致出发时间不准的情况，而外部方不会计算首发站的eta累计时间,故须加上可用首发站eta累计时间处理此情况
			t := time.Unix(cast.ToInt64(shiftDepartureTime), 0)
			t = t.Add(time.Duration(firstStationSumMinutes) * time.Minute)
			schedule.FromTime = t.Format("15:04")
		}
		schedule.AdvanceBookMinutes = prodAdapter.GetAdvanceBookMinutes()
		stationPriceInfo := shiftId2StationPrice[prodAdapter.GetShiftID()]
		schedule.BottomPrice = stationPriceInfo.BottomPrice
		schedule.SegmentPrices = stationPriceInfo.SegmentPrices
		schedules = append(schedules, schedule)
	}
	return schedules
}

type StationPriceRender struct {
	BottomPrice   float64
	SegmentPrices []*proto.SegmentPrice
}
