package intercity_multi_station

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

type SubMsgProvMock struct {
}

func (*SubMsgProvMock) GetSelectInfo() *models.StationInventorySelectInfo {
	return &models.StationInventorySelectInfo{
		ShiftID: "1",
	}
}

func (*SubMsgProvMock) GetBusMode() string {
	return ""
}

func (*SubMsgProvMock) GetRobinMap() map[string]*carpoolstation.BusServerShift {
	a := make(map[string]*carpoolstation.BusServerShift)
	a["1"] = &carpoolstation.BusServerShift{
		ShiftTagId: "1",
	}
	return a
}

type SubMsgProvFailMock struct {
}

func (*SubMsgProvFailMock) GetSelectInfo() *models.StationInventorySelectInfo {
	return &models.StationInventorySelectInfo{
		ShiftID: "1",
	}
}

func (*SubMsgProvFailMock) GetBusMode() string {
	return ""
}

func (*SubMsgProvFailMock) GetRobinMap() map[string]*carpoolstation.BusServerShift {
	a := make(map[string]*carpoolstation.BusServerShift)
	a["2"] = &carpoolstation.BusServerShift{
		ShiftTagId: "1",
	}
	return a
}

func TestSubBackground(t *testing.T) {
	// 定义测试用例结构体
	type testCase struct {
		name       string
		mockParams struct {
			ctx  context.Context
			prov SubMsgProv
		}
		setupMocks func() []*mockey.Mocker
		assertions func(t *testing.T, result0 *string, mockParams struct {
			ctx  context.Context
			prov SubMsgProv
		})
	}

	// 基础Mock方法（多个case之间公共的mock）
	getBaseMock := func() []*mockey.Mocker {
		return []*mockey.Mocker{
			mockey.Mock(dcmp.GetDcmpPlainContent).Return("{\"1\": \"text\"}").Build(),
		}
	}

	getBaseMock2 := func() []*mockey.Mocker {
		return []*mockey.Mocker{
			mockey.Mock(dcmp.GetDcmpPlainContent).Return("{\"12\": \"text\"}").Build(),
		}
	}

	testCases := []testCase{
		{
			name: "Test_SubBackground_Success",
			mockParams: struct {
				ctx  context.Context
				prov SubMsgProv
			}{
				ctx:  context.Background(),
				prov: &SubMsgProvMock{},
			},
			setupMocks: func() []*mockey.Mocker {
				// 动态创建基础mock（避免re-mock问题）
				baseMock := getBaseMock()
				// 创建当前用例特定的mock
				caseMock := []*mockey.Mocker{
					// 这里可以添加当前用例特定的mock方法
				}
				// 合并基础mock和用例特定mock
				caseMock = append(baseMock, caseMock...)
				return caseMock
			},
			assertions: func(t *testing.T, result0 *string, mockParams struct {
				ctx  context.Context
				prov SubMsgProv
			}) {
				assert.Equal(t, "text", *result0)
			}, // 你现在可以在这里使用 receiver 和 mockParams 进行额外的断言
			// 例如: assert.Equal(t, expectedValue, receiver.SomeField)
			// 例如: assert.Equal(t, expectedParam, mockParams.paramName)

		}, {
			name: "Test_SubBackground_Robin_nil",
			mockParams: struct {
				ctx  context.Context
				prov SubMsgProv
			}{
				ctx:  context.Background(),
				prov: &SubMsgProvFailMock{},
			},
			setupMocks: func() []*mockey.Mocker {
				// 动态创建基础mock（避免re-mock问题）
				baseMock := getBaseMock()
				// 创建当前用例特定的mock
				caseMock := []*mockey.Mocker{
					// 这里可以添加当前用例特定的mock方法
				}
				// 合并基础mock和用例特定mock
				caseMock = append(baseMock, caseMock...)
				return caseMock
			},
			assertions: func(t *testing.T, result0 *string, mockParams struct {
				ctx  context.Context
				prov SubMsgProv
			}) {
				assert.Nil(t, result0)
			}, // 你现在可以在这里使用 receiver 和 mockParams 进行额外的断言
			// 例如: assert.Equal(t, expectedValue, receiver.SomeField)
			// 例如: assert.Equal(t, expectedParam, mockParams.paramName)

		}, {
			name: "Test_SubBackground_Dcmp_Fail",
			mockParams: struct {
				ctx  context.Context
				prov SubMsgProv
			}{
				ctx:  context.Background(),
				prov: &SubMsgProvMock{},
			},
			setupMocks: func() []*mockey.Mocker {
				// 动态创建基础mock（避免re-mock问题）
				baseMock := getBaseMock2()
				// 创建当前用例特定的mock
				caseMock := []*mockey.Mocker{
					// 这里可以添加当前用例特定的mock方法
				}
				// 合并基础mock和用例特定mock
				caseMock = append(baseMock, caseMock...)
				return caseMock
			},
			assertions: func(t *testing.T, result0 *string, mockParams struct {
				ctx  context.Context
				prov SubMsgProv
			}) {
				assert.Nil(t, result0)
			}, // 你现在可以在这里使用 receiver 和 mockParams 进行额外的断言
			// 例如: assert.Equal(t, expectedValue, receiver.SomeField)
			// 例如: assert.Equal(t, expectedParam, mockParams.paramName)

		},
	}

	// 遍历测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 在每个子测试开始前确保清理所有mock
			mockey.UnPatchAll()

			// 动态创建并设置mock 避免未Patch的情况, 正常Build时会Patch
			mockMethods := tc.setupMocks()
			for _, mocker := range mockMethods {
				mocker.Patch()
			}
			// defer 调用 mockey 释放掉所有的mock数据
			defer mockey.UnPatchAll()

			// 调用被测试的函数
			result0 := SubBackground(tc.mockParams.ctx, tc.mockParams.prov)

			// 执行断言
			if tc.assertions != nil {
				tc.assertions(t, result0, tc.mockParams)
			}
		})
	}
}
