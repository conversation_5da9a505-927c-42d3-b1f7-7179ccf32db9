package intercity_multi_station

import (
	"context"
	"testing"
	"time"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

// MockTimeMsgProv 生成mock对象命令：
// mockgen -source=time_msg.go -destination=time_msg_mock.go -package=intercity_multi_station
type MockTimeMsgProv struct {
	departureTime          int64
	selectInfo             *models.StationInventorySelectInfo
	productFull            *biz_runtime.ProductInfoFull
	stationEarlyPickUpTime int32
}

func (m *MockTimeMsgProv) GetDepartureTime() int64 {
	return m.departureTime
}

func (m *MockTimeMsgProv) GetSelectInfo() *models.StationInventorySelectInfo {
	return m.selectInfo
}

func (m *MockTimeMsgProv) GetProductFull() *biz_runtime.ProductInfoFull {
	return m.productFull
}

func (m *MockTimeMsgProv) GetStationEarlyPickUpTimeEta() int32 {
	return m.stationEarlyPickUpTime
}

func TestTimeMsg(t *testing.T) {
	ctx := context.Background()
	testTime := time.Date(2023, 10, 1, 14, 30, 0, 0, time.UTC).Unix()

	t.Run("非EstimateV2返回时间格式", func(t *testing.T) {
		prov := &MockTimeMsgProv{departureTime: testTime}
		result := TimeMsg(ctx, prov, false)
		assert.Equal(t, "22:30", result)
	})

	t.Run("EstimateV2门到站场景", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockGetApolloParams := mockey.Mock((*biz_runtime.ProductInfoFull).GetApolloParams).Return("test_pid", map[string]string{
			"page_type":       "2",
			"to_city":         "101",
			"font_scale_type": "3",
		}).Build()
		defer mockGetApolloParams.UnPatch()

		// 构造门到站场景
		selectInfo := &models.StationInventorySelectInfo{
			FromStationInfo: &Prfs.StationInfo{StationSceneType: FenceStation},
		}
		prov := &MockTimeMsgProv{
			departureTime: testTime,
			selectInfo:    selectInfo,
			productFull:   &biz_runtime.ProductInfoFull{},
		}

		expectedMsg := "{{{time}}}开始接人"
		patch := mockey.Mock(dcmp.GetDcmpPlainContent).Return(expectedMsg).Build()
		defer patch.UnPatch()

		result := TimeMsg(ctx, prov, true)
		assert.Equal(t, "{22:30}开始接人", result)
	})

	t.Run("EstimateV2门到站场景", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockGetApolloParams := mockey.Mock((*biz_runtime.ProductInfoFull).GetApolloParams).Return("test_pid", map[string]string{
			"page_type":       "2",
			"to_city":         "101",
			"font_scale_type": "3",
		}).Build()
		defer mockGetApolloParams.UnPatch()

		// 构造站到门场景
		selectInfo := &models.StationInventorySelectInfo{
			DestStationInfo: &Prfs.StationInfo{StationSceneType: FenceStation},
		}
		prov := &MockTimeMsgProv{
			departureTime: testTime,
			selectInfo:    selectInfo,
			productFull:   &biz_runtime.ProductInfoFull{},
		}

		expectedMsg := "{{{time}}}发车"
		patch := mockey.Mock(dcmp.GetDcmpPlainContent).Return(expectedMsg).Build()
		defer patch.UnPatch()

		result := TimeMsg(ctx, prov, true)
		assert.Equal(t, "{22:30}发车", result)
	})

}
