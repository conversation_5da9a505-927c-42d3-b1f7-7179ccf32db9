package intercity_multi_station

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

type FeeMsgProv interface {
	GetEstimateFee() float64
}

func FeeMsg(ctx context.Context, prov FeeMsgProv) string {
	feeMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-feemsg")
	return dcmp.TranslateTemplate(
		feeMsgTemplate,
		map[string]string{"fee": formatPrice(prov.GetEstimateFee())},
	)
}
func formatPrice(price float64) string {
	return util.RemoveSuffixZero(strconv.FormatFloat(price, 'f', 2, 64))
}
