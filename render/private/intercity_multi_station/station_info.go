package intercity_multi_station

import (
	"context"
	"strconv"
	"time"

	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/distance_render"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	// 如果涵盖属性信息，则展示涵盖属性的文案内容
	PropertySuffix = "property"
	// 标识过度开关名称，防止上线前后顺序问题
	PropertyNew = "new"
	// 只包含属性时
	PropertyOnly = "only"
	// 兜底展示粤澳的属性，否则展示{}异常
	DefaultYueAoProperty = "粤澳"
	// 兜底的粤澳map id
	DefaultPropertyId = 1
	// 批量预估页面，站点配置
	IntercityStationInfoKey = "intercity_station-stationinfo"
	// 兜底的站点副标题key
	SubTitleOuter = "sub_title_outer"
	// 新样式站点副标题
	SubTitleOuterNew = "sub_title_outer_new"
	// 起点
	START = "start"
	// 终点
	END = "end"
	// 人像
	PORTRAITICON = "portrait_icon"
	// 精确站点
	AccurateStation = 0
	// 围栏站点
	FenceStation = 1
	// 围栏说明
	FenceIntro = "fence_intro"
	// 站点说明
	StationIntro = "station_intro"
	// 未选择站点
	NoSelectStation = 0
)

type StationInfoProv interface {
	GetAllStationInfo() []*Prfs.StationInfo
	GetSelectInfo() *models.StationInventorySelectInfo
	GetDepartureTime() int64
	GetAccessKeyID() int
	GetAppVersion() string
	GetBusMode() string
	GetStationPropertyInfo(ctx context.Context) map[int32][]*Prfs.PropertyInfo
	CheckShowStationProperty(ctx context.Context) bool
	GetShiftDetail() *CarpoolOpenApi.ShiftDetail
	GetRobinMap() map[string]*carpoolstation.BusServerShift
	GetStationInfo() models.StationInfo
}

func StationInfo(ctx context.Context, prov StationInfoProv, req *proto.IntercityMultiEstimateRequest, pid int64) (*proto.IntercityStationInfo, string, []*Prfs.PropertyInfo) {
	config := dcmp.GetDcmpContent(ctx, "intercity_station-stationinfo", nil)

	res := proto.IntercityStationInfo{
		Start: &proto.IntercityStation{
			Name:      prov.GetSelectInfo().FromStationInfo.StationName,
			StationId: int64(prov.GetSelectInfo().FromStationInfo.StationId),
			Lat:       prov.GetSelectInfo().FromStationInfo.StationLat,
			Lng:       prov.GetSelectInfo().FromStationInfo.StationLng,
		},
		End: &proto.IntercityStation{
			Name:      prov.GetSelectInfo().DestStationInfo.StationName,
			StationId: int64(prov.GetSelectInfo().DestStationInfo.StationId),
			Lat:       prov.GetSelectInfo().DestStationInfo.StationLat,
			Lng:       prov.GetSelectInfo().DestStationInfo.StationLng,
		},
		Detail: &proto.Detail{
			StationList:   []*proto.IntercityStationDetail{},
			ButtonText:    gjson.Get(config, "button_text").String(),
			MapButtonText: gjson.Get(config, "map_button_text").String(),
			RouteId:       prov.GetSelectInfo().RouteId,
			ShiftType:     prov.GetSelectInfo().ShiftType,
		},
	}

	if req.GetIsEstimateV2() {
		getTag(req, &res, prov.GetSelectInfo().FromStationInfo, prov.GetSelectInfo().SrcCost, config, START)
		getTag(req, &res, prov.GetSelectInfo().DestStationInfo, prov.GetSelectInfo().DestCost, config, END)
		if prov.GetSelectInfo().FromStationInfo.StationSceneType == FenceStation {
			if prov.GetSelectInfo().SrcCost != nil && prov.GetSelectInfo().SrcCost.Dist == 0 && len(req.GetStartPoiName()) > 0 {
				res.Start.Name = gjson.Get(config, START+"_"+StationIntro).String() + req.GetStartPoiName()
			} else {
				res.Start.Name = gjson.Get(config, START+"_"+StationIntro).String() + res.Start.Name
			}
		}
		if prov.GetSelectInfo().DestStationInfo.StationSceneType == FenceStation {
			if prov.GetSelectInfo().DestCost != nil && prov.GetSelectInfo().DestCost.Dist == 0 && len(req.GetEndPoiName()) > 0 {
				res.End.Name = gjson.Get(config, END+"_"+StationIntro).String() + req.GetEndPoiName()
			} else {
				res.End.Name = gjson.Get(config, END+"_"+StationIntro).String() + res.End.Name
			}
		}
		// 多点到门班次标识
		if v, ok := prov.GetSelectInfo().ExtraInfo[intercity.RouteSceneType]; ok {
			res.Detail.RouteSceneType = cast.ToInt32(v)
		}
	}

	dt := prov.GetDepartureTime()
	newdt := prov.GetDepartureTime()
	minRes := ""
	var propertyList []*Prfs.PropertyInfo
	if len(prov.GetAllStationInfo()) != 0 {
		res.Detail.Title = dcmp.TranslateTemplate(
			gjson.Get(config, "title").String(),
			map[string]string{"n": strconv.Itoa(len(prov.GetAllStationInfo()) - 2)},
		)
		totalMin := 0
		stationPropertyMap := prov.GetStationPropertyInfo(ctx)
		for k, v := range prov.GetAllStationInfo() {
			stationType := int32(0)
			if v.StationType == "1" {
				stationType = 1
			} else if v.StationType == "2" {
				stationType = 2
			} else if v.StationType == "3" {
				stationType = 3
			}
			if k != 0 {
				totalMin = totalMin + int(v.Eta)
			}
			detail := proto.IntercityStationDetail{
				Name:        v.StationName,
				DisplayName: v.StationName,
				Lat:         v.StationLat,
				Lng:         v.StationLng,
				Id:          v.StationId,
				Type:        stationType,
			}
			if (stationType == 1 || stationType == 3) && k != len(prov.GetAllStationInfo())-1 {
				if k != 0 {
					dt = dt + int64(v.Eta)*60
					newdt = GetDepartureTime(prov, int64(v.StationId))
				}
				if newdt != 0 {
					detail.BeginMsg = dcmp.TranslateTemplate(
						gjson.Get(config, "begin_msg").String(),
						map[string]string{"time": time.Unix(newdt, 0).Format("15:04")},
					)
				} else {
					detail.BeginMsg = dcmp.TranslateTemplate(
						gjson.Get(config, "begin_msg").String(),
						map[string]string{"time": time.Unix(dt, 0).Format("15:04")},
					)
				}
			}
			if k == 0 {
				detail.Icon = gjson.Get(config, "from_icon").String()
			}
			if k == len(prov.GetAllStationInfo())-1 {
				detail.Icon = gjson.Get(config, "to_icon").String()
			}
			if stationPropertyMap != nil && stationPropertyMap[v.StationId] != nil && len(stationPropertyMap[v.StationId]) > 0 {
				propertyList = stationPropertyMap[v.StationId]
			}

			res.Detail.StationList = append(res.Detail.StationList, &detail)

		}

		validMin := int(GetTotalTime(prov))
		if validMin != 0 {
			totalMin = validMin
		}

		hour := totalMin / 60
		min := totalMin % 60
		if hour != 0 {
			minRes = strconv.Itoa(hour) + gjson.Get(config, "hour").String()
		}
		if min != 0 {
			minRes = minRes + strconv.Itoa(min) + gjson.Get(config, "min").String()
		}

		if req.GetIsEstimateV2() {
			if hitSelectStationPointPageSwitch(ctx, pid, prov) {
				res.Detail.Title = dcmp.TranslateTemplate(
					gjson.Get(config, "title").String(),
					map[string]string{"n": strconv.Itoa(len(prov.GetAllStationInfo()) - 2)},
				) + " " + DetailTitle(ctx, minRes) + ">"
				res.Detail.CorrectStationLink = gjson.Get(config, "correct_station_link").String()
			} else {
				res.Detail.Title = DetailTitle(ctx, minRes)
			}
		} else {
			res.Detail.Title = dcmp.TranslateTemplate(
				gjson.Get(config, "title").String(),
				map[string]string{"n": strconv.Itoa(len(prov.GetAllStationInfo()) - 2)},
			)
		}
		res.Detail.SubTitle = detailSubTitle(ctx, minRes)
	}
	return &res, minRes, propertyList
}

// hitSelectStationPointPageSwitch 修改站点页受到版本控制
func hitSelectStationPointPageSwitch(ctx context.Context, passengerID int64, prov StationInfoProv) bool {
	return apollo.FeatureToggle(
		ctx,
		"gs_select_station_point_page_switch",
		cast.ToString(passengerID),
		map[string]string{
			"pid":           cast.ToString(passengerID),
			"access_key_id": cast.ToString(prov.GetAccessKeyID()),
			"app_version":   prov.GetAppVersion(),
			"city_id":       cast.ToString(prov.GetStationInfo().StartCity),
		})
}

func getTag(req *proto.IntercityMultiEstimateRequest, res *proto.IntercityStationInfo, stationInfo *Prfs.StationInfo, cost *CarpoolOpenApi.StationRouteCost, config string, model string) {
	if res == nil {
		return
	}
	if stationInfo.StationSceneType == AccurateStation {
		if cost == nil {
			return
		}
		dist := cost.Dist
		if dist <= 0 {
			return
		}
		distance, dcmpKey := distance_render.GetDistanceInfo(int64(dist))
		if model == START {
			res.Start.Tag = &proto.DisabledInfo{}
			if dcmpKey == "distance" {
				res.Start.Tag.Icon = gjson.Get(config, model+"_"+PORTRAITICON).String()
			}
			res.Start.Tag.Content = dcmp.TranslateTemplate(gjson.Get(config, model+"_"+dcmpKey).String(), map[string]string{
				"distance": distance,
			})
		} else {
			res.End.Tag = &proto.DisabledInfo{}
			if dcmpKey == "distance" {
				res.End.Tag.Icon = gjson.Get(config, model+"_"+PORTRAITICON).String()
			}
			res.End.Tag.Content = dcmp.TranslateTemplate(gjson.Get(config, model+"_"+dcmpKey).String(), map[string]string{
				"distance": distance,
			})
		}
	}
	if stationInfo.StationSceneType == FenceStation {
		if stationInfo == nil {
			return
		}
		if model == START {
			if !(len(req.GetStartPoiName()) > 0 && cost != nil && cost.Dist == 0) {
				res.Start.Tag = &proto.DisabledInfo{
					Content: gjson.Get(config, model+"_"+FenceIntro).String(),
				}
			}
		} else {
			if !(len(req.GetEndPoiName()) > 0 && cost != nil && cost.Dist == 0) {
				res.End.Tag = &proto.DisabledInfo{
					Content: gjson.Get(config, model+"_"+FenceIntro).String(),
				}
			}
		}
	}
	return
}

func GetDepartureTime(prov StationInfoProv, id int64) int64 {
	if prov.GetShiftDetail() == nil {
		return 0
	}
	for _, v := range prov.GetShiftDetail().StationDetails {
		if v.StationID == id && v.DepartureTime != nil {
			return *v.DepartureTime
		}
	}
	return 0
}

func GetTotalTime(prov StationInfoProv) int64 {
	wid := len(prov.GetAllStationInfo())
	if wid > 0 {
		startId := int64(prov.GetAllStationInfo()[0].StationId)
		endId := int64(prov.GetAllStationInfo()[wid-1].StationId)
		startTime := GetDepartureTime(prov, startId)
		endTime := GetDepartureTime(prov, endId)
		if startTime == 0 || endTime == 0 {
			return 0
		}
		return (GetDepartureTime(prov, endId) - GetDepartureTime(prov, startId)) / 60
	}
	return 0
}

// Compliance ...
func Compliance(ctx context.Context, prov StationInfoProv, businessName string, propertyList []*Prfs.PropertyInfo, icon string) []string {
	var subTitleList []string
	dcmpContent := dcmp.GetDcmpPlainContent(ctx, IntercityStationInfoKey)
	subTitleList = GetSubtitleList(ctx, prov, businessName, dcmpContent, icon)
	propertyName := GetPropertyName(DefaultPropertyId, propertyList)
	textParams := map[string]string{
		"brand_name": businessName,
		"property":   propertyName,
	}
	for i := 0; i < len(subTitleList); i++ {
		subTitleList[i] = dcmp.TranslateTemplate(subTitleList[i], textParams)
	}
	return subTitleList
}

// SubTitle ...
func SubTitle(ctx context.Context,
	prov StationInfoProv,
	time, businessName string,
	propertyList []*Prfs.PropertyInfo) string {
	// 兜底展示：防止品牌不在的情况：预计行驶xxx分钟
	textPath := GetDcmpKey(ctx, prov, propertyList, businessName)
	// 获取属性名称，根据属性id，匹配属性后展示：xx段
	propertyName := GetPropertyName(DefaultPropertyId, propertyList)
	textConfig := gjson.Get(dcmp.GetDcmpPlainContent(ctx, IntercityStationInfoKey), textPath).String()
	if len(textConfig) == 0 {
		// 如果，对应后缀的数据match不到，依旧用兜底的
		textConfig = gjson.Get(dcmp.GetDcmpPlainContent(ctx, IntercityStationInfoKey), SubTitleOuter).String()
	}

	textParams := map[string]string{
		"time":       time,
		"property":   propertyName,
		"brand_name": businessName,
	}

	return dcmp.TranslateTemplate(textConfig, textParams)
}

func DetailTitle(ctx context.Context, minRes string) string {
	dcmpText := gjson.Get(dcmp.GetDcmpPlainContent(ctx, "intercity_station-stationinfo"), "sub_title_outer").String()
	return dcmp.TranslateTemplate(dcmpText, map[string]string{"time": minRes})
}

func detailSubTitle(ctx context.Context, minRes string) string {
	dcmpText := gjson.Get(dcmp.GetDcmpPlainContent(ctx, "intercity_station-stationinfo"), "sub_title_optimization").String()
	return dcmp.TranslateTemplate(dcmpText, map[string]string{"min": minRes})
}

// FillBusModeInfo 根据路线获取大巴运营模式
func FillBusModeInfo(productFull *biz_runtime.ProductInfoFull) {
	if productFull == nil || productFull.Product == nil || productFull.Product.BizInfo == nil || productFull.Product.BizInfo.ComboID == 0 || productFull.Product.BizInfo.BusMode != "" {
		return
	}

	pidKey, apoParams := productFull.GetApolloParams(biz_runtime.WithPIDKey)
	apoParams["route_id"] = strconv.FormatInt(productFull.Product.BizInfo.ComboID, 10)
	allow, expParams := apollo.GetParameters("bus_mode_toggle", pidKey, apoParams)
	if allow && len(expParams) > 0 {
		productFull.Product.BizInfo.BusMode = expParams["bus_mode"]
	}
}

// 获取对应承运商户名称
func GetBusinessName(ctx context.Context, productFull *biz_runtime.ProductInfoFull) string {
	businessName := car_info.GetBusinessName(ctx, productFull)
	if len(businessName) == 0 {
		return ""
	}
	return businessName
}

// 拼接不同场景下获取的文案配置信息
func GetDcmpKey(ctx context.Context, prov StationInfoProv, propertyList []*Prfs.PropertyInfo, businessName string) string {
	dcmpPath := SubTitleOuter
	containProperty := false
	if prov.CheckShowStationProperty(ctx) {
		containProperty = nil != propertyList && len(propertyList) != 0
	}

	// 如果既开启了属性，也属于不同模式，则展示模式+属性
	if suffix := prov.GetBusMode(); suffix != "" && len(businessName) != 0 && containProperty {
		// 预计行驶{{time}} | {{brand_name}}承运 | {{property}}段 | 公交模式
		dcmpPath = dcmpPath + "_" + PropertyNew + "_" + suffix + "_" + PropertySuffix
	} else if suffix := prov.GetBusMode(); suffix != "" && len(businessName) != 0 {
		// 预计行驶{{time}} | {{brand_name}}承运 | 公交模式
		dcmpPath = dcmpPath + "_" + PropertyNew + "_" + suffix
	} else if containProperty && len(businessName) > 0 {
		// 预计行驶{{time}} | {{brand_name}}承运 | {{property}}段
		dcmpPath = dcmpPath + "_" + PropertyNew + "_" + PropertySuffix
	} else if containProperty {
		// 预计行驶{{time}} | {{property}}段
		dcmpPath = dcmpPath + "_" + PropertyNew + "_" + PropertyOnly + "_" + PropertySuffix
	} else if len(businessName) != 0 {
		// 预计行驶{{time}} | {{brand_name}}承运
		dcmpPath = SubTitleOuterNew
	}
	return dcmpPath

}

func GetSubtitleList(ctx context.Context, prov StationInfoProv, businessName string, dcmpContent string, icon string) []string {
	var subtitleList []string
	subtitleList = append(subtitleList, icon)
	if len(businessName) > 0 {
		subtitleList = append(subtitleList, gjson.Get(dcmpContent, "sub_title_outer_brand_name").String())
	}
	if prov.CheckShowStationProperty(ctx) {
		subtitleList = append(subtitleList, gjson.Get(dcmpContent, "sub_title_outer_property").String())
	}
	return subtitleList
}

// 获取xx段的属性名称
func GetPropertyName(needPropertyId int32, propertyList []*Prfs.PropertyInfo) string {
	for _, propertyConfig := range propertyList {
		if propertyConfig.PropertyId == needPropertyId {
			return propertyConfig.PropertyName
		}
	}
	return DefaultYueAoProperty
}
