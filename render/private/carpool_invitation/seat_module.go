package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"time"
)

type SeatModuleProvider interface {
	GetCarpoolSeatMaxNum() int
	GetCarpoolSeatCurrentNum() int
	GetDepartureTime() time.Time
	GetInviterSeatNum() int32
	ApolloParam() *ApolloModel.User
}

func BuildSeatModule(ctx context.Context, prov SeatModuleProvider) *proto.InvitationSeatOptionModule {
	carpoolSeatOption := pincheche.CarpoolSeatModule(ctx, prov)
	if carpoolSeatOption == nil {
		return nil
	}

	res := &proto.InvitationSeatOptionModule{
		Title:      carpoolSeatOption.Title,
		OptionList: carpoolSeatOption.OptionList,
	}

	if prov.GetInviterSeatNum() == 2 {
		for _, opt := range res.OptionList {
			if opt.Value == 2 {
				opt.Selected = false
				opt.Disable = true
				break
			}
		}
		res.SeatsExceedMsg = dcmp.GetJSONContentWithPath(ctx, DcmpKeyPinchecheInvitation, nil, "seat_num_exceed_msg")
	}

	return res
}
