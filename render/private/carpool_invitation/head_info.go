package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type HeadImgProvider interface {
	IsRouteMatch() bool
}

func GetHeadImg(ctx context.Context, prov HeadImgProvider) string {
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheInvitation, DcmpPathHeadRegion)
	if prov.IsRouteMatch() {
		return *dcmp.GetStringDcmpContentByPath(config, DcmpPathHeadImgMatch)
	}

	return *dcmp.GetStringDcmpContentByPath(config, DcmpPathHeadImgUnMatch)
}
