package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"
)

type MultiPriceProvider interface {
	GetInviterSeatNum() int32
	GetExactSeatNum() int32
	GetPriceByScene(seatNum, poolNum int32, carpoolSuccess bool) float64
}

func BuildMultiFeeMsg(ctx context.Context, prov MultiPriceProvider) []string {
	var multiFeeMsg = make([]string, 0)
	scenePriceOne := prov.GetPriceByScene(prov.GetExactSeatNum(), 1, true)
	scenePriceTwo := prov.GetPriceByScene(prov.GetExactSeatNum(), 2, true)

	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheInvitation, DcmpPathMultiPrice)

	// 客态选择2座 => 拼成xx元   使用拼1人价沟通
	if prov.GetExactSeatNum() == 2 && scenePriceOne >= 0 {
		multiFeeMsg = append(multiFeeMsg, getFeeMsg(ctx, config, "fee_equal", scenePriceOne))
		return multiFeeMsg
	}

	// 主态选择2座 => 拼到2人y元  使用拼2人价沟通
	if prov.GetInviterSeatNum() == 2 && scenePriceTwo >= 0 {
		multiFeeMsg = append(multiFeeMsg, getFeeMsg(ctx, config, "success_two", scenePriceTwo))
		return multiFeeMsg
	}

	// 拼一人 拼两人 等价 => 拼成xx元
	if scenePriceOne == scenePriceTwo && scenePriceOne >= 0 {
		multiFeeMsg = append(multiFeeMsg, getFeeMsg(ctx, config, "fee_equal", scenePriceOne))
		return multiFeeMsg
	}

	// 拼一人 拼两人 不等价 =>  拼到2人xx元 拼到1人yy元
	if scenePriceTwo >= 0 {
		multiFeeMsg = append(multiFeeMsg, getFeeMsg(ctx, config, "success_two", scenePriceTwo))
	}

	if scenePriceOne >= 0 {
		multiFeeMsg = append(multiFeeMsg, getFeeMsg(ctx, config, "success_one", scenePriceOne))
	}

	return multiFeeMsg
}

func getFeeMsg(ctx context.Context, config map[string]gjson.Result, path string, fee float64) string {
	rawFeeMsg := *dcmp.GetStringDcmpContentByPath(config, path)
	return util.ReplaceTag(ctx, rawFeeMsg, map[string]string{
		"num": util.FormatPrice(fee, -1),
	})
}
