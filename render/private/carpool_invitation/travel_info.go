package carpool_invitation

import (
	"context"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"
	"time"
)

type TravelInfoProvider interface {
	IsRouteMatch() bool
	GetMatchDepartureRange() string
	GetUserOption() *models.UserOption
	GetToName() string
	GetFromName() string
	GetOrderType() int16
	GetTimeSpan() []*EstimateDecision.TimeSpanV2
}

func BuildTravelInfo(ctx context.Context, prov TravelInfoProvider) *proto.InvitationTravelData {
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheInvitation, DcmpPathTravelInfo)
	res := &proto.InvitationTravelData{
		DepartureText: *dcmp.GetStringDcmpContentByPath(config, "departure_now"),
		StartName:     prov.GetFromName(),
		DestName:      prov.GetToName(),
	}

	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	endOfTomorrow := endOfDay.Add(24 * time.Hour)
	dr := prov.GetUserOption().DepartureRange

	// 顺路使用duse预匹配返回的推荐时间
	var departureTime *models.DepartureRange
	if prov.IsRouteMatch() {
		dr = intercity_common.DecodeDepartureRangeV3(prov.GetMatchDepartureRange())
	} else if departureTime = getSelectedTimeSlice(ctx, prov); departureTime != nil {
		// 不顺路 感知当前预估落在的时间片
		dr = departureTime
	}

	var dayTime, dayText string

	if dr != nil && (dr.From.Before(endOfDay) || prov.GetOrderType() == order.OrderTypeNow && prov.IsRouteMatch()) {
		//[今天]{8:00-8:10}出发
		dayText = *dcmp.GetStringDcmpContentByPath(config, "departure_today")
	} else if dr != nil && dr.From.After(endOfDay) && dr.From.Before(endOfTomorrow) {
		//[明天]{8:00-8:10}出发
		dayText = *dcmp.GetStringDcmpContentByPath(config, "departure_tomorrow")
	} else if dr != nil {
		// 兜底异常的时间 直接展示日期 2024-10-12{8:00-8:10}出发
		dayTime = dr.From.Format(consts.DateFormat)
		dayText = *dcmp.GetStringDcmpContentByPath(config, "departure_other")
	}

	if dr != nil && dayText != "" {
		startTime := time.Unix(dr.From.Local().Unix(), 0).Format("15:04")
		endTime := time.Unix(dr.To.Local().Unix(), 0).Format("15:04")
		res.DepartureText = util.ReplaceTag(ctx, dayText, map[string]string{
			"day":        dayTime,
			"start_time": startTime,
			"end_time":   endTime,
		})
	}

	if (prov.GetOrderType() == order.OrderTypeNow || departureTime != nil && (departureTime.To.Equal(now) || departureTime.From.Equal(now) || departureTime.From.Before(now) && departureTime.To.After(now))) &&
		!prov.IsRouteMatch() {
		res.DepartureText = *dcmp.GetStringDcmpContentByPath(config, "departure_now")
	}

	return res
}

func getSelectedTimeSlice(ctx context.Context, prov TravelInfoProvider) *models.DepartureRange {
	timeSpans := prov.GetTimeSpan()
	if len(timeSpans) == 0 {
		return nil
	}

	var departureTime = prov.GetUserOption().DepartureRange.To.Unix()

	_, timeSpan := after_dds_fission.ConvertTimeSpan(timeSpans, 0, departureTime)

	for _, dayTime := range timeSpan {
		if dayTime == nil {
			continue
		}
		for _, hourTime := range dayTime.Time {
			if hourTime == nil {
				continue
			}
			for _, minuTime := range hourTime.MinuteList {
				if minuTime == nil {
					continue
				}
				if minuTime.Selected {
					return intercity_common.DecodeDepartureRangeV3(minuTime.GetValue())
				}
			}
		}
	}

	return nil
}
