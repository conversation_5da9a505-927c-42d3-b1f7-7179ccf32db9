package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"math"
	"strconv"
)

type MatchRateProvider interface {
	IsRouteMatch() bool
	GetRouteMatchRate() float64
}

func BuildMatchRateInfo(ctx context.Context, prov MatchRateProvider) (string, string) {
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheInvitation, DcmpPathRouteMatch)
	if prov.IsRouteMatch() {
		icon := *dcmp.GetStringDcmpContentByPath(config, "route_match_icon")
		msg := *dcmp.GetStringDcmpContentByPath(config, "route_match_msg")
		replace := map[string]string{
			"rate": strconv.Itoa(int(math.Min(math.Round(prov.GetRouteMatchRate()*100), 100))),
		}
		return icon, util.ReplaceTag(ctx, msg, replace)
	}

	return "", ""
}
