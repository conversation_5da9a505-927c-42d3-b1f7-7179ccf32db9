package passenger_begin_charge

import (
	"context"

	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/passenger_begin_charge/bubble_info"
)

type PassengerBeginChargeProvider interface {
	GetTravelQuotation() *Ferrari.TravelQuotation // 获取行中报价单
}

func Render(ctx context.Context, provider PassengerBeginChargeProvider) (*proto.PassengerBeginChargeData, NewErrors.BizError) {
	passengerBeginChargeData := &proto.PassengerBeginChargeData{}
	passengerBeginChargeData.BubbleInfo = bubble_info.BuildBubbleInfo(ctx, provider)

	return passengerBeginChargeData, nil
}
