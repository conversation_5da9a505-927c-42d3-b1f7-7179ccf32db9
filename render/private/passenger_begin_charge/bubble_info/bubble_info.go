package bubble_info

import (
	"context"
	"encoding/json"

	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/passenger_begin_charge/model"
)

const IsUpgrade = "1"

type BubbleInfoProvider interface {
	GetTravelQuotation() *Ferrari.TravelQuotation
}

func BuildBubbleInfo(ctx context.Context, provider BubbleInfoProvider) *proto.BubbleInfo {
	travelQuotation := provider.GetTravelQuotation()
	if travelQuotation == nil {
		return nil
	}

	isUpgrade, upgradeEstimateFee, err := getUpgradeInfo(ctx, travelQuotation)
	if err != nil {
		return nil
	}

	if !isUpgrade {
		return nil
	}

	result := dcmp.GetJSONResult(ctx, "passenger_begin_charge-bubble_info", nil)
	return &proto.BubbleInfo{
		RenderInfo: &proto.RenderInfo{
			BubbleTitle:    result["bubble_title"].String(),
			BubbleSubTitle: upgradeEstimateFee,
			TotalFeeText: util.ReplaceTag(ctx, result["total_fee_text"].String(), map[string]string{
				"num": upgradeEstimateFee,
			}),
		},
		OriginInfo: &proto.OriginInfo{
			IsUpgradeFlag: IsUpgrade,
		},
	}
}

func getUpgradeInfo(ctx context.Context, travelQuotation *Ferrari.TravelQuotation) (isUpgrade bool, upgradeEstimateFee string, err error) {
	if travelQuotation == nil {
		return
	}

	priceItem, ok := travelQuotation.PriceData[ferrari.OrderEstimateFee]
	if ok && priceItem != nil {
		ci := &model.CacheInfo{}
		err = json.Unmarshal([]byte(priceItem.ExtraInfo), ci)
		if err != nil {
			log.Trace.Warnf(ctx, "getUpgradeInfo", "unmarshal fail:%v", err)
			return isUpgrade, "", NewErrors.NewBizError(err, NewErrors.ErrnoSystemError)
		}

		if ci.FeeType == nil || ci.EstimateFee == nil {
			return isUpgrade, "", nil
		}

		if *ci.FeeType != consts.FeeTypeUpgrade {
			return isUpgrade, "", nil
		}

		return true, util.Float64ToString(*ci.EstimateFee), nil
	}

	priceItem, ok = travelQuotation.PriceData[ferrari.WaitFreeUpgradeFee]
	if ok && priceItem != nil {
		isUpgrade = true
		return isUpgrade, util.Fen2Yuan(priceItem.FeeValue), nil
	}

	priceItem, ok = travelQuotation.PriceData[ferrari.BeforeFreeUpgradeFee]
	if ok && priceItem != nil {
		isUpgrade = true
		return isUpgrade, util.Fen2Yuan(priceItem.FeeValue), nil
	}

	return
}
