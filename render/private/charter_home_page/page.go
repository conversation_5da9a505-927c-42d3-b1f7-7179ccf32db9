package charter_home_page

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	trace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/s3e/x-engine/condition"
)

const (
	CharterHomePageHeadConfNS   = "charter_render_matarial"
	CharterHomePageHeadConfName = "charter_landing_page_head"
)

type headConfParams struct {
	CityId int `json:"city_id"`
}

// GetPageHeadConf 城市维度获取包车头图等物料
func GetPageHeadConf(ctx context.Context, area int) (items []*proto.HeadImgItem, time int32, style int32, ratio float64) {
	material := &proto.CharterHomePageData{}
	params := &headConfParams{CityId: area}

	res, err := condition.Check(ctx, CharterHomePageHeadConfNS, params, condition.WithConfName(CharterHomePageHeadConfName))
	if res == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pageHeadConf x-engine check failed, params=%+v, res=%+v, error=%s", params, res, err.Error())
		return
	}

	if !res.IsAllow {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pageHeadConf x-engine check not allowed, params=%+v， reason=%s", params, res.Reason)
		return
	}

	err = res.GetMaterial(material)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pageHeadConf x-engine get material failed, material=%+v, err=%s", res.Material, err.Error())
		return
	}

	return material.HeadImgList, material.HeadImgTime, material.TitleColorStyle, material.HeadImgAspectRatio
}
