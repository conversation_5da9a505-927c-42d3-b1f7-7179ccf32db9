package charter_home_page

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_combo"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	"sort"
)

const (
	CharterHomePageTabConfDcmpKey = "charter-home_page_tab"

	SelectMark   = 1
	UnSelectMark = 0
)

type CharterTab struct {
	productId int
	combos    map[int]*charter_combo.ComboPackage
	sourceId  int
	tabNum    int // tab的总数量

	material *charterTabConf
}

type charterTabConf struct {
	TabName            string `json:"tab_name"`
	TabSelectIcon      string `json:"tab_select_icon"`
	TabSelectBg        string `json:"tab_select_bg"`
	JumpUrl            string `json:"jump_url"`
	ComboTitle         string `json:"combo_title"`
	ComboTitleSolo     string `json:"combo_title_solo"`
	ComboDetailTitle   string `json:"combo_detail_title"`
	ComboDetailDesc    string `json:"combo_detail_desc"`
	ComboDetailSubDesc string `json:"combo_detail_sub_desc"`
	SourceId           int    `json:"source_id"`
	BusinessId         int    `json:"business_id"`
}

func NewChartTab(productId int, combos map[int]*charter_combo.ComboPackage, sourceId int, tabNum int) *CharterTab {
	return &CharterTab{
		productId: productId,
		combos:    combos,
		sourceId:  sourceId,
		tabNum:    tabNum,

		material: &charterTabConf{},
	}
}

func (t *CharterTab) Render(ctx context.Context) *proto.TabItem {
	if t.combos == nil {
		return nil
	}

	tmpl := dcmp.GetJSONContentWithPath(ctx, CharterHomePageTabConfDcmpKey, nil, util.ToString(t.productId))
	err := json.Unmarshal([]byte(tmpl), t.material)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pageTabConf unmarshal dcmp failed. tmpl=%s, err=%s", tmpl, err.Error())
		return nil
	}

	var (
		comboDetails = make([]*proto.ComboDetail, 0, len(t.combos))
	)

	for _, combo := range t.combos {
		if combo == nil {
			continue
		}

		comboDetails = append(comboDetails, &proto.ComboDetail{
			ComboId:      int32(combo.ComboId),
			ComboTitle:   util.ReplaceTag(ctx, t.material.ComboDetailTitle, map[string]string{"num": cast.ToString(combo.Hour)}),
			ComboDesc:    util.ReplaceTag(ctx, t.material.ComboDetailDesc, map[string]string{"num": cast.ToString(combo.Distance)}),
			ComboSubDesc: util.ReplaceTag(ctx, t.material.ComboDetailSubDesc, map[string]string{"num": cast.ToString(combo.Amount)}),
		})
	}

	sort.SliceStable(comboDetails, func(i, j int) bool {
		return comboDetails[i].ComboId < comboDetails[j].ComboId
	})

	res := &proto.TabItem{
		TabName:       t.material.TabName,
		TabSelectIcon: t.material.TabSelectIcon,
		TabSelectBg:   t.material.TabSelectBg,
		IsDefault:     UnSelectMark,
		JumpUrl:       t.material.JumpUrl,
		ComboInfo: &proto.ComboInfo{
			ComboTitle:  t.material.ComboTitle,
			ComboDetail: comboDetails,
		},
		SourceId:   int32(t.material.SourceId),
		BusinessId: int32(t.material.BusinessId),
	}

	if charter_car.SourceId2ProductId[t.sourceId] == t.productId {
		res.IsDefault = SelectMark
	}

	if t.tabNum == 1 {
		res.IsDefault = SelectMark
		res.TabName = ""
		res.ComboInfo.ComboTitle = t.material.ComboTitleSolo
	}

	return res
}
