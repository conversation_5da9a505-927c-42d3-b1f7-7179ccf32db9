package pbd_station_bus_estimate_render

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_estimate"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// getOrderParams ... 改签&明细接口使用
func GetOrderParams(ctx context.Context, product *pbd_station_bus_estimate.AdapterPbdStationBus) *proto.PsOrderParams {
	if product == nil {
		return nil
	}
	//使用起终站点的位置信息
	fromAreaInfo := new(proto.PsOrderAreaInfo)
	areaInfo := product.BaseReqData.AreaInfo
	fromAreaInfo.Lng = cast.ToString(areaInfo.FromLng)
	fromAreaInfo.Lat = cast.ToString(areaInfo.FromLat)
	fromAreaInfo.CityId = cast.ToInt64(areaInfo.Area)
	fromAreaInfo.StationId = product.GetCommonBizInfo().StationInfo.StartStationId

	toAreaInfo := new(proto.PsOrderAreaInfo)
	toAreaInfo.Lng = cast.ToString(areaInfo.ToLng)
	toAreaInfo.Lat = cast.ToString(areaInfo.ToLat)
	toAreaInfo.CityId = cast.ToInt64(areaInfo.ToArea)
	toAreaInfo.StationId = product.GetCommonBizInfo().StationInfo.EndStationId
	//品类信息
	productParams := make([]OrderParamProduct, 0)
	productParam := new(OrderParamProduct)
	productParam.ProductCategory = product.GetProductCategory()
	productParam.ComboId = product.GetComboID()
	productParam.ComboType = product.GetComboType()
	productParam.RequireLevel = cast.ToInt64(product.GetRequireLevel())
	productParam.BusinessId = product.GetBusinessID()
	productParam.PageType = product.GetPageType()
	productParam.RouteType = cast.ToInt32(product.GetRouteType())
	carpoolSeatNum := product.GetBizInfo().CarpoolSeatNum
	productParam.CarpoolSeatNum = &carpoolSeatNum
	productParams = append(productParams, *productParam)
	productParamBytes, err := json.Marshal(productParams)
	if err != nil {
		log.Trace.Warnf(ctx, "pbd station bus GetOrderParams err||%v", err.Error())
	}
	return &proto.PsOrderParams{
		RouteId:           product.GetComboID(),
		BusServiceShiftId: product.GetShiftID(),
		PoolSeat:          cast.ToInt64(carpoolSeatNum),
		Origin:            fromAreaInfo,
		Destination:       toAreaInfo,
		EstimateId:        product.GetEstimateID(),
		DepartureTime:     product.GetDepartureTime(),
		Product:           string(productParamBytes),
	}
}

type OrderParamProduct struct {
	ProductCategory int64  `json:"product_category" `
	ComboId         int64  `json:"combo_id" `
	ComboType       int64  `json:"combo_type" `
	RequireLevel    int64  `json:"require_level" `
	BusinessId      int64  `json:"business_id" `
	PageType        int32  `json:"page_type" `
	RouteType       int32  `json:"route_type" `
	CarpoolSeatNum  *int32 `json:"carpool_seat_num,omitempty"`
}
