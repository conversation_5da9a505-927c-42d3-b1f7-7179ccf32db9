package pbd_station_bus_estimate_render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_estimate"
)

func GetVotes(ctx context.Context, prov *pbd_station_bus_estimate.AdapterPbdStationBus) []*proto.Vote {
	ruleData := prov.GetRuleData()
	votes := make([]*proto.Vote, 0)
	//默认支持成人
	vote := proto.Vote{
		Type: seat_selection_consts.Adult.ToInt32(),
	}
	votes = append(votes, &vote)
	if ruleData != nil {
		if len(ruleData.SpecialSeatRules) > 0 {
			for _, rules := range ruleData.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.ChildTicket && rules.IsSupport == 1 {
					votes = append(votes, &proto.Vote{
						Type: seat_selection_consts.Children.ToInt32(),
					})
				}
			}
		}
	}
	return votes
}
