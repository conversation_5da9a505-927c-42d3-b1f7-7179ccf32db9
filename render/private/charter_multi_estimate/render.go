package charter_multi_estimate

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_combo"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_open_products"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/selection/user_pay_info"
	"github.com/spf13/cast"
	"sort"
)

const (
	CharterEstimatePageInfoDcmpKey = "charter-estimate_page_info"

	LogTag = "estimate_render"

	DefaultWeight = 999
)

type Render struct {
	ctx context.Context

	req      *proto.CharterMultiEstimateReq
	pg       *biz_runtime.ProductsGenerator
	products []*biz_runtime.ProductInfoFull

	curProductId      int
	hasDefaultProduct bool

	charterComboHandler       *charter_combo.CharterComboHandler
	charterOpenProductHandler *charter_open_products.CharterOpenProductHandler

	pcIdSort      []int // 配置的车型排序
	pcId2Material map[int]*pcMaterial
}

func NewCharterMultiEstimateRender(ctx context.Context, req *proto.CharterMultiEstimateReq, pg *biz_runtime.ProductsGenerator, products []*biz_runtime.ProductInfoFull) *Render {
	return &Render{
		ctx:      ctx,
		req:      req,
		pg:       pg,
		products: products,
	}
}

func (r *Render) Load() error {
	var err error

	r.curProductId = charter_car.SourceId2ProductId[util.ToInt(r.pg.BaseReqData.CommonInfo.SourceID)]

	r.charterComboHandler = charter_combo.NewCharterComboHandler(r.ctx).SetNeedTimeSlice()
	if err = r.charterComboHandler.Load([]int{r.curProductId}, int(r.pg.BaseReqData.AreaInfo.Area), r.pg.BaseReqData.AreaInfo.District, cast.ToInt64(r.req.DepartureTime), cast.ToInt(r.req.AccessKeyId), r.req.AppVersion); err != nil {
		return fmt.Errorf("load charterComboHandler failed. err=%s", err.Error())
	}

	r.charterOpenProductHandler = r.pg.BaseReqData.CommonBizInfo.CharterOpenProduct

	if r.pcIdSort, err = r.loadPcIdSort(); err != nil {
		return err
	}

	if r.pcId2Material, err = r.loadPcMaterial(); err != nil {
		return err
	}

	return nil
}

func (r *Render) Render() (*proto.CharterMultiEstimateData, error) {
	var (
		err      error
		respData = &proto.CharterMultiEstimateData{}
	)

	r.sort()

	respData.EstimateData = r.getEstimateData()

	if respData.ComboList, err = r.getComboList(); err != nil {
		return nil, err
	}

	if respData.PageInfo, err = r.getPageInfo(); err != nil {
		return nil, err
	}

	respData.ExtraInfo = &proto.CharterEstimateExtra{
		FeeDetailUrl: fee_detail_info.GetDetailUrl(r.ctx),
		CityList:     r.charterOpenProductHandler.GetCityList([]int{util.ToInt(r.pg.BaseReqData.CommonInfo.SourceID)}),
	}

	respData.UserPayInfo = user_pay_info.NewUserPayInfo(r.pg.BaseReqData, r.products).GetUserPayInfo(r.ctx)

	return respData, nil
}

func (r *Render) sort() {
	if r.pcIdSort == nil {
		return
	}

	pcId2Weight := map[int]int{}
	for idx, pcId := range r.pcIdSort {
		pcId2Weight[pcId] = idx
	}

	getWeight := func(pcId int) int {
		if weight, ok := pcId2Weight[pcId]; ok {
			return weight
		}

		return DefaultWeight
	}

	sort.SliceStable(r.products, func(i, j int) bool {
		return getWeight(int(r.products[i].GetProductCategory())) < getWeight(int(r.products[j].GetProductCategory()))
	})
}

func (r *Render) getPageInfo() (*proto.CharterEstimatePageInfo, error) {
	res := &proto.CharterEstimatePageInfo{}
	template := dcmp.GetJSONContentWithPath(r.ctx, CharterEstimatePageInfoDcmpKey, nil, util.ToString(r.curProductId))
	if err := json.Unmarshal([]byte(template), res); err != nil {
		return nil, err
	}

	return res, nil
}
