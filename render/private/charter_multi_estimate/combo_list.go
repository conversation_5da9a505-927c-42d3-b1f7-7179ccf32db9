package charter_multi_estimate

import (
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"sort"
)

const (
	ComboTimeSliceDcmpKey = "charter-time_slice_v2"

	DefaultSelectMark = 1
)

type ComboTimeSliceTmpl struct {
	ComboTitle       string `json:"combo_title"`
	ComboDesc        string `json:"combo_desc"`
	TimePageTitle    string `json:"time_page_title"`
	TimePageSubTitle string `json:"time_page_sub_title"`
}

func (r *Render) getComboList() ([]*proto.ComboBookingDetail, error) {
	conf := map[int]*ComboTimeSliceTmpl{}
	template := dcmp.GetDcmpPlainContent(r.ctx, ComboTimeSliceDcmpKey)
	if err := json.Unmarshal([]byte(template), &conf); err != nil {
		return nil, err
	}
	comboTsTmpl, ok := conf[r.curProductId]
	if comboTsTmpl == nil || !ok {
		return nil, fmt.Errorf("no dcmp conf for cur product id")
	}

	comboId2Package := r.charterComboHandler.GetPackageInfo(r.curProductId)
	comboId2TimeSlice := r.charterComboHandler.GetComboTimeSlice(r.curProductId)
	title, subTitle := r.charterComboHandler.GetTimePageTitles(r.curProductId)

	res := make([]*proto.ComboBookingDetail, 0, len(comboId2Package))

	for comboId, pack := range comboId2Package {
		if pack == nil {
			continue
		}

		ts, ok := comboId2TimeSlice[comboId]
		if !ok || ts == nil {
			continue
		}

		detail := &proto.ComboBookingDetail{
			ComboId:              int32(comboId),
			ComboTitle:           util.ReplaceTag(r.ctx, comboTsTmpl.ComboTitle, map[string]string{"num": util.ToString(pack.Hour)}),
			ComboDesc:            util.ReplaceTag(r.ctx, comboTsTmpl.ComboDesc, map[string]string{"num": util.ToString(pack.Distance)}),
			ComboStartTime:       int32(ts.StartTime),
			ComboEndTime:         int32(ts.EndTime),
			ComboEarliestMinutes: int32(ts.EarliestMinutes),
			ComboDuringDays:      int32(ts.DuringDays),
			ComboTimeGap:         int32(ts.TimeGap),
			TimePageTitle:        title,
			TimePageSubTitle:     subTitle,
		}

		if comboId == int(r.req.ComboId) {
			detail.IsDefaultCombo = DefaultSelectMark
		}

		res = append(res, detail)
	}

	// 按照combo_id正序排序
	sort.Slice(res, func(i, j int) bool { return res[i].ComboId < res[j].ComboId })

	return res, nil
}
