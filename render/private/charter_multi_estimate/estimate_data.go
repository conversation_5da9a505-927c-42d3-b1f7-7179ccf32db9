package charter_multi_estimate

import (
	"encoding/json"
	"git.xiaojukeji.com/gobiz/logger"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3/data"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
)

const (
	CharterFeeMsgDcmpKey = "charter-fee_msg"
)

func (r *Render) getEstimateData() []*proto.CharterEstimateData {
	var (
		err          error
		estimateData = make([]*proto.CharterEstimateData, 0, len(r.products))
	)

	for _, product := range r.products {
		if product == nil || r.pcId2Material == nil {
			continue
		}

		material, ok := r.pcId2Material[int(product.GetProductCategory())]
		if !ok || material == nil {
			continue
		}

		prov := &data.AnyCarV3Adapter{ProductInfoFull: product}

		prod := &proto.CharterEstimateData{
			EstimateId:      product.GetEstimateID(),
			ProductCategory: int32(product.GetProductCategory()),
			CarTitle:        material.CarName,
			CarIcon:         material.CarIcon,
		}

		if material.SubTitleDescStr != "" {
			var desc []string
			err = json.Unmarshal([]byte(material.SubTitleDescStr), &desc)
			if err != nil {
				logger.Warnf(r.ctx, LogTag, "unmarshal sub_title_desc failed str=%s, err=%s", material.SubTitleDescStr, err)
			} else {
				prod.SubTitleDesc = desc
			}
		}

		if material.SubTitleTagStr != "" {
			var tag []*proto.SubTitleTag
			err = json.Unmarshal([]byte(material.SubTitleTagStr), &tag)
			if err != nil {
				logger.Warnf(r.ctx, LogTag, "unmarshal sub_title_tag failed str=%s, err=%s", material.SubTitleTagStr, err)
			} else {
				prod.SubTitleTag = tag
			}
		}

		_, prod.FeeAmount, _ = fee_info_render.GetFeeInfo(r.ctx, prov)
		prod.FeeDescList = fee_info_render.GetPriceInfoDescList(r.ctx, prov)
		prod.ExtraMap = &proto.NewFormExtraMap{
			LevelType:    prov.GetLevelType(),
			BusinessId:   prov.GetBusinessID(),
			ComboType:    prov.GetComboType(),
			RequireLevel: prov.GetRequireLevel(),
		}

		feeTemplate := dcmp.GetDcmpPlainContent(r.ctx, CharterFeeMsgDcmpKey)
		prod.FeeMsg = dcmp.TranslateTemplate(feeTemplate, map[string]string{"amount": prod.FeeAmount})

		if r.req.LastPcId == prod.ProductCategory {
			prod.IsDefaultSelect = DefaultSelectMark
			r.hasDefaultProduct = true
		}

		prod.UserPayInfo = anycar_v3.GetPayInfo(r.ctx, prov)

		estimateData = append(estimateData, prod)
	}

	if len(estimateData) > 0 && !r.hasDefaultProduct {
		estimateData[0].IsDefaultSelect = DefaultSelectMark
	}

	return estimateData
}
