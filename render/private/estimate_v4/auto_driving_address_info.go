package estimate_v4

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/render"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type AutoDrivingProvider interface {
	render.ProductProvider
	GetRecResultExtraInfo() map[string]string
}

func GetAutoDrivingAddressInfo(ctx context.Context, prov AutoDrivingProvider) *proto.NewFormAutoDriveRedirectionInfo {
	if prov.GetProductCategory() != estimate_pc_id.EstimatePcIdAutoDriving {
		return nil
	}

	extraInfo := prov.GetRecResultExtraInfo()
	if extraInfo == nil {
		return nil
	}
	redirectionInfo, ok := extraInfo["redirection_info"]
	if !ok {
		return nil
	}

	var ret *proto.NewFormAutoDriveRedirectionInfo
	err := json.Unmarshal([]byte(redirectionInfo), ret)
	if err != nil {
		return nil
	}

	return ret
}
