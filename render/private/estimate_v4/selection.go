package estimate_v4

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"strconv"
)

type SelectionProvider interface {
	GetProductCategory() int64
	GetMultiRequireProduct() []models.RequireProduct
	GetProductCheckStatus() int

	render.ApolloProvider
}

var ProductInfos = make(map[string]int)

func GetSelection(ctx context.Context, prov SelectionProvider) int32 {
	// athena推荐
	if prov.GetProductCheckStatus() == consts.Checked {
		return consts.Checked
	}

	// 用户勾选
	multiRequireProduct := prov.GetMultiRequireProduct()
	if len(prov.GetMultiRequireProduct()) > 0 {
		for _, product := range multiRequireProduct {
			if prov.GetProductCategory() == product.ProductCategory && product.IsSelected == consts.Checked {
				return consts.Checked
			}

			// 豪华车勾选继承
			if _, ok := ProductInfos[strconv.FormatInt(prov.GetProductCategory(), 10)]; ok {
				hasSelectedNormalLux, hasSelectedSixSeatLux := luxSelectedInfo(ctx, prov)
				if supportSixLux(ctx, prov) {
					key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
					isSixLux := apollo.FeatureToggle(ctx, "six_seat_lux_judge", key, params)
					if isSixLux && hasSelectedSixSeatLux {
						return consts.Checked
					}
					if !isSixLux && hasSelectedNormalLux {
						return consts.Checked
					}
				} else {
					if hasSelectedNormalLux || hasSelectedSixSeatLux {
						return consts.Checked
					}
				}
			}
		}
	}

	return consts.UnChecked
}

func luxSelectedInfo(ctx context.Context, prov SelectionProvider) (bool, bool) {
	var (
		multiRequireProduct = prov.GetMultiRequireProduct()
		selectedNormalLux   = false
		selectedSixSeatLux  = false
	)

	if len(multiRequireProduct) > 0 {
		for _, product := range multiRequireProduct {
			// 已勾选豪华车
			if _, ok := ProductInfos[strconv.FormatInt(product.ProductCategory, 10)]; ok && product.IsSelected == 1 {
				key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
				isSixSeatLux := apollo.FeatureToggle(ctx, "six_seat_lux_judge", key, params)
				if isSixSeatLux {
					selectedSixSeatLux = true
				} else {
					selectedNormalLux = true
				}
			}
		}
	}

	return selectedNormalLux, selectedSixSeatLux
}

func supportSixLux(ctx context.Context, prov SelectionProvider) bool {
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "six_seat_lux_toggle", key, params)
}

func InitLuxInfoMap() error {
	luxuryConfig := apollo.GetConfig(nil, "luxury_carlevel_product_config", "luxury_carlevel_product_config")
	if luxuryConfig != nil {
		productInfoRaw := luxuryConfig["product_category"]
		_ = json.Unmarshal([]byte(productInfoRaw), &ProductInfos)
	}
	return nil
}
