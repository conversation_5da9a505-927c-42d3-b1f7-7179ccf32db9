package omage_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type OmageInfo struct {
	product *biz_runtime.ProductInfoFull
}

// NewOmageInfo ...
func NewOmageInfo(product *biz_runtime.ProductInfoFull) *OmageInfo {
	return &OmageInfo{
		product: product,
	}
}

// Render ...
func (rc *OmageInfo) Render(ctx context.Context) *proto.BusDetailOmegaInfo {
	return rc.getOmageInfo(ctx)
}

// getOmageInfo ...
func (rc *OmageInfo) getOmageInfo(ctx context.Context) *proto.BusDetailOmegaInfo {
	omageInfo := new(proto.BusDetailOmegaInfo)
	omageCommonParams := new(proto.BusDetailOmegaCommonParams)
	omageCommonParams.PageType = rc.product.BaseReqData.CommonInfo.PageType
	omageInfo.CommonParams = omageCommonParams
	if rc.product.Product.BizInfo.RouteDetailV2 != nil && rc.product.Product.BizInfo.RouteDetailV2.RouteBasicInfo != nil {
		omageInfo.RouteSceneType = rc.product.Product.BizInfo.RouteDetailV2.RouteBasicInfo.RouteSceneType
	}
	return omageInfo
}
