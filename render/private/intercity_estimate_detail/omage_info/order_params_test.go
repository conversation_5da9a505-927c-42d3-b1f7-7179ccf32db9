package omage_info

import (
	"context"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetOmageInfo(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					RouteDetailV2: &models.RouteDetail{
						RouteBasicInfo: &Prfs.RouteBasicInfo{
							EarlyPickUpTimeEta: 30,
							StationList: []*Prfs.StationInfo{
								{
									StationId:        1,
									StationSceneType: 1,
								},
								{
									StationId: 2,
								},
							},
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				DepartureTime: 1750237876,
			},
			CommonBizInfo: models.CommonBizInfo{
				StartStationId: 1,
				EndStationId:   2,
			},
		},
	}
	res := NewOmageInfo(product).getOmageInfo(ctx)
	assert.NotNil(t, res.RouteSceneType)
}
