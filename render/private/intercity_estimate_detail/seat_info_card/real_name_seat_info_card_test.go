package seat_info_card

import (
	"context"
	"encoding/json"
	"testing"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

// 确保proto包被使用
var _ *proto.NewFormFeeDesc = nil

func createMockRealData(realJSON, key string) map[string]gjson.Result {
	text := map[string]gjson.Result{
		key: gjson.Result{
			Type: gjson.JSON,
			Raw:  realJSON,
		},
	}

	return text[key].Map()
}

// 创建模拟的realNameSeatInfoCard
func createMockRealNameSeatInfoCard(newPageSwitch bool) *realNameSeatInfoCard {
	mockProduct := createProduct()
	mockText := createMockRealData(`{
    "passenger_max_num": 20,
    "disable_toast": "业主票不支持编辑",
    "passenger_limit_toast": "当前班次最多可选{{num}}人",
    "carry_children_limit_toast": "当前班次携童最多可选{{num}}人",
    "carry_children_zero_limit_toast": "当前班次携童已售罄",
    "passenger_list_func_button": {
        "text": "+ 新增乘车人",
        "link_url": "https://page.udache.com/passenger/apps/coach-autonym-intercept/index.html#/pages/edit"
    },
    "add_passenger_button": {
        "text": "+ 新增乘车人",
        "link_url": "https://page.udache.com/passenger/apps/coach-autonym-intercept/index.html#/pages/add"
    },
    "more_button": {
        "text": "更多",
        "text_v2": "新增",
        "limit_num": 3,
        "link_url": "https://page.udache.com/passenger/apps/coach-autonym-intercept/index.html#/pages/edit"
    },
    "choose_passenger_detail": {
        "title": "请选择乘车人",
        "trunc_limit": 3
    },
    "selected_passenger_detail": {
      "default_title": "请选择乘车人",
        "title": "已选{{num}}人",
        "fee_msg": "{{{ticketPrice}}}元",
        "card_fee_desc": {
            "icon": "https://s3-gz01.didistatic.com/packages-mait/img/cUT5tV2YLK1725078530554.png",
            "content": "{{{fee}}}元",
            "border_color": "#FF754B",
            "text_color": "#FF754B"
        },
        "link_url": "https://page.udache.com/passenger/apps/coach-autonym-intercept/index.html#/pages/add",
        "discount_msg_list": {
            "2": {
                "half_price": {
                    "content": "儿童半价优惠",
                    "text_color": "#000000",
                    "border_color": "#000000"
                },
                "default": {
                    "content": "儿童{{discount}}折优惠",
                    "text_color": "#000000",
                    "border_color": "#000000"
                }
            },
            "7": {
                "half_price": {
                    "content": "优待半价优惠",
                    "text_color": "#000000",
                    "border_color": "#000000"
                },
                "default": {
                    "content": "优待{{discount}}折优惠",
                    "text_color": "#000000",
                    "border_color": "#000000"
                }
            }
        },
        "sub_title_list": {
            "1": {
                "content": "成人",
                "text_color": "#000000",
                "border_color": "#000000"
            },
            "2": {
                "content": "儿童",
                "text_color": "#000000",
                "border_color": "#000000"
            },
            "4": {
                "content": "业主成人",
                "text_color": "#000000",
                "border_color": "#000000"
            },
            "5": {
                "content": "业主儿童",
                "text_color": "#000000",
                "border_color": "#000000"
            },
            "6": {
                "content": "业主老人",
                "text_color": "#000000",
                "border_color": "#000000"
            },
            "7": {
                "content": "优待",
                "text_color": "#000000",
                "border_color": "#000000"
            }
        }
    },
    "no_real_name": {
        "3": {
            "title": "携童票",
            "sub_title": "6周岁及1.2m以下的儿童",
            "is_occupy": {
                "content": "提供座位",
                "border_color": "#FF754B",
                "text_color": "#FF754B"
            },
            "no_occupy": {
                "content": "不提供座位",
                "border_color": "#FF754B",
                "text_color": "#FF754B"
            }
        }
    },
    "subtitle": "{该路线统一票价 不区分成人儿童}",
    "new_default_subtitle": "{该班次统一定价 无乘客类型优惠}",
    "ticket_type_subtitle": {
        "child_preferential": "{该班次儿童、优待可享优惠价}",
        "child": "{该班次儿童可享优惠价}",
        "preferential": "{该班次优待可享优惠价}"
    }
}`, "real_name_seat_info_card")
	req := &proto.IntercityEstimateDetailRequest{}
	rns := NewRealNameSeatInfoCard(mockProduct, mockText, newPageSwitch, req)
	// 初始化supportTicketTypes
	rns.supportTicketTypes = make(map[int32]bool)

	return rns
}

func parseRule() (*dirpcSdkBrick.RuleData, error) {
	jsonStr := `{
        "special_seat_rules": [
            {
                "rule_name": "child_ticket",
                "is_support": 1,
                "type_percent": [
                    {
                        "type": 2,
                        "percent": 50,
                        "is_filter": 0
                    }
                ],
                "occupy_seat": 1
            },
			{
                "rule_name": "coupon_ticket",
                "is_support": 1,
                "type_percent": [
                    {
                        "type": 7,
                        "percent": 50,
                        "is_filter": 0
                    }
                ],
                "occupy_seat": 0
            },
            {
                "rule_name": "infant_ticket",
                "is_support": 1,
                "type_percent": [
                    {
                        "type": 3,
                        "percent": 0,
                        "is_filter": 0
                    }
                ],
                "occupy_seat": 0
            }
        ]
    }`

	var rules *dirpcSdkBrick.RuleData
	err := json.Unmarshal([]byte(jsonStr), &rules)
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func createProduct() *biz_runtime.ProductInfoFull {
	ruleData, err := parseRule()
	if err != nil {
		ruleData = &dirpcSdkBrick.RuleData{}
	}

	//sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo = nil
	return &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					IntercityRule: &ticketPrice.RuleData{
						RebookChargeRule: &ticketPrice.RebookChargeRuleData{
							RebookRuleId: util.StringPtr("default7"),
							RuleName:     util.StringPtr("可改签改签后不可退"),
							RuleDetail: &ticketPrice.RebookChargeRuleDetail{
								MaxRebookTimes: 1,
								ChargeRate: []*ticketPrice.RebookChargeRate{
									{Times: 1,
										Rate: 100},
								},
							},
						},
						RefundChargeRule: &ticketPrice.RefundChargeRuleData{
							BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
								{
									LeftTimeInterval:  24,
									RightTimeInterval: 48,
									RefundChargeRate:  50,
								},
							},
							TimeUnit: util.String2PtrString("hour"),
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				PassengerDetailInfo: &models.PassengerDetailInfo{
					Mode: 1,
					PassengerList: []*models.PassengerItem{
						{
							TicketType:     1,
							PassengerCount: 1,
							IdentityType:   1,
							IdentityName:   "大人",
							IdentityID:     "110113201205222699",
						},
					},
				},
				//IdentityPageInfo.IdentityHistoryInfo
				IsBestShift: 1,
				IdentityPageInfo: &dirpcSdkBrick.IdentityPageInfoData{
					IdentityHistoryInfo: []*dirpcSdkBrick.IdentityItem{
						{
							Id:           1,
							Name:         "test",
							IdentityType: 1,
							IdentityNo:   "123456789012345678",
							Type:         1,
						},
						{
							Id:           1,
							Name:         "过期儿童",
							IdentityType: 1,
							IdentityNo:   "123456789012345678",
							Type:         2,
							Expired:      1,
						},
					},
					RuleInfo: ruleData,
				},
				RuleData: ruleData,
			},
		},
	}
}

// 设置所需的依赖项模拟
func setupRealNameMockDependencies(t *testing.T) []*mockey.Mocker {
	patches := []*mockey.Mocker{}

	// Mock util.StringPtr
	patchStringPtr := mockey.Mock(util.StringPtr).To(func(s string) *string {
		return &s
	}).Build()
	patches = append(patches, patchStringPtr)

	// Mock util.Int32Ptr
	patchInt32Ptr := mockey.Mock(util.Int32Ptr).To(func(i int32) *int32 {
		return &i
	}).Build()
	patches = append(patches, patchInt32Ptr)

	// Mock CarryChildren.ToInt32
	patchCarryChildrenToInt32 := mockey.Mock(seat_selection_consts.CarryChildren.ToInt32).Return(int32(3)).Build()
	patches = append(patches, patchCarryChildrenToInt32)

	// Mock util.Int32String
	patchInt32String := mockey.Mock(util.Int32String).To(func(i int32) string {
		return "3" // 假设CarryChildren的ToInt32返回3
	}).Build()
	patches = append(patches, patchInt32String)

	return patches
}

// 准备模拟的gjson数据
func prepareRealNameMockGjsonData(t *testing.T) []*mockey.Mocker {
	patches := []*mockey.Mocker{}

	// Mock gjson.Map
	patchGjsonResultMap := mockey.Mock((*gjson.Result).Map).To(func(r *gjson.Result) map[string]gjson.Result {
		if r == nil {
			return map[string]gjson.Result{}
		}

		// 创建不同场景下的返回值
		mockData := map[string]gjson.Result{
			"3":      {}, // 对应携童票类型
			NoOccupy: {},
			IsOccupy: {},
		}

		return mockData
	}).Build()
	patches = append(patches, patchGjsonResultMap)

	// Mock gjson.String
	patchGjsonResultString := mockey.Mock((*gjson.Result).String).To(func(r *gjson.Result) string {
		return "mock_value"
	}).Build()
	patches = append(patches, patchGjsonResultString)

	return patches
}

func TestBuildNoRealNamePassengerList(t *testing.T) {
	// 设置所有依赖项模拟
	patches := setupRealNameMockDependencies(t)
	gjsonPatches := prepareRealNameMockGjsonData(t)
	patches = append(patches, gjsonPatches...)

	defer func() {
		for _, p := range patches {
			p.UnPatch()
		}
	}()

	ctx := context.Background()

	t.Run("测试 - 文本为空时返回nil", func(t *testing.T) {
		// 准备 - 文本为空的场景
		rns := createMockRealNameSeatInfoCard(false)
		rns.text = nil

		// 执行
		result := rns.buildNoRealNamePassengerList(ctx)

		// 断言
		assert.Nil(t, result, "当text为nil时应返回nil")
	})

	t.Run("测试 - 不支持携童票类型时返回nil", func(t *testing.T) {
		// 准备
		rns := createMockRealNameSeatInfoCard(false)
		rns.text = make(map[string]gjson.Result)
		rns.text["no_real_name"] = gjson.Result{}
		// 不支持携童票类型
		rns.supportTicketTypes = map[int32]bool{
			1: true, // 假设1是成人票类型
		}

		// 执行
		result := rns.buildNoRealNamePassengerList(ctx)

		// 断言
		assert.Nil(t, result, "当不支持携童票类型时应返回nil")
	})

	t.Run("测试 - 支持携童票且不占座时返回正确的PassengerItem", func(t *testing.T) {
		// 准备
		rns := createMockRealNameSeatInfoCard(false)
		rns.text = make(map[string]gjson.Result)
		rns.text["no_real_name"] = gjson.Result{}
		// 支持携童票类型
		rns.supportTicketTypes = map[int32]bool{
			3: true, // 携童票类型
		}
		// 设置不占座
		rns.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat = 0

		// 执行
		result := rns.buildNoRealNamePassengerList(ctx)

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个PassengerItem")
		assert.Equal(t, int32(3), result[0].TicketType, "TicketType应为携童票类型")
		assert.Equal(t, int32(0), *result[0].PassengerCount, "PassengerCount应为0")
		assert.NotNil(t, result[0].SubtitleList, "SubtitleList不应为空")
		assert.Len(t, result[0].SubtitleList, 1, "SubtitleList应有一个元素")
	})

	t.Run("测试 - 支持携童票且占座时返回正确的PassengerItem", func(t *testing.T) {
		// 准备
		rns := createMockRealNameSeatInfoCard(false)
		rns.text = make(map[string]gjson.Result)
		rns.text["no_real_name"] = gjson.Result{}
		// 支持携童票类型
		rns.supportTicketTypes = map[int32]bool{
			3: true, // 携童票类型
		}
		// 设置占座
		rns.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat = 1

		// 执行
		result := rns.buildNoRealNamePassengerList(ctx)

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个PassengerItem")
		assert.Equal(t, int32(3), result[0].TicketType, "TicketType应为携童票类型")
		assert.Equal(t, int32(0), *result[0].PassengerCount, "PassengerCount应为0")
		assert.NotNil(t, result[0].SubtitleList, "SubtitleList不应为空")
		assert.Len(t, result[0].SubtitleList, 1, "SubtitleList应有一个元素")
	})

	t.Run("测试 - carryChildrenPassengerInfo不为nil时正确设置PassengerCount", func(t *testing.T) {
		// 准备
		rns := createMockRealNameSeatInfoCard(false)
		rns.text = make(map[string]gjson.Result)
		rns.text["no_real_name"] = gjson.Result{}
		// 支持携童票类型
		rns.supportTicketTypes = map[int32]bool{
			3: true, // 携童票类型
		}
		// 设置carryChildrenPassengerInfo
		rns.carryChildrenPassengerInfo = &models.PassengerItem{
			PassengerCount: 2,
		}

		// 执行
		result := rns.buildNoRealNamePassengerList(ctx)

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个PassengerItem")
		assert.Equal(t, int32(2), *result[0].PassengerCount, "PassengerCount应为2")
	})
}

func TestGetIdentityDiscountMap(t *testing.T) {
	ctx := context.Background()

	t.Run("测试 01- product为nil", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockSeatInfoCard()
		sic.product = nil

		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 0, "当product为nil时长度为0")
	})

	t.Run("测试 02- IdentityPageInfo为空", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo = nil
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 0, "当IdentityPageInfo为nil时长度为0")
	})
	t.Run("测试 03- 涵盖规则信息", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.NotNil(t, sic.hasDiscountSupport)
		assert.Equal(t, sic.hasDiscountSupport[2].HasDiscountSupport, true)
		assert.NotZero(t, sic.hasDiscountSupport[2].DiscountRatio, "费率不可为0")
	})
	t.Run("测试 04- 规则内部为nil", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0] = nil
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 1)
		assert.Equal(t, sic.hasDiscountSupport[7].HasDiscountSupport, true)
	})

	t.Run("测试 05- 规则内部为值为0", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		//sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent[0].Type = 9
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent[0].Percent = 0
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 2)
		assert.Equal(t, sic.hasDiscountSupport[2].HasDiscountSupport, false)
		assert.Equal(t, sic.hasDiscountSupport[2].DiscountRatio, float64(0))
	})

	t.Run("测试 06- 规则type不符", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent[0].Type = 9
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent[0].Percent = 0
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 2)
		assert.Equal(t, sic.hasDiscountSupport[2].HasDiscountSupport, true)
		assert.Equal(t, sic.hasDiscountSupport[2].DiscountRatio, float64(0))
	})

	t.Run("测试 07- 规则值100", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent[0].Percent = 100
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 2)
		assert.Equal(t, sic.hasDiscountSupport[2].HasDiscountSupport, false)
		assert.Equal(t, sic.hasDiscountSupport[2].DiscountRatio, float64(0))
	})

	t.Run("测试 08- parent为0", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent = nil
		// 执行
		sic.getIdentityDiscountMap(ctx)
		// 断言
		assert.Len(t, sic.hasDiscountSupport, 2)
		assert.Equal(t, sic.hasDiscountSupport[2].HasDiscountSupport, false)
		assert.Equal(t, sic.hasDiscountSupport[2].DiscountRatio, float64(0))
	})
}

// getRealNameSeatInfoCard
func TestGetRealNameSeatInfoCard(t *testing.T) {
	ctx := context.Background()

	t.Run("测试 01- 构建支持优惠", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules[0].TypePercent = nil
		// 执行
		sic.getRealNameSeatInfoCard(ctx)
		// 断言
		assert.NotNil(t, sic.hasDiscountSupport, "规则需要有可支持的内容")
	})

	t.Run("测试 01-1- 文本空", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.text = nil
		// 执行
		result := sic.getRealNameSeatInfoCard(ctx)
		// 断言
		assert.Nil(t, result, "缺少文本")
	})

	t.Run("测试 02- 开关没开正常展示", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(false)
		// 执行
		result := sic.buildChoosePassengerDetail(ctx)
		// 断言
		assert.NotNil(t, result, "开关没开正常展示")
	})

	t.Run("测试 03- 未涵盖添加按钮", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		// 执行
		result := sic.buildSelectPassengerDetail(ctx)
		// 断言
		assert.NotNil(t, result, "需要涵盖按钮")
		assert.NotNil(t, result.PassengerListButton, "按钮不可为空")
	})

	t.Run("测试 04- ", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.text = nil
		// 执行
		result := sic.buildSelectPassengerDetail(ctx)
		// 断言
		assert.Nil(t, result, "text is nil")
	})

	t.Run("测试 05- ", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.checkedPassengerList = []*proto.PassengerItem{
			{
				PassengerName: util.StringPtr("大壮儿童"),
				TicketType:    2,
				IdentityId:    util.StringPtr("123123444444999999"),
			},
		}
		sic.checkNum = 1
		// 执行
		result := sic.buildSelectPassengerDetail(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, result.Title, "已选1人")
		assert.Len(t, sic.checkedPassengerList, 1)
	})
	t.Run("测试 06- ", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.checkedPassengerList = make([]*proto.PassengerItem, 0)
		// 执行
		result := sic.buildSelectPassengerDetail(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, result.Title, "请选择乘车人")
	})
	t.Run("测试 07- ", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(false)
		sic.checkedPassengerList = make([]*proto.PassengerItem, 0)
		// 执行
		result := sic.buildSelectPassengerDetail(ctx)
		// 断言
		assert.Nil(t, result, "非新版依旧为nil")
	})

	t.Run("测试 027- 文本为nil", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.text = nil
		// 执行
		result := sic.buildChoosePassengerDetail(ctx)
		// 断言
		assert.Nil(t, result, "文本为空")
	})
}

// getRealNameSeatInfoCard
func TestBuildNewSubTitle(t *testing.T) {
	ctx := context.Background()
	sic := createMockRealNameSeatInfoCard(true)
	// 执行
	sic.getIdentityDiscountMap(ctx)

	t.Run("测试 01- 儿童优待都支持", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic.hasDiscountSupport[2].HasDiscountSupport = true
		sic.hasDiscountSupport[2].DiscountRatio = 10
		sic.hasDiscountSupport[7].HasDiscountSupport = true
		sic.hasDiscountSupport[7].DiscountRatio = 20
		result := sic.buildNewSubTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, *result, "{该班次儿童、优待可享优惠价}", "值未匹配")
	})
	t.Run("测试 02- 只支持儿童", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic.hasDiscountSupport[2].HasDiscountSupport = true
		sic.hasDiscountSupport[2].DiscountRatio = 10
		sic.hasDiscountSupport[7].HasDiscountSupport = false
		sic.hasDiscountSupport[7].DiscountRatio = 0
		result := sic.buildNewSubTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, *result, "{该班次儿童可享优惠价}", "儿童需要有优惠")
	})
	t.Run("测试 023- 只支持优待", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic.hasDiscountSupport[2].HasDiscountSupport = false
		sic.hasDiscountSupport[2].DiscountRatio = 0
		sic.hasDiscountSupport[7].HasDiscountSupport = true
		sic.hasDiscountSupport[7].DiscountRatio = 20
		result := sic.buildNewSubTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, *result, "{该班次优待可享优惠价}", "班次需要支持优待")
	})
	t.Run("测试 023- 无任何匹配", func(t *testing.T) {
		temp := map[int32]*model.IdentityDiscountInfo{}
		temp[999] = &model.IdentityDiscountInfo{}
		sic.hasDiscountSupport = temp
		result := sic.buildNewSubTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, *result, "{该班次统一定价 无乘客类型优惠}", "班次统一定价描述")
	})
	t.Run("测试 024- 判断走新开关", func(t *testing.T) {
		result := sic.buildSubTitleList(ctx, &dirpcSdkBrick.IdentityItem{
			Id:           1,
			Name:         "test",
			IdentityType: 1,
			IdentityNo:   "123456789012345678",
			Type:         1,
		})
		// 断言
		assert.Nil(t, result, "must be nil")
	})
}

// TestBuildPassListButton
func TestBuildPassListButton(t *testing.T) {
	ctx := context.Background()
	t.Run("测试 01- 按钮构建", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		result := sic.passengerListButton(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, result.Text, "+ 新增乘车人", "按钮文案不匹配")
		assert.NotNil(t, result, result.LinkUrl)
	})
	t.Run("测试 02- 无文案", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(false)
		sic.text = nil
		result := sic.passengerListButton(ctx)
		// 断言
		assert.Nil(t, result, "be nil")
	})

}

// TestDiscountMsg
func TestDiscountMsg(t *testing.T) {
	sic := createMockRealNameSeatInfoCard(true)

	t.Run("测试 01- 成人不展示", func(t *testing.T) {
		result := sic.isDiscountRatio(&dirpcSdkBrick.IdentityItem{
			Type: 1,
		})
		// 断言
		assert.False(t, result, "不展示成人")
	})
	t.Run("测试 02- 配置为空", func(t *testing.T) {
		sic.text = make(map[string]gjson.Result)
		result := sic.isDiscountRatio(&dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.False(t, result, "未匹配配置需要为false")
	})
	t.Run("测试 03- 涵盖匹配的类型", func(t *testing.T) {
		ctx := context.Background()
		sicT := createMockRealNameSeatInfoCard(true)
		sicT.getIdentityDiscountMap(ctx)
		result := sicT.isDiscountRatio(&dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.True(t, result, "有儿童的优惠")
	})
	t.Run("测试 04- 无任何异常的兜底", func(t *testing.T) {
		ctx := context.Background()
		sic.getIdentityDiscountMap(ctx)
		result := sic.isDiscountRatio(&dirpcSdkBrick.IdentityItem{
			Type: 99,
		})
		// 断言
		assert.False(t, result, "兜底策略")
	})
}

// TestBuildDiscountMsg
func TestBuildDiscountMsg(t *testing.T) {
	ctx := context.Background()

	t.Run("测试 01- 儿童费率为0 ", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(false)
		sic.getIdentityDiscountMap(ctx)
		sic.hasDiscountSupport[2].DiscountRatio = 0
		result := sic.buildDiscountMsg(ctx, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.Nil(t, result, "must be nil")
	})
	t.Run("测试 01- 儿童费率不为0也不是5折 ", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		sic.hasDiscountSupport[2].DiscountRatio = 2
		result := sic.buildDiscountMsg(ctx, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, result.Content, "儿童2折优惠")
	})

	t.Run("测试 01- 儿童费率不为0也不是5折 ", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		sic.hasDiscountSupport[2].DiscountRatio = 5
		result := sic.buildDiscountMsg(ctx, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.NotNil(t, result, "not nil")
		assert.Equal(t, result.Content, "儿童半价优惠")
	})

	t.Run("测试 01- 成人构建 ", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		result := sic.buildDiscountMsg(ctx, &dirpcSdkBrick.IdentityItem{
			Type: 1,
		})
		// 断言
		assert.Nil(t, result, "must be nil")
	})
}

// TestIsShowSubtitle
func TestIsShowSubtitle(t *testing.T) {
	ctx := context.Background()
	sic := createMockRealNameSeatInfoCard(false)
	sic.getIdentityDiscountMap(ctx)
	t.Run("测试 01- 不展示 ", func(t *testing.T) {
		result := sic.isShowSubtitle(false, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.False(t, result, "must be false")
	})
	t.Run("测试 02- 不展示 ", func(t *testing.T) {
		result := sic.isShowSubtitle(true, &dirpcSdkBrick.IdentityItem{
			Type: 1,
		})
		// 断言
		assert.False(t, result, "must be false")
	})
	t.Run("测试 03- 不展示 ", func(t *testing.T) {
		sic.text = make(map[string]gjson.Result)
		result := sic.isShowSubtitle(true, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.False(t, result, "must be false")
	})

	t.Run("测试 04- 不展示 ", func(t *testing.T) {
		sic.text = make(map[string]gjson.Result)
		result := sic.isShowSubtitle(true, &dirpcSdkBrick.IdentityItem{
			Type: 99,
		})
		// 断言
		assert.False(t, result, "must be false")
	})

	t.Run("测试 05- 展示 ", func(t *testing.T) {
		sic = createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		result := sic.isShowSubtitle(true, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.True(t, result, "must be true")
	})
	t.Run("测试 05- 展示 ", func(t *testing.T) {
		sic = createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		result := sic.isShowSubtitle(true, &dirpcSdkBrick.IdentityItem{
			Type: 88,
		})
		// 断言
		assert.False(t, result, "must be false")
	})

}

// TestBuildNewSubtitleList
func TestBuildNewSubtitleList(t *testing.T) {
	ctx := context.Background()
	t.Run("测试 01- 构建成功", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(false)
		sic.getIdentityDiscountMap(ctx)
		result := sic.buildNewSubtitleList(ctx, true, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.NotNil(t, result, "not nil")
	})
	t.Run("测试 02- 直接返回", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(false)
		sic.getIdentityDiscountMap(ctx)
		result := sic.buildNewSubtitleList(ctx, false, &dirpcSdkBrick.IdentityItem{
			Type: 2,
		})
		// 断言
		assert.Nil(t, result, "nil")
	})
	t.Run("测试 02- 直接返回", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		sic.handleCheck(ctx)
	})
	t.Run("测试 02- 直接返回", func(t *testing.T) {
		sic := createMockRealNameSeatInfoCard(true)
		sic.getIdentityDiscountMap(ctx)
		sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.IdentityHistoryInfo[0] = nil
		sic.handleCheck(ctx)
	})

}
