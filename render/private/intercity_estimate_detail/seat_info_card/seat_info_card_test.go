package seat_info_card

import (
	"context"
	"encoding/json"
	"testing"

	"git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

// 确保proto包被使用
var _ *proto.NewFormFeeDesc = nil

// 创建模拟的运行时数据
func createMockSeatInfoCard() *seatInfoCard {
	mockProduct := &biz_runtime.ProductInfoFull{
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				CarryChildrenIsOccupySeat: 0, // 默认不占座
			},
		},
	}

	mockText := make(map[string]gjson.Result)
	mockSupportInfo := &intercity_estimate_detail.SupportInfo{}

	sic := NewSeatInfoCard(mockProduct, mockText, mockSupportInfo, false)
	return sic
}

// 设置所需的依赖项模拟
func setupMockDependencies(t *testing.T) []*mockey.Mocker {
	patches := []*mockey.Mocker{}

	// Mock util.Int642String
	patchInt642String := mockey.Mock(util.Int642String).To(func(i int64) string {
		return "10"
	}).Build()
	patches = append(patches, patchInt642String)

	// Mock util.StringPtr
	patchStringPtr := mockey.Mock(util.StringPtr).To(func(s string) *string {
		return &s
	}).Build()
	patches = append(patches, patchStringPtr)

	// Mock util.ReplaceTag
	patchReplaceTag := mockey.Mock(util.ReplaceTag).To(func(ctx context.Context, content string, tags map[string]string) string {
		return "替换后的内容"
	}).Build()
	patches = append(patches, patchReplaceTag)

	// Mock decimal.NewFromFloat
	patchDecimalNewFromFloat := mockey.Mock(decimal.NewFromFloat).To(func(f float64) decimal.Decimal {
		mockDecimal := decimal.New(1, 0) // 创建值为1的Decimal
		return mockDecimal
	}).Build()
	patches = append(patches, patchDecimalNewFromFloat)

	// Mock decimal.Round
	patchDecimalRound := mockey.Mock((*decimal.Decimal).Round).To(func(d *decimal.Decimal, places int32) decimal.Decimal {
		return *d
	}).Build()
	patches = append(patches, patchDecimalRound)

	// Mock decimal.String
	patchDecimalString := mockey.Mock((*decimal.Decimal).String).To(func(d *decimal.Decimal) string {
		return "1.0"
	}).Build()
	patches = append(patches, patchDecimalString)

	// Mock CarryChildren.ToInt64
	patchCarryChildrenToInt64 := mockey.Mock(seat_selection_consts.CarryChildren.ToInt64).Return(int64(3)).Build()
	patches = append(patches, patchCarryChildrenToInt64)

	return patches
}

// 准备模拟的gjson数据
func prepareMockGjsonData(t *testing.T) []*mockey.Mocker {
	patches := []*mockey.Mocker{}

	// Mock gjson.Map
	patchGjsonResultMap := mockey.Mock((*gjson.Result).Map).To(func(r *gjson.Result) map[string]gjson.Result {
		if r == nil {
			return map[string]gjson.Result{}
		}

		// 创建不同场景下的返回值
		mockData := map[string]gjson.Result{
			"default":  {}, // 默认结果
			"10":       {}, // 对应 seatRule.Percent
			CarryChild: {},
			IsOccupy:   {},
			NoOccupy:   {},
		}

		return mockData
	}).Build()
	patches = append(patches, patchGjsonResultMap)
	// Mock gjson.String
	patchGjsonResultString := mockey.Mock((*gjson.Result).String).To(func(r *gjson.Result) string {
		return "mock_value"
	}).Build()
	patches = append(patches, patchGjsonResultString)

	return patches
}

func TestBuildSubTitleList(t *testing.T) {
	// 设置所有依赖项模拟
	patches := setupMockDependencies(t)
	gjsonPatches := prepareMockGjsonData(t)
	patches = append(patches, gjsonPatches...)

	defer func() {
		for _, p := range patches {
			p.UnPatch()
		}
	}()

	ctx := context.Background()

	t.Run("测试 - 文本为空时返回nil", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockSeatInfoCard()
		sic.text = nil

		// 执行
		result := sic.buildSubTitleList(ctx, 1)

		// 断言
		assert.Nil(t, result, "当text为nil时应返回nil")
	})

	t.Run("测试 - 价格相关标签展示", func(t *testing.T) {
		// 准备
		sic := createMockSeatInfoCard()
		sic.text = make(map[string]gjson.Result)
		sic.text["sub_title_list"] = gjson.Result{
			Str: "替换后的内容",
		}

		// 模拟ruleInfoMap
		sic.ruleInfoMap = map[int64]*Dirpc_SDK_Brick.TypePercentList{
			1: {
				Type:    1,
				Percent: 10, // 百分比为10
			},
		}

		// 执行
		result := sic.buildSubTitleList(ctx, 1)

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个标签")
		assert.Equal(t, "替换后的内容", result[0].Content, "替换后的内容")
	})

	t.Run("测试 - 携童票场景 - 不占座", func(t *testing.T) {
		// 准备
		sic := createMockSeatInfoCard()
		sic.text = make(map[string]gjson.Result)
		sic.text["normal_sub_title_list"] = createCarryChildGjsonResult()

		// 设置不占座
		sic.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat = 0

		// 执行
		result := sic.buildSubTitleList(ctx, 3) // 3 是 CarryChildren 的模拟值

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个标签")
		assert.Equal(t, "不提供座位", result[0].Content, "文案错误")
		assert.NotNil(t, result[0].BorderColor, "没有边框")
		assert.NotNil(t, result[0].TextColor, "缺颜色")
	})

	t.Run("测试 - 携童票场景 - 占座", func(t *testing.T) {
		// 准备
		sic := createMockSeatInfoCard()
		sic.text = make(map[string]gjson.Result)
		sic.text["normal_sub_title_list"] = createCarryChildGjsonResult()

		// 设置占座
		sic.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat = 1

		// 执行
		result := sic.buildSubTitleList(ctx, 3) // 3 是 CarryChildren 的模拟值

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 1, "应有一个标签")
		assert.Equal(t, "提供座位", result[0].Content, "文案错误")
		assert.NotNil(t, result[0].BorderColor, "没有边框")
		assert.NotNil(t, result[0].TextColor, "缺颜色")
	})

	t.Run("测试 - 携童票场景 - 标签不存在", func(t *testing.T) {
		// 准备
		sic := createMockSeatInfoCard()
		sic.text = make(map[string]gjson.Result)
		sic.text["normal_sub_title_list"] = gjson.Result{}

		// Mock特定场景 - 标签不存在
		patchSpecificExists := mockey.Mock((*gjson.Result).Exists).To(func(r *gjson.Result) bool {
			return false // 模拟CarryChild标签不存在
		}).Build()
		patches = append(patches, patchSpecificExists)

		// 执行
		result := sic.buildSubTitleList(ctx, 3) // 3 是 CarryChildren 的模拟值

		// 断言
		assert.Empty(t, result, "当携童标签不存在时应返回空结果")

		// 恢复Mock
		patchSpecificExists.UnPatch()
	})

	t.Run("测试 - 同时存在价格标签和携童票场景", func(t *testing.T) {
		// 准备
		sic := createMockSeatInfoCard()
		sic.text = make(map[string]gjson.Result)
		sic.text["sub_title_list"] = createSubTitleListdGjsonResult()
		sic.text["normal_sub_title_list"] = createCarryChildGjsonResult()

		// 模拟ruleInfoMap
		sic.ruleInfoMap = map[int64]*Dirpc_SDK_Brick.TypePercentList{
			3: {
				Type:    3,
				Percent: 10, // 百分比为10
			},
		}

		// 执行
		result := sic.buildSubTitleList(ctx, 3) // 3 是 CarryChildren 的模拟值

		// 断言
		assert.NotNil(t, result, "结果不应为空")
		assert.Len(t, result, 2, "应有两个标签")
		assert.Equal(t, "替换后的内容", result[0].Content, "文案错误")
		assert.NotNil(t, result[0].BorderColor, "没有边框")
		assert.NotNil(t, result[0].TextColor, "缺颜色")
		assert.Equal(t, "不提供座位", result[1].Content, "文案错误")
		assert.NotNil(t, result[1].BorderColor, "没有边框")
		assert.NotNil(t, result[1].TextColor, "缺颜色")
	})
}

func createCarryChildGjsonResult() gjson.Result {
	carryChild := map[string]interface{}{
		"carry_child": map[string]interface{}{
			"is_occupy": map[string]interface{}{
				"content":      "提供座位",
				"border_color": "#FF754B",
				"text_color":   "#FF754B",
			},
			"no_occupy": map[string]interface{}{
				"content":      "不提供座位",
				"border_color": "#FF754B",
				"text_color":   "#FF754B",
			},
		},
	}
	// 使用 encoding/json 包中的 json.Marshal 生成 JSON 字符串
	jsonBytes, err := json.Marshal(carryChild)
	if err != nil {
		panic(err) // 或者根据需要处理错误
	}

	// 将 JSON 字符串转换为 gjson.Result
	return gjson.ParseBytes(jsonBytes)
}

func createSubTitleListdGjsonResult() gjson.Result {
	carryChild := map[string]interface{}{
		"carry_child": map[string]interface{}{
			"0": map[string]interface{}{
				"content":      "免费",
				"border_color": "#000000",
				"text_color":   "#000000",
			},
			"50": map[string]interface{}{
				"content":      "半价",
				"border_color": "#000000",
				"text_color":   "#000000",
			},
			"100": map[string]interface{}{
				"content":      "全价",
				"border_color": "#000000",
				"text_color":   "#000000",
			},
			"default": map[string]interface{}{
				"content":      "{{num}}折",
				"border_color": "#000000",
				"text_color":   "#000000",
			},
		},
	}
	// 使用 encoding/json 包中的 json.Marshal 生成 JSON 字符串
	jsonBytes, err := json.Marshal(carryChild)
	if err != nil {
		panic(err) // 或者根据需要处理错误
	}

	// 将 JSON 字符串转换为 gjson.Result
	return gjson.ParseBytes(jsonBytes)
}

func createBaseProduct() *seatInfoCard {
	mockText := createMockRealData(`{
    "passenger_limit_toast": "当前班次最多可选{{num}}人",
    "carry_children_limit_toast": "当前班次携童最多可选{{num}}人",
    "carry_children_zero_limit_toast": "当前班次携童已售罄",
    "title": "请选择乘车人",
    "select_title": "已选{{num}}人乘车",
    "sub_title": "",
    "sub_title_no_children_no_carry_children": "{该线路统一票价，不区分成人儿童}",
    "normal_sub_title_list": {
        "carry_child": {
            "is_occupy": {
                "content": "提供座位",
                "border_color": "#FF754B",
                "text_color": "#FF754B"
            },
            "no_occupy": {
                "content": "不提供座位",
                "border_color": "#FF754B",
                "text_color": "#FF754B"
            }
        }
    },
    "sub_title_list": {
        "0": {
            "content": "免费",
            "border_color": "#FF754B",
            "text_color": "#FF754B"
        },
        "50": {
            "content": "半价",
            "border_color": "#FF754B",
            "text_color": "#FF754B"
        },
        "100": {
            "content": "全价",
            "border_color": "#FF754B",
            "text_color": "#FF754B"
        },
        "default": {
            "content": "{{num}}折",
            "border_color": "#FF754B",
            "text_color": "#FF754B"
        }
    },
    "ticket_type": {
        "1": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/zK25sXqpig1693042625562.png",
            "title": "成人票",
            "sub_title": "14周岁以上或>1.5m的儿童",
            "sub_title_no_children_carry_children": "6周岁以上或>1.2m的儿童",
            "sub_title_no_children_no_carry_children": ""
        },
        "2": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/PhfeDvYPse1693042610250.png",
            "title": "儿童票",
            "sub_title": "6-14周岁或1.2-1.5m的儿童",
            "sub_title_children_no_carry_children": "0-14周岁且身高在1.5m以下的儿童（含怀抱幼儿）"
        },
        "3": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/eo86bu3p0t1693042616837.png",
            "title": "携童票",
            "sub_title": "6周岁或1.2m以下的儿童"
        },
        "4": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/WUk7BqmEMt1695038515117.png",
            "title": "业主成人票"
        },
        "5": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/ZISP1WluCu1695038501918.png",
            "title": "业主儿童票"
        },
        "6": {
            "icon": "https://img-hxy021.didistatic.com/static/starimg/img/uNEzaBHnF51695038508258.png",
            "title": "业主老人票"
        },
        "7": {
            "icon": "https://s3-gz01.didistatic.com/packages-mait/img/L1jvTqJhk31730086859650.png",
            "title": "优待票",
            "sub_title": "军残、警残、消防残疾",
            "bubble_text": "乘车需出示相关证件"
        }
    },
    "ticket_rank": [
        1,
        2,
        3,
        4,
        5,
        6,
        7
    ]
}`, "seat_info_card")
	product := createProduct()
	baseSic := NewSeatInfoCard(product, mockText, &intercity_estimate_detail.SupportInfo{
		SupportChildrenTicket: true,
	}, true)
	return baseSic
}

// TestBuildSeatCardTitle
func TestBuildSeatCardTitle(t *testing.T) {
	ctx := context.Background()
	sic := createBaseProduct()
	t.Run("测试 01- 无开关", func(t *testing.T) {
		result := sic.buildSeatCardTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
	})
	t.Run("测试 02- 有开关", func(t *testing.T) {
		sicT := createBaseProduct()
		sicT.newPageSwitch = false
		result := sicT.buildSeatCardTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
	})
	/**
	passengerDetailInfo := sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo
	if passengerDetailInfo == nil || len(passengerDetailInfo.PassengerList) == 0 {
		sic.selectNum = DefaultSelectNum
		return
	}
	*/
	t.Run("测试 03 有开关", func(t *testing.T) {
		sicT := createBaseProduct()
		sicT.newPageSwitch = false
		result := sicT.buildSeatCardTitle(ctx)
		// 断言
		assert.NotNil(t, result, "not nil")
	})
}
