package seat_info_card

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/spf13/cast"

	Dirpc_SDK_Brick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/shopspring/decimal"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"github.com/tidwall/gjson"
)

type seatInfoCard struct {
	*seatInfoBase
	supportInfo   *intercity_estimate_detail.SupportInfo
	ruleInfoMap   map[int64]*Dirpc_SDK_Brick.TypePercentList
	newPageSwitch bool
}

const (
	CarryChild       = "carry_child" //携童标签相关
	IsOccupy         = "is_occupy"   // 携童票占座
	NoOccupy         = "no_occupy"   // 携童票不可占座
	DefaultSelectNum = 1             // 默认勾选人数
)

// NewSeatInfoCard ...
func NewSeatInfoCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, supportInfo *intercity_estimate_detail.SupportInfo, newPageSwitch bool) *seatInfoCard {
	return &seatInfoCard{
		seatInfoBase:  newSeatInfoBase(product, text),
		supportInfo:   supportInfo,
		newPageSwitch: newPageSwitch,
	}
}

func (sic *seatInfoCard) Render(ctx context.Context) *proto.SeatInfoCard {
	return sic.getSeatInfoCard(ctx)
}

// GetHeadCard ...
func (sic *seatInfoCard) getSeatInfoCard(ctx context.Context) *proto.SeatInfoCard {
	if sic.text == nil {
		return nil
	}

	sic.buildSupportTicketType(ctx)
	sic.buildSeatRules(ctx)

	subTitleKey := "sub_title"
	if sic.hitApolloSwitch(ctx) && !sic.supportInfo.SupportChildrenTicket && !sic.supportInfo.SupportCarryChildrenTicket {
		subTitleKey += "_" + intercity_estimate_detail.NoSupportChildTicket + intercity_estimate_detail.NoSupportCarryChildTicket
	}
	return &proto.SeatInfoCard{
		SeatLimit:     sic.buildSeatLimit(ctx),
		Title:         sic.buildSeatCardTitle(ctx),
		Subtitle:      sic.text[subTitleKey].String(),
		PassengerList: sic.buildSeatDetail(ctx),
	}
}
func (sic *seatInfoCard) buildSeatCardTitle(ctx context.Context) string {
	if !sic.newPageSwitch {
		return sic.text["title"].String()
	}

	selectNum := sic.getSelectTotalNum()
	if selectNum == 0 {
		return sic.text["title"].String()
	}
	return util.ReplaceTag(ctx, sic.text["select_title"].String(), map[string]string{
		"num": cast.ToString(selectNum),
	})
}

// buildSeatLimit ...
func (sic *seatInfoCard) buildSeatLimit(ctx context.Context) *proto.SeatLimit {
	return &proto.SeatLimit{
		CarryChildrenLimit: sic.buildCarryChildrenLimit(ctx),
		PassengerLimit:     sic.buildPassengerLimit(ctx),
	}
}

// buildSeatDetail ...
func (sic *seatInfoCard) buildSeatDetail(ctx context.Context) []*proto.SeatDetail {
	if sic.product == nil || sic.product.BaseReqData == nil || sic.text == nil {
		return nil
	}

	resSeatDetail := sic.getDefaultSeatDetail(ctx)
	passengerMap := make(map[int32]*models.PassengerItem)

	passengerDetailInfo := sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo
	if passengerDetailInfo == nil || len(passengerDetailInfo.PassengerList) == 0 {
		return resSeatDetail
	}

	for _, passengerItem := range passengerDetailInfo.PassengerList {
		if passengerItem == nil {
			continue
		}

		passengerMap[passengerItem.TicketType] = passengerItem
	}

	for _, passenger := range resSeatDetail {
		if passenger == nil {
			continue
		}

		if passengerItem, exist := passengerMap[passenger.TicketType]; exist {
			passenger.PassengerCount = passengerItem.PassengerCount
		}
	}

	return resSeatDetail
}

// getDefaultSeatDetail ...
func (sic *seatInfoCard) getDefaultSeatDetail(ctx context.Context) []*proto.SeatDetail {
	ticketsInfo := sic.text["ticket_type"].Map()

	resSeatDetail := make([]*proto.SeatDetail, 0)
	selectedTicketTypes := make(map[int64]bool)

	passengerDetailInfo := sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo
	if passengerDetailInfo != nil && len(passengerDetailInfo.PassengerList) > 0 {
		for _, passengerItem := range passengerDetailInfo.PassengerList {
			if passengerItem == nil {
				continue
			}
			if passengerItem.PassengerCount > 0 {
				selectedTicketTypes[int64(passengerItem.TicketType)] = true
			}
		}
	}

	for _, ticketType := range sic.text["ticket_rank"].Array() {
		if _, support := sic.supportTicketTypes[int32(ticketType.Int())]; support {
			var passengerCount int32
			var bubbleText *string
			if ticketType.Int() == seat_selection_consts.Adult.ToInt64() {
				if sic.product != nil && sic.product.BaseReqData != nil && sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo != nil && len(sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
					passengerCount = 0
				} else {
					passengerCount = 1
				}
			}
			// 选择了对应人数，才会有对应气泡
			if ok := selectedTicketTypes[ticketType.Int()]; ok {
				bubbleText = util.String2PtrString(ticketsInfo[util.Int642String(ticketType.Int())].Map()["bubble_text"].String())
			}

			// 根据票类型特殊化副标题
			subTitleKey := "sub_title"
			if sic.hitApolloSwitch(ctx) && ticketType.Int() == seat_selection_consts.Adult.ToInt64() && !sic.supportInfo.SupportChildrenTicket {
				if sic.supportInfo.SupportCarryChildrenTicket {
					subTitleKey += "_" + intercity_estimate_detail.NoSupportChildTicket + intercity_estimate_detail.SupportCarryChildTicket
				} else {
					subTitleKey += "_" + intercity_estimate_detail.NoSupportChildTicket + intercity_estimate_detail.NoSupportCarryChildTicket
				}
			}
			if sic.hitApolloSwitch(ctx) && ticketType.Int() == seat_selection_consts.Children.ToInt64() && sic.supportInfo.SupportChildrenTicket && !sic.supportInfo.SupportCarryChildrenTicket {
				subTitleKey += "_" + intercity_estimate_detail.SupportChildTicket + intercity_estimate_detail.NoSupportCarryChildTicket
			}
			resSeatDetail = append(resSeatDetail, &proto.SeatDetail{
				TicketType:     int32(ticketType.Int()),
				Title:          ticketsInfo[util.Int642String(ticketType.Int())].Map()["title"].String(),
				Subtitle:       ticketsInfo[util.Int642String(ticketType.Int())].Map()[subTitleKey].String(),
				Icon:           ticketsInfo[util.Int642String(ticketType.Int())].Map()["icon"].String(),
				BubbleText:     bubbleText,
				PassengerCount: passengerCount,
				SubtitleList:   sic.buildSubTitleList(ctx, ticketType.Int()),
			})
		}
	}

	return resSeatDetail
}

// GetSupportTicketType 获取当前路线支持的票类型
func (sic *seatInfoCard) GetSupportTicketType(ctx context.Context) map[int32]bool {
	supportTicketTypes := make(map[int32]bool)

	// 兜底成人
	supportTicketTypes[seat_selection_consts.Adult.ToInt32()] = true

	if sic.product == nil || sic.product.BaseReqData == nil || sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		return supportTicketTypes
	}

	if sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo != nil {
		ruleInfo := sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo
		if ruleInfo != nil && len(ruleInfo.SpecialSeatRules) > 0 {
			for _, rules := range ruleInfo.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.ChildTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.Children.ToInt32()] = true
					supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()] = true
				}

				if rules.RuleName == seat_selection_consts.OwnerTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.HomeOwnerAdult.ToInt32()] = true
					supportTicketTypes[seat_selection_consts.HomeOwnerChildren.ToInt32()] = true
					supportTicketTypes[seat_selection_consts.HomeOwnerOldMan.ToInt32()] = true
				}
			}
		}
	}

	return supportTicketTypes
}

// buildSubTitleList ...
func (sic *seatInfoCard) buildSubTitleList(ctx context.Context, ticketType int64) []*proto.NewFormFeeDesc {
	if sic.text == nil {
		return nil
	}

	var resSubTitle []*proto.NewFormFeeDesc

	// 特殊,与价格相关的标签展示，收敛到这里。
	if seatRule, exist := sic.ruleInfoMap[ticketType]; exist && seatRule != nil {
		percent := float64(seatRule.Percent) / 10
		var config map[string]gjson.Result

		if sic.text["sub_title_list"].Map()[util.Int642String(seatRule.Percent)].Exists() {
			config = sic.text["sub_title_list"].Map()[util.Int642String(seatRule.Percent)].Map()
		} else {
			config = sic.text["sub_title_list"].Map()["default"].Map()
		}

		resSubTitle = append(resSubTitle, &proto.NewFormFeeDesc{
			TextColor:   util.StringPtr(config["text_color"].String()),
			Icon:        config["icon"].String(),
			BorderColor: config["border_color"].String(),
			Content: util.ReplaceTag(ctx, config["content"].String(), map[string]string{
				"num": decimal.NewFromFloat(percent).Round(1).String(),
			}),
		})
	}

	if ticketType == seat_selection_consts.CarryChildren.ToInt64() {
		if !sic.text["normal_sub_title_list"].Map()[CarryChild].Exists() {
			return resSubTitle
		}
		normal := sic.text["normal_sub_title_list"].Map()[CarryChild].Map()
		occupyConfig := normal[NoOccupy].Map()
		// 有设置携童占座
		if sic.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat == 1 {
			occupyConfig = normal[IsOccupy].Map()
		}
		resSubTitle = append(resSubTitle, BuildChildOccupyLabel(occupyConfig))
	}

	return resSubTitle
}

// buildSeatRules ...
func (sic *seatInfoCard) buildSeatRules(ctx context.Context) {
	if sic.product.BaseReqData == nil || sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		return
	}

	var (
		ruleInfoMap = make(map[int64]*Dirpc_SDK_Brick.TypePercentList)
	)

	ruleInfo := sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo
	if ruleInfo == nil {
		return
	}

	for _, rule := range ruleInfo.SpecialSeatRules {
		if rule == nil {
			continue
		}

		if len(rule.TypePercent) > 0 {
			for _, typePercent := range rule.TypePercent {
				if typePercent == nil {
					continue
				}

				ruleInfoMap[typePercent.Type] = typePercent
			}
		}
	}

	sic.ruleInfoMap = ruleInfoMap
}

// hitApolloSwitch ...文案优化开关
func (sic *seatInfoCard) hitApolloSwitch(ctx context.Context) bool {
	apolloParams := map[string]string{
		"pid":           cast.ToString(sic.product.BaseReqData.PassengerInfo.PID),
		"phone":         sic.product.BaseReqData.PassengerInfo.Phone,
		"city":          cast.ToString(sic.product.BaseReqData.AreaInfo.Area),
		"access_key_id": cast.ToString(sic.product.BaseReqData.CommonInfo.AccessKeyID),
		"app_version":   sic.product.BaseReqData.CommonInfo.AppVersion,
	}
	if apollo.FeatureToggle(ctx, "intercity_estimate_confirm_text_optimization", cast.ToString(sic.product.BaseReqData.PassengerInfo.PID), apolloParams) {
		return true
	}

	return false
}

func (sic *seatInfoCard) getSelectTotalNum() int32 {
	passengerDetailInfo := sic.product.BaseReqData.CommonBizInfo.PassengerDetailInfo
	if passengerDetailInfo == nil || len(passengerDetailInfo.PassengerList) == 0 {
		return DefaultSelectNum
	}
	var checkNum int32
	for _, checkPassengerItem := range passengerDetailInfo.PassengerList {
		checkNum += checkPassengerItem.PassengerCount
	}
	return checkNum
}
