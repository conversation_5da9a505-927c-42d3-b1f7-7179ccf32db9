package seat_info_card

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/spf13/cast"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/security"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"github.com/tidwall/gjson"
)

type realNameSeatInfoCard struct {
	*seatInfoBase

	checkPassengerMap          map[string]*models.PassengerItem
	passengerList              []*proto.PassengerItem
	checkedPassengerList       []*proto.PassengerItem
	checkNum                   int
	carryChildrenPassengerInfo *models.PassengerItem
	newPageSwitch              bool
	req                        *proto.IntercityEstimateDetailRequest
}

// NewRealNameSeatInfoCard ...
func NewRealNameSeatInfoCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, newPageSwitch bool, req *proto.IntercityEstimateDetailRequest) *realNameSeatInfoCard {
	return &realNameSeatInfoCard{
		seatInfoBase:  newSeatInfoBase(product, text),
		newPageSwitch: newPageSwitch,
		req:           req,
	}
}

// Render ...
func (rns *realNameSeatInfoCard) Render(ctx context.Context) *proto.RealNameSeatInfoCard {
	return rns.getRealNameSeatInfoCard(ctx)
}

// GetHeadCard ...
func (rns *realNameSeatInfoCard) getRealNameSeatInfoCard(ctx context.Context) *proto.RealNameSeatInfoCard {
	if rns.text == nil {
		return nil
	}

	// 获取儿童和优待票是否有优惠
	rns.getIdentityDiscountMap(ctx)
	// 获取支持票类型
	rns.buildSupportTicketType(ctx)

	// 处理勾选
	rns.handleCheck(ctx)

	// 默勾前置
	rns.checkReSort(ctx)

	return &proto.RealNameSeatInfoCard{
		SeatLimit:                 rns.buildSeatLimit(ctx),
		ChoosePassengerDetail:     rns.buildChoosePassengerDetail(ctx),
		SelectedPassengerDetail:   rns.buildSelectPassengerDetail(ctx),
		NoRealNamePassengerDetail: rns.buildNoRealNamePassengerDetail(ctx),
	}
}

// buildSeatLimit ...
func (rns *realNameSeatInfoCard) buildSeatLimit(ctx context.Context) *proto.SeatLimit {
	return &proto.SeatLimit{
		CarryChildrenLimit: rns.buildCarryChildrenLimit(ctx),
		PassengerLimit:     rns.buildPassengerLimit(ctx),
	}
}

// buildChoosePassengerDetail ...
func (rns *realNameSeatInfoCard) buildChoosePassengerDetail(ctx context.Context) *proto.ChoosePassengerDetail {
	if rns.text == nil {
		return nil
	}
	if rns.newPageSwitch {
		return nil
	}

	return &proto.ChoosePassengerDetail{
		Title:              rns.text["choose_passenger_detail"].Map()["title"].String(),
		PassengerList:      rns.buildChoosePassengerList(ctx),
		AddPassengerButton: rns.buildAddPassengerButton(ctx),
		MoreButton:         rns.buildMoreButton(ctx),
	}
}

// buildSelectPassengerDetail ...
func (rns *realNameSeatInfoCard) buildSelectPassengerDetail(ctx context.Context) *proto.SelectedPassengerDetail {
	if rns.text == nil {
		return nil
	}

	// 老版本，直接返回nil了
	if len(rns.checkedPassengerList) == 0 && !rns.newPageSwitch {
		return nil
	}

	// 新版本，需要按照是否有乘车人，判断副标题的下发+按钮的下发
	title := util.ReplaceTag(ctx, rns.text["selected_passenger_detail"].Map()["title"].String(), map[string]string{
		"num": util.Int2String(rns.checkNum)})
	if rns.newPageSwitch && len(rns.checkedPassengerList) == 0 {
		title = rns.text["selected_passenger_detail"].Map()["default_title"].String()
	}
	return &proto.SelectedPassengerDetail{
		Title:               title,
		Subtitle:            rns.buildSubTitle(ctx),
		PassengerList:       rns.buildSelectPassengerList(ctx),
		PassengerListButton: rns.passengerListButton(ctx),
	}
}

func (rns *realNameSeatInfoCard) buildSubTitle(ctx context.Context) *string {
	if rns.text == nil || rns.product == nil || rns.product.BaseReqData == nil {
		return nil
	}

	if rns.newPageSwitch {
		return rns.buildNewSubTitle(ctx)
	}

	cbi := rns.product.GetCommonBizInfo()

	if cbi.GetChildTicketNew() == nil {
		return util.StringPtr(rns.text["subtitle"].String())
	}

	return nil
}

func (rns *realNameSeatInfoCard) buildNewSubTitle(ctx context.Context) *string {
	childSupport := false
	if rns.hasDiscountSupport[seat_selection_consts.Children.ToInt32()] != nil {
		childSupport = rns.hasDiscountSupport[seat_selection_consts.Children.ToInt32()].HasDiscountSupport
	}
	preferentialSupport := false
	if rns.hasDiscountSupport[seat_selection_consts.PreferentialPeople.ToInt32()] != nil {
		preferentialSupport = rns.hasDiscountSupport[seat_selection_consts.PreferentialPeople.ToInt32()].HasDiscountSupport
	}
	ticketTypeSubtitle := rns.text["ticket_type_subtitle"].Map()
	if childSupport && preferentialSupport {
		return util.StringPtr(ticketTypeSubtitle["child_preferential"].String())
	}
	if childSupport {
		return util.StringPtr(ticketTypeSubtitle["child"].String())
	}
	if preferentialSupport {
		return util.StringPtr(ticketTypeSubtitle["preferential"].String())
	}
	return util.StringPtr(rns.text["new_default_subtitle"].String())

}

// buildNoRealNamePassengerDetail ...
func (rns *realNameSeatInfoCard) buildNoRealNamePassengerDetail(ctx context.Context) *proto.NoRealNamePassengerDetail {
	if rns.text == nil {
		return nil
	}

	return &proto.NoRealNamePassengerDetail{PassengerList: rns.buildNoRealNamePassengerList(ctx)}
}

// buildAddPassengerButton ...
func (rns *realNameSeatInfoCard) buildAddPassengerButton(ctx context.Context) *proto.Button {
	if rns.text == nil {
		return nil
	}

	return &proto.Button{
		Text:                rns.text["add_passenger_button"].Map()["text"].String(),
		FontColor:           util.StringPtr(rns.text["add_passenger_button"].Map()["font_color"].String()),
		LinkUrl:             rns.buildLinkUrl(ctx, rns.text["add_passenger_button"].Map()["link_url"].String(), rns.buildAddPassengerButtonQuery(ctx)),
		BackgroundGradients: nil,
		ActionType:          util.Int32Ptr(int32(rns.text["add_passenger_button"].Map()["action_type"].Int())),
	}
}

// buildMoreButton ...
func (rns *realNameSeatInfoCard) buildMoreButton(ctx context.Context) *proto.Button {
	if rns.text == nil {
		return nil
	}

	buttonText := rns.text["more_button"].Map()["text_v2"].String()
	if len(rns.passengerList) > int(rns.text["more_button"].Map()["limit_num"].Int()) {
		buttonText = rns.text["more_button"].Map()["text"].String()
	}

	return &proto.Button{
		Text:                buttonText,
		FontColor:           util.StringPtr(rns.text["more_button"].Map()["font_color"].String()),
		LinkUrl:             rns.buildLinkUrl(ctx, rns.text["more_button"].Map()["link_url"].String(), rns.buildMoreButtonQuery(ctx)),
		BackgroundGradients: nil,
		ActionType:          util.Int32Ptr(int32(rns.text["more_button"].Map()["action_type"].Int())),
	}
}

// passengerListButton ...
func (rns *realNameSeatInfoCard) passengerListButton(ctx context.Context) *proto.Button {
	if !rns.newPageSwitch {
		return nil
	}

	if rns.text == nil {
		return nil
	}

	passengerButtonConfig := rns.text["passenger_list_func_button"].Map()
	return &proto.Button{
		Text:                passengerButtonConfig["text"].String(),
		FontColor:           util.StringPtr(passengerButtonConfig["font_color"].String()),
		LinkUrl:             rns.buildLinkUrl(ctx, passengerButtonConfig["link_url"].String(), rns.buildMoreButtonQuery(ctx)),
		BackgroundGradients: nil,
		ActionType:          util.Int32Ptr(int32(passengerButtonConfig["action_type"].Int())),
	}
}

// buildLinkUrl ...
func (rns *realNameSeatInfoCard) buildLinkUrl(ctx context.Context, originUrl string, query map[string]string) *string {
	if len(originUrl) <= 0 {
		return util.StringPtr("")
	}
	queryStr := ""

	if len(query) > 0 {
		for k, v := range query {
			queryItem := fmt.Sprintf("%s=%s", k, v)
			if len(queryStr) <= 0 {
				queryStr += queryItem
			} else {
				queryStr += fmt.Sprintf("&%s", queryItem)
			}
		}
	}

	if len(queryStr) > 0 {
		return util.StringPtr(fmt.Sprintf("%s?%s", originUrl, queryStr))
	}

	return util.StringPtr(originUrl)
}

// buildChoosePassengerList ...
func (rns *realNameSeatInfoCard) buildChoosePassengerList(ctx context.Context) []*proto.PassengerItem {
	if rns.text == nil {
		return rns.passengerList
	}

	if rns.text["choose_passenger_detail"].Map()["trunc_limit"].Exists() {
		truncLimit := int(rns.text["choose_passenger_detail"].Map()["trunc_limit"].Int())
		if len(rns.passengerList) > truncLimit {
			return rns.passengerList[:truncLimit]
		}
	}

	return rns.passengerList
}

// buildSelectPassengerList ...
func (rns *realNameSeatInfoCard) buildSelectPassengerList(ctx context.Context) []*proto.PassengerItem {
	return rns.checkedPassengerList
}

// buildNoRealNamePassengerList ...
func (rns *realNameSeatInfoCard) buildNoRealNamePassengerList(ctx context.Context) []*proto.PassengerItem {
	if rns.text == nil {
		return nil
	}

	passengerList := make([]*proto.PassengerItem, 0)

	if _, support := rns.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; support {
		conf := rns.text["no_real_name"].Map()[util.Int32String(seat_selection_consts.CarryChildren.ToInt32())].Map()
		passengerItem := &proto.PassengerItem{
			TicketType: seat_selection_consts.CarryChildren.ToInt32(),
			Title:      util.StringPtr(conf["title"].String()),
			Subtitle:   util.StringPtr(conf["sub_title"].String()),
		}

		if rns.carryChildrenPassengerInfo != nil {
			passengerItem.PassengerCount = util.Int32Ptr(rns.carryChildrenPassengerInfo.PassengerCount)
		} else {
			passengerItem.PassengerCount = util.Int32Ptr(0)
		}

		// 如果占座，有库存
		subtitle := BuildChildOccupyLabel(conf[NoOccupy].Map())
		if rns.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat == 1 {
			subtitle = BuildChildOccupyLabel(conf[IsOccupy].Map())
		}
		passengerItem.SubtitleList = []*proto.NewFormFeeDesc{subtitle}

		passengerList = append(passengerList, passengerItem)

		return passengerList
	}

	return nil
}

// buildDisable ...
func (rns *realNameSeatInfoCard) buildDisable(ctx context.Context, item *dirpcSdkBrick.IdentityItem) *int32 {
	if item == nil {
		return util.Int32Ptr(models.NoDisable)
	}

	if util.InArrayInt32(item.Type, []int32{
		seat_selection_consts.HomeOwnerAdult.ToInt32(),
		seat_selection_consts.HomeOwnerChildren.ToInt32(),
		seat_selection_consts.HomeOwnerOldMan.ToInt32()}) {
		return util.Int32Ptr(models.Disable)
	}

	return util.Int32Ptr(models.NoDisable)
}

// handleCheck 处理勾选
func (rns *realNameSeatInfoCard) handleCheck(ctx context.Context) {
	var (
		resPassengerList        = make([]*proto.PassengerItem, 0)
		resCheckedPassengerList = make([]*proto.PassengerItem, 0)
		sortWeight              = make(map[string]int32)
		checkPassengerMap       = make(map[string]*models.PassengerItem)
		checkNum                = 0

		logTag = "buildChoosePassengerList"
	)

	// 1. 获取已选乘车人
	passengerDetailInfo := rns.product.BaseReqData.CommonBizInfo.PassengerDetailInfo
	if passengerDetailInfo != nil {
		checkPassengerNum := len(passengerDetailInfo.PassengerList)
		for index, checkPassengerItem := range passengerDetailInfo.PassengerList {
			if checkPassengerItem == nil {
				continue
			}

			// 携童没有身份证
			if checkPassengerItem.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
				rns.carryChildrenPassengerInfo = checkPassengerItem
				continue
			}

			key := models.GenPassengerKeyIngoreTicketType(checkPassengerItem.DeIdentityID, checkPassengerItem.IdentityType)
			checkPassengerMap[key] = checkPassengerItem
			sortWeight[key] = int32(checkPassengerNum - index)
		}
	}

	rns.checkPassengerMap = checkPassengerMap

	// 2. 获取全部乘车人
	identityPageInfo := rns.product.BaseReqData.CommonBizInfo.IdentityPageInfo.IdentityHistoryInfo
	currentAdultNum := 0 //记录成人数量
	for _, passengerItem := range identityPageInfo {
		if passengerItem == nil {
			continue
		}

		if rns.newPageSwitch && passengerItem.Expired == intercity_estimate_detail.ChildIdExpired {
			continue
		}
		key := models.GenPassengerKeyIngoreTicketType(passengerItem.IdentityNo, passengerItem.IdentityType)
		isSelected := models.NoCheck
		enIdentity, err := security.AesEncryptMsg(passengerItem.IdentityNo)
		if err != nil {
			log.Trace.Warnf(ctx, logTag, "aes encrypt fail, err:%v", err)
			return
		}

		if _, exist := checkPassengerMap[key]; exist {
			checkNum++
			isSelected = models.Check
			passengerCheckedItem := &proto.PassengerItem{
				PassengerName: util.StringPtr(passengerItem.Name),
				TicketType:    passengerItem.Type,
				IdentityId:    util.StringPtr(util.CoverStrByStart(passengerItem.IdentityNo, 2, 4)),
				IdentityType:  util.Int32Ptr(passengerItem.IdentityType),
				EncryptionId:  util.StringPtr(enIdentity),
				SubtitleList:  rns.buildSubTitleList(ctx, passengerItem),
				LinkUrl:       rns.buildLinkUrl(ctx, rns.text["selected_passenger_detail"].Map()["link_url"].String(), rns.buildEditQuery(ctx, passengerItem)),
				Disable:       rns.buildDisable(ctx, passengerItem),
				DisableToast:  rns.buildDisableToast(ctx, passengerItem),
				DiscountMsg:   rns.buildDiscountMsg(ctx, passengerItem),
			}
			passengerCheckedItem.FeeMsg = rns.buildFeeMsg(ctx, passengerItem, cast.ToInt64(currentAdultNum))
			//passengerCheckedItem.FeeDescList = rns.buildFeeDescList(ctx, passengerItem, cast.ToInt64(currentAdultNum))
			resCheckedPassengerList = append(resCheckedPassengerList, passengerCheckedItem)
			if passengerItem.Type == seat_selection_consts.Adult.ToInt32() {
				currentAdultNum = currentAdultNum + 1
			}
		}

		resPassengerList = append(resPassengerList, &proto.PassengerItem{
			PassengerName: util.StringPtr(passengerItem.Name),
			TicketType:    passengerItem.Type,
			IdentityType:  util.Int32Ptr(passengerItem.IdentityType),
			IdentityId:    util.StringPtr(util.CoverStrByStart(passengerItem.IdentityNo, 2, 4)),
			EncryptionId:  util.StringPtr(enIdentity),
			IsSelected:    util.Int32Ptr(int32(isSelected)),
			Weight:        util.Int32Ptr(sortWeight[key]),
		})

	}

	rns.checkNum = checkNum
	rns.passengerList = resPassengerList
	rns.checkedPassengerList = resCheckedPassengerList
}

// buildSubTitleList ...
func (rns *realNameSeatInfoCard) buildSubTitleList(ctx context.Context, passenger *dirpcSdkBrick.IdentityItem) []*proto.NewFormFeeDesc {
	if passenger == nil || rns.text == nil {
		return nil
	}

	resSubTitle := make([]*proto.NewFormFeeDesc, 0)
	showSubTitle := true

	// 老端版本 都下发
	// 新端 支持儿童票 才则下发，加了优待
	if rns.product != nil && rns.product.BaseReqData != nil {
		cbi := rns.product.GetCommonBizInfo()
		showSubTitle = !rns.isSupportNoType(ctx) || (rns.isSupportNoType(ctx) && (cbi.GetChildTicketNew() != nil || cbi.GetCouponTicket() != nil))
	}

	if rns.newPageSwitch {
		return rns.buildNewSubtitleList(ctx, showSubTitle, passenger)
	}

	if showSubTitle && rns.text["selected_passenger_detail"].Map()["sub_title_list"].Map()[util.Int32String(passenger.Type)].Exists() {
		conf := rns.text["selected_passenger_detail"].Map()["sub_title_list"].Map()[util.Int32String(passenger.Type)].Map()
		resSubTitle = append(resSubTitle, &proto.NewFormFeeDesc{
			TextColor:   util.StringPtr(conf["text_color"].String()),
			BorderColor: conf["border_color"].String(),
			Icon:        conf["icon"].String(),
			Content:     conf["content"].String(),
		})
	}

	if len(resSubTitle) > 0 {
		return resSubTitle
	}

	return nil
}

func (rns *realNameSeatInfoCard) buildDiscountMsg(ctx context.Context, passenger *dirpcSdkBrick.IdentityItem) *proto.NewFormFeeDesc {
	if !rns.newPageSwitch {
		return nil
	}
	if !rns.isDiscountRatio(passenger) {
		return nil
	}
	discountRatio := rns.hasDiscountSupport[passenger.Type].DiscountRatio
	if discountRatio == intercity_estimate_detail.ZeroRule {
		return nil
	}

	discountConfig := rns.text["selected_passenger_detail"].Map()["discount_msg_list"].Map()[util.Int32String(passenger.Type)].Map()
	msgConfig := discountConfig["default"].Map()
	if discountRatio == intercity_estimate_detail.HalfPrice {
		msgConfig = discountConfig["half_price"].Map()
	}

	return &proto.NewFormFeeDesc{
		TextColor:   util.StringPtr(msgConfig["text_color"].String()),
		BorderColor: msgConfig["border_color"].String(),
		Icon:        msgConfig["icon"].String(),
		Content: util.ReplaceTag(ctx, msgConfig["content"].String(), map[string]string{
			"discount": cast.ToString(discountRatio),
		}),
	}
}

func (rns *realNameSeatInfoCard) buildNewSubtitleList(ctx context.Context, showSubTitle bool, passenger *dirpcSdkBrick.IdentityItem) []*proto.NewFormFeeDesc {
	if !rns.isShowSubtitle(showSubTitle, passenger) {
		return nil
	}
	identityTypeTagConfig := rns.text["selected_passenger_detail"].Map()["sub_title_list"].Map()[util.Int32String(passenger.Type)].Map()
	resSubTitle := make([]*proto.NewFormFeeDesc, 0)
	resSubTitle = append(resSubTitle, &proto.NewFormFeeDesc{
		TextColor:   util.StringPtr(identityTypeTagConfig["text_color"].String()),
		BorderColor: identityTypeTagConfig["border_color"].String(),
		Icon:        identityTypeTagConfig["icon"].String(),
		Content:     identityTypeTagConfig["content"].String(),
	})
	return resSubTitle
}

func (rns *realNameSeatInfoCard) isDiscountRatio(passenger *dirpcSdkBrick.IdentityItem) bool {
	// 所有成人优惠都不需要
	if passenger.Type == seat_selection_consts.Adult.ToInt32() {
		return false
	}
	discountConfig := rns.text["selected_passenger_detail"].Map()["discount_msg_list"].Map()
	if len(discountConfig) == 0 {
		return false
	}
	// 查看是否有对应的优惠。如果是百分之百，也不展示。
	hasTicket := rns.hasDiscountSupport
	if hasTicket[passenger.Type] != nil && hasTicket[passenger.Type].HasDiscountSupport && discountConfig[util.Int32String(passenger.Type)].Exists() {
		return true
	}
	return false
}

func (rns *realNameSeatInfoCard) isShowSubtitle(showSubTitle bool, passenger *dirpcSdkBrick.IdentityItem) bool {
	if !showSubTitle {
		return false
	}
	// 所有成人标签都不展示了
	if passenger.Type == seat_selection_consts.Adult.ToInt32() {
		return false
	}
	identityTypeTagConfig := rns.text["selected_passenger_detail"].Map()["sub_title_list"].Map()
	if len(identityTypeTagConfig) == 0 {
		return false
	}
	// 查看是否有对应的优惠。如果是百分之百，也不展示。
	hasTicket := rns.hasDiscountSupport
	if hasTicket[passenger.Type] != nil && hasTicket[passenger.Type].HasDiscountSupport && identityTypeTagConfig[util.Int32String(passenger.Type)].Exists() {
		return true
	}
	return false
}

// buildMoreButtonQuery ...
func (rns *realNameSeatInfoCard) buildMoreButtonQuery(ctx context.Context) map[string]string {
	return rns.buildCommonQuery(ctx)
}

// buildAddPassengerButtonQuery ...
func (rns *realNameSeatInfoCard) buildAddPassengerButtonQuery(ctx context.Context) map[string]string {
	return rns.buildCommonQuery(ctx)
}

// buildEditLinkUrl ...
func (rns *realNameSeatInfoCard) buildEditQuery(ctx context.Context, item *dirpcSdkBrick.IdentityItem) map[string]string {
	if rns.product == nil {
		return nil
	}

	var logTag = "buildEditQuery"

	resQuery := rns.buildCommonQuery(ctx)

	passengerInfo := &models.PassengerH5Item{
		ID:           item.Id,
		TicketType:   item.Type,
		Name:         item.Name,
		IdentityType: item.IdentityType,
		IdentityNo:   item.IdentityNo,
	}

	passengerInfoStr, err := json.Marshal(passengerInfo)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "marshal fail, err:%v, origin:%v", err, util.JustJsonEncode(passengerInfo))
		return nil
	}

	encryptPassengerInfo, err := security.AesEncryptMsg(string(passengerInfoStr))
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "aes fail, err:%v, origin:%v", err, string(passengerInfoStr))
		return nil
	}

	resQuery["passenger_info"] = encryptPassengerInfo

	return resQuery
}

// buildCommonQuery ...
func (rns *realNameSeatInfoCard) buildCommonQuery(ctx context.Context) map[string]string {
	var resQuery = make(map[string]string)

	if rns.product == nil || rns.text == nil {
		return resQuery
	}

	if rns.product.Product != nil {
		resQuery["bus_service_shift_id"] = rns.product.Product.ShiftID
	}

	if rns.product.GetBizInfo() != nil {
		resQuery["combo_id"] = util.Int642String(rns.product.GetBizInfo().ComboID)
	}

	if rns.product.BaseReqData != nil {
		resQuery["start_city_id"] = util.Int32String(rns.product.BaseReqData.AreaInfo.Area)
		var carryChildrenNum int32
		var remainNum int32
		// 支持携童票，且占座
		if _, support := rns.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; support && rns.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat == 1 {
			if rns.carryChildrenPassengerInfo != nil {
				carryChildrenNum = rns.carryChildrenPassengerInfo.PassengerCount
			}
		}

		if rns.product.BaseReqData.CommonBizInfo.MaxInventory-carryChildrenNum > 0 {
			remainNum = rns.product.BaseReqData.CommonBizInfo.MaxInventory - carryChildrenNum
		}

		resQuery["seat_num"] = util.Int32String(remainNum)

	}

	if rns.text != nil {
		resQuery["max_num"] = rns.text["passenger_max_num"].String()
	}

	resQuery["business_id"] = util.Int642String(rns.product.GetBusinessID())
	resQuery["estimate_id"] = rns.product.GetEstimateID()

	return resQuery
}

// buildDisableToast ..
// buildDisableToast ...
func (rns *realNameSeatInfoCard) buildDisableToast(ctx context.Context, item *dirpcSdkBrick.IdentityItem) *string {
	if rns.text == nil {
		return nil
	}

	disable := rns.buildDisable(ctx, item)
	if disable != nil {
		return util.StringPtr(rns.text["disable_toast"].String())
	}

	return nil
}

// buildFeeDescList ...
func (rns *realNameSeatInfoCard) buildFeeDescList(ctx context.Context, item *dirpcSdkBrick.IdentityItem, currentAdultNum int64) []*proto.TagWithIconAndBorder {
	feeDescList := make([]*proto.TagWithIconAndBorder, 0)
	busCard := rns.product.GetBusCard()
	ticketPrice := rns.product.GetBusTicketPriceByType(item.Type)
	//根据成人顺序依次显示抵扣金额
	if item.Type == seat_selection_consts.Adult.ToInt32() && busCard != nil && currentAdultNum < busCard.DiscountTimes {
		cardDesc := &proto.TagWithIconAndBorder{
			Icon: rns.text["selected_passenger_detail"].Map()["card_fee_desc"].Map()["icon"].String(),
			Content: util.ReplaceTag(ctx, rns.text["selected_passenger_detail"].Map()["card_fee_desc"].Map()["content"].String(), map[string]string{
				"fee": cast.ToString(ticketPrice),
			}),
			BorderColor: rns.text["selected_passenger_detail"].Map()["card_fee_desc"].Map()["border_color"].String(),
			TextColor:   rns.text["selected_passenger_detail"].Map()["card_fee_desc"].Map()["text_color"].String(),
		}
		feeDescList = append(feeDescList, cardDesc)
	}
	return feeDescList
}

// buildFeeMsg ...
func (rns *realNameSeatInfoCard) buildFeeMsg(ctx context.Context, item *dirpcSdkBrick.IdentityItem, currentAdultNum int64) *string {
	if rns.text == nil {
		return nil
	}
	var feeMsg string
	ticketPrice := rns.product.GetBusTicketPriceByType(item.Type)
	feeMsg = util.ReplaceTag(ctx, rns.text["selected_passenger_detail"].Map()["fee_msg"].String(), map[string]string{
		"ticketPrice": cast.ToString(ticketPrice),
	})
	return &feeMsg
}

// checkReSort 勾选重排
func (rns *realNameSeatInfoCard) checkReSort(ctx context.Context) {
	passengerList := rns.passengerList

	if len(passengerList) > 0 {
		sort.Slice(passengerList, func(i, j int) bool {
			var iWeight, jWeight int32

			if passengerList[i] != nil && passengerList[i].Weight != nil {
				iWeight = *passengerList[i].Weight
			}

			if passengerList[j] != nil && passengerList[j].Weight != nil {
				jWeight = *passengerList[j].Weight
			}

			return iWeight > jWeight
		})
	}
}

// isSupportNoType 端是否支持不下发票型选择组件
func (rns *realNameSeatInfoCard) isSupportNoType(ctx context.Context) bool {
	pidKey, params := rns.product.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "ticket_rules_support_rich_text", pidKey, params)
}
