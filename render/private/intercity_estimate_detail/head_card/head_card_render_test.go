package head_card

import (
	"context"
	"encoding/json"
	"testing"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"

	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/util"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	"google.golang.org/protobuf/proto"
)

func TestBuildRebookButton(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
		},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				IsBestShift: 1,
			},
		},
	}

	text := map[string]gjson.Result{
		"rebook_button": gjson.Result{
			Type: gjson.JSON,
			Raw:  `{"text":"重新购票","font_color":"#000000", "action_type":1}`,
		},
	}

	hc := &headCard{
		product: product,
		text:    text,
	}

	PatchConvey("should handle normal case", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":"https://refund.url"}`).Build()
		Mock(apollo.FeatureToggle).Return(true).Build()

		button := hc.buildRebookButton(ctx)
		So(button, ShouldNotBeNil)
		So(button.Text, ShouldEqual, "重新购票")
		So(*button.FontColor, ShouldEqual, "#000000")
		So(*button.ActionType, ShouldEqual, 1)
		So(*button.LinkUrl, ShouldEqual, "https://refund.url")
	})

	PatchConvey("should handle case with empty refund click url", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":""}`).Build()

		button := hc.buildRebookButton(ctx)
		So(button, ShouldNotBeNil)
		So(button.Text, ShouldEqual, "重新购票")
		So(*button.FontColor, ShouldEqual, "#000000")
		So(*button.ActionType, ShouldEqual, 1)
		So(*button.LinkUrl, ShouldEqual, "")
	})

	PatchConvey("should handle case without bus card", t, func() {
		Mock((*biz_runtime.ProductInfoFull).GetBusCard).Return(nil).Build()
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":"https://refund.url"}`).Build()
		Mock(apollo.FeatureToggle).Return(false).Build()

		button := hc.buildRebookButton(ctx)
		So(button, ShouldNotBeNil)
		So(button.Text, ShouldEqual, "重新购票")
		So(*button.FontColor, ShouldEqual, "#000000")
		So(*button.ActionType, ShouldEqual, 1)
		So(*button.LinkUrl, ShouldEqual, "https://refund.url")
	})
}

// 将JSON转换为map[string]gjson.Result
func buildHeadCardMap(jsonStr string) map[string]gjson.Result {
	var result map[string]gjson.Result

	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil
	}
	return result
}

func createMockHeadData() map[string]gjson.Result {
	headCardJSON := `{
    "head_card": {
        "icon": "https://img-hxy021.didistatic.com/static/starimg/img/zGLv9myz6z1693203456283.png",
        "title": "该线路由粤运交通提供服务",
        "background_img": "https://img-hxy021.didistatic.com/static/starimg/img/BCc2pghqnT1693904525858.png",
        "title_img_new": "https://s3-gz01.didistatic.com/packages-mait/img/dBrPgWEcVM1732866540653.png",
        "rebook_button": {
            "text": "退改签规则",
            "link_url": ""
        },
        "new_rebook_button": {
            "text": "购票须知",
            "font_color": "#666666",
            "link_url": "",
            "background_gradients": ["#123","#234"],
            "action_type": "2"
        },
        "rebook_msg_info": {
            "default": {
                "text": "改签规则点击购票须知查看",
                "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                "text_color": "#295DF3"
            },
            "default4": {
                "text": "检票前可改签一次",
                "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                "text_color": "#295DF3"
            },
            "default6": {
                "text": "不支持改签",
                "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                "text_color": "#295DF3"
            },
            "default7": {
                "text": "出发前10分钟可改签，改签后不可退",
                "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                "text_color": "#295DF3"
            }
        },
        "refund_msg_info": {
            "zero_fee_info": {
                "once_rule": {
                    "text": "发车前免费退票",
                    "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                    "text_color": "#295DF3"
                },
                "more_rule": {
                    "text": "发车前{{date_time}}{{unit_str}}免费退票.",
                    "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
                    "text_color": "#295DF3"
                }
            },
            "has_fee": {
                "text": "发车前退票费{{rate}}%起",
                "icon": "https://dpubstatic.udache.com/static/dpubimg/ao-bi1fynXcZEGfXv4225.png",
                "text_color": "#295DF3"
            }
        },
        "station_info": {
            "title": "{{date}}{{{time}}}发车",
            "fence_bus_title": "预计{{{time}}}开始接人",
            "middle_title": "途经{{num}}个站点",
            "from_pick_up_method": "【上门接/送】",
            "to_pick_up_method": "【上门接/送】",
            "pick_up_method": "【上门接/送】",
            "popup_data": {
                "title": "途经{{num}}个站点",
                "hour_minute": "{{hour}}小时{{minute}}分钟",
                "only_minute": "{{minute}}分钟",
                "sub_title": "全程预计用时{{time}}",
                "departure_msg": "预计{{time}}出发",
                "start_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/MYJcAJqKMu1693210222572.png",
                "end_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/yTVgyhrjal1693210231395.png",
                "middle_station_icon": ""
            }
        }
    }
}`

	text := map[string]gjson.Result{
		"head_card": gjson.Result{
			Type: gjson.JSON,
			Raw:  headCardJSON,
		},
	}

	return text["head_card"].Map()
}

func createConditionData(ruleInfo *ticketPrice.RefundChargeRuleData) *biz_runtime.ProductInfoFull {
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					IntercityRule: &ticketPrice.RuleData{
						RefundChargeRule: ruleInfo,
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				IsBestShift: 1,
			},
		},
	}
	return product
}

func createProduct() *biz_runtime.ProductInfoFull {
	return &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					IntercityRule: &ticketPrice.RuleData{
						RebookChargeRule: &ticketPrice.RebookChargeRuleData{
							RebookRuleId: util.StrPtr("default7"),
							RuleName:     util.StrPtr("可改签改签后不可退"),
							RuleDetail: &ticketPrice.RebookChargeRuleDetail{
								MaxRebookTimes: 1,
								ChargeRate: []*ticketPrice.RebookChargeRate{
									{Times: 1,
										Rate: 100},
								},
							},
						},
						RefundChargeRule: &ticketPrice.RefundChargeRuleData{
							BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
								{
									LeftTimeInterval:  24,
									RightTimeInterval: 48,
									RefundChargeRate:  50,
								},
							},
							TimeUnit: proto.String("hour"),
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				IsBestShift: 1,
			},
		},
	}
}

func createDefaultRuleData() *ticketPrice.RefundChargeRuleData {
	return &ticketPrice.RefundChargeRuleData{
		TimeUnit: util.StrPtr("hour"),
		BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
			{
				LeftTimeInterval:  24,
				RightTimeInterval: 48,
				RefundChargeRate:  50,
			},
		},
		AfterDepartureRules: []*ticketPrice.RefundChargeRule{
			{
				LeftTimeInterval:  24,
				RightTimeInterval: 48,
				RefundChargeRate:  50,
			},
		},
	}
}

func TestHeadCard(t *testing.T) {
	ctx := context.Background()
	product := createProduct()
	text := createMockHeadData()
	defaultRule := createDefaultRuleData()

	PatchConvey("test01 开关false", t, func() {
		hc := &headCard{
			product:       product,
			text:          text["head_card"].Map(),
			NewPageSwitch: false,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.Nil(t, result, "应该为nil")
	})

	PatchConvey("test02 规则内容为nil开关为true", t, func() {
		hc := &headCard{
			product:       createConditionData(nil),
			text:          text["head_card"].Map(),
			NewPageSwitch: true,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.NotNil(t, result, "不需要为nil")
		assert.Len(t, result.RuleMsg, 0, "不需要为nil")
	})

	PatchConvey("test03 无规则但涵盖退票按钮", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{
        "refund_button_name": "退改签说明",
        "refund_right_icon": "https://pt-starimg.didistatic.com/static/starimg/img/DJLUlcbDJo1618475210517.png",
        "refund_click_url": "https://page.udache.com/passenger/apps/bus-station-refund-intro/index.html?bus_service_shift_id={{bus_service_shift_id}}&rules_type={{rules_type}}&product_id={{product_id}}"
    }`).Build()
		hc := &headCard{
			product: createConditionData(&ticketPrice.RefundChargeRuleData{
				BeforeDepartureRules: []*ticketPrice.RefundChargeRule{},
			}),
			text:          text["head_card"].Map(),
			NewPageSwitch: true,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.NotNil(t, result, "不能为nil")
		assert.NotNil(t, result.PurchaseRulesButton, "按钮需要有值")
	})

	PatchConvey("test04 发车前涵盖规则", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{
        "refund_button_name": "退改签说明",
        "refund_right_icon": "https://pt-starimg.didistatic.com/static/starimg/img/DJLUlcbDJo1618475210517.png",
        "refund_click_url": "https://page.udache.com/passenger/apps/bus-station-refund-intro/index.html?bus_service_shift_id={{bus_service_shift_id}}&rules_type={{rules_type}}&product_id={{product_id}}"
    }`).Build()
		hc := &headCard{
			NewPageSwitch: true,
			text:          text["head_card"].Map(),
			product:       createConditionData(defaultRule),
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.NotNil(t, result, "不能为nil")
		assert.Len(t, result.RuleMsg, 1, "需要至少有1条")
		assert.Equal(t, result.RuleMsg[0].Text, "发车前退票费50%起")
	})

	PatchConvey("test05 规则为nil", t, func() {
		hc := &headCard{
			product: &biz_runtime.ProductInfoFull{
				Product: &models.Product{
					ShiftID: "123",
					BizInfo: &models.PrivateBizInfo{
						IntercityData: models.IntercityData{
							IntercityRule: nil,
						}}},
				BaseReqData: &models.BaseReqData{
					CommonBizInfo: models.CommonBizInfo{},
				},
			},
			text:          text["head_card"].Map(),
			NewPageSwitch: true,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.Nil(t, result, "需要为nil")
	})

	PatchConvey("test06 timeU", t, func() {
		hc := &headCard{
			product: createConditionData(&ticketPrice.RefundChargeRuleData{
				TimeUnit: util.StrPtr("hour"),
				BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
					{
						LeftTimeInterval:  24,
						RightTimeInterval: 48,
						RefundChargeRate:  50,
					},
				},
			}),
			text:          text["head_card"].Map(),
			NewPageSwitch: true,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.NotNil(t, result, "不需要为nil")
		assert.Len(t, result.RuleMsg, 1, "需要至少有1条")
		assert.Equal(t, result.RuleMsg[0].Text, "发车前退票费50%起")
	})

	PatchConvey("test06 rebookRule", t, func() {
		hc := &headCard{
			product:       product,
			text:          text["head_card"].Map(),
			NewPageSwitch: true,
		}
		result := hc.BuildRebookRefundInfo(ctx)
		assert.NotNil(t, result, "不需要为nil")
		assert.Len(t, result.RuleMsg, 2, "需要至少有2条")
		assert.Equal(t, result.RuleMsg[1].Text, "出发前10分钟可改签，改签后不可退")
	})

	PatchConvey("test07 NewHeadCard", t, func() {
		NewHeadCard(product, text["head_card"].Map(), true, true)
	})

	PatchConvey("test07 NewHeadCard", t, func() {
		hc := NewHeadCard(product, text["head_card"].Map(), true, true)
		result := hc.getHeadCard(ctx)
		assert.NotNil(t, result, "不为空")
	})
	PatchConvey("test08 buildRebookButton", t, func() {
		hc := NewHeadCard(product, text["head_card"].Map(), true, true)
		result := hc.buildRebookButton(ctx)
		assert.Nil(t, result, "为空")
	})

	PatchConvey("test09 getZeroMsgInfo-只有1条", t, func() {
		hc := NewHeadCard(product, text["head_card"].Map(), true, true)
		result := hc.getZeroMsgInfo(ctx, &ticketPrice.RefundChargeRule{}, []*ticketPrice.RefundChargeRule{}, text["head_card"].Map()["refund_msg_info"].Map(), "hour")
		assert.NotNil(t, result, "不为空")
		assert.Equal(t, result.Text, "发车前免费退票")
	})

	PatchConvey("test09 getZeroMsgInfo-只有1条", t, func() {
		hc := NewHeadCard(product, text["head_card"].Map(), true, true)
		lastBeforeRule := &ticketPrice.RefundChargeRule{
			LeftTimeInterval:  24,
			RightTimeInterval: 38,
			RefundChargeRate:  0,
		}
		result := hc.getZeroMsgInfo(ctx, lastBeforeRule, []*ticketPrice.RefundChargeRule{
			{
				LeftTimeInterval:  24,
				RightTimeInterval: 38,
				RefundChargeRate:  50,
			},
			{
				LeftTimeInterval:  24,
				RightTimeInterval: 38,
				RefundChargeRate:  0,
			},
		}, text["head_card"].Map()["refund_msg_info"].Map(), "hour")
		assert.NotNil(t, result, "不为空")
		assert.Equal(t, result.Text, "发车前1天免费退票.")
	})

}
func TestBuildStationInfo(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					RouteDetailV2: &models.RouteDetail{
						RouteBasicInfo: &Prfs.RouteBasicInfo{
							EarlyPickUpTimeEta: 30,
							StationList: []*Prfs.StationInfo{
								{
									StationId:        1,
									StationSceneType: 1,
								},
								{
									StationId: 2,
								},
							},
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				DepartureTime: 1750237876,
			},
			CommonBizInfo: models.CommonBizInfo{
				StartStationId: 1,
				EndStationId:   2,
			},
		},
	}
	headCardJSON := `{
    "head_card":  {
    "icon": "https://img-hxy021.didistatic.com/static/starimg/img/zGLv9myz6z1693203456283.png",
    "title": "该线路由粤运交通提供服务",
    "background_img": "https://img-hxy021.didistatic.com/static/starimg/img/BCc2pghqnT1693904525858.png",
    "title_img_new": "https://s3-gz01.didistatic.com/packages-mait/img/dBrPgWEcVM1732866540653.png",
    "rebook_button": {
      "text": "退改签规则",
      "link_url": ""
    },
    "new_rebook_button": {
      "text": "购票须知",
      "font_color": "#666666",
      "link_url": "",
      "background_gradients": [],
      "action_type": "2"
    },
    "rebook_msg_info": {
      "default": {
        "text": "改签规则点击购票须知查看",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default4": {
        "text": "检票前可改签一次",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default6": {
        "text": "不支持改签",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default7": {
        "text": "出发前10分钟可改签，改签后不可退",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      }
    },
    "refund_msg_info": {
      "zero_fee_info": {
        "once_rule": {
          "text": "发车前免费退票",
          "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
          "text_color": "#295DF3"
        },
        "more_rule": {
          "text": "发车前{{date_time}}{{unit_str}}免费退票。",
          "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
          "text_color": "#295DF3"
        }
      },
      "has_fee": {
        "text": "发车前退票费{{rate}}%起",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/ao-bi1fynXcZEGfXv4225.png",
        "text_color": "#295DF3"
      }
    },
    "station_info": {
      "title": "{{date}}{{{time}}}出发",
      "fence_bus_title": "预计{{{time}}}开始接人",
      "door_to_station_fence_bus_title": "{{month}}月{{day}}日{{{time}}}出发",
      "door_to_station_fence_bus_sub_title": "司机会{提前{{minute}}分钟}开始接人",
      "middle_title": "途经{{num}}个站点",
      "from_pick_up_method": "【上门接/送】",
      "to_pick_up_method": "【上门接/送】",
      "pick_up_method": "【上门接/送】",
      "popup_data": {
        "title": "途经{{num}}个站点",
        "hour_minute": "{{hour}}小时{{minute}}分钟",
        "only_minute": "{{minute}}分钟",
        "sub_title": "全程预计用时{{time}}",
        "departure_msg": "预计{{time}}出发",
        "start_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/MYJcAJqKMu1693210222572.png",
        "end_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/yTVgyhrjal1693210231395.png",
        "middle_station_icon": ""
      }
    }
  }
}`
	text := map[string]gjson.Result{
		"head_card": gjson.Result{
			Type: gjson.JSON,
			Raw:  headCardJSON,
		},
	}
	hc := &headCard{
		product:       product,
		text:          text["head_card"].Map()["head_card"].Map(),
		NewPageSwitch: false,
	}

	PatchConvey("test01 开关true-门到站", t, func() {
		patches := Mock(apollo.FeatureToggle).Return(true).Build()
		res := hc.buildStationInfo(ctx)
		assert.NotNil(t, res.SubTitle)
		assert.Equal(t, res.SubTitle, "司机会{提前30分钟}开始接人")
		patches.UnPatch()
	})

	PatchConvey("test01 开关true-站到门", t, func() {
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[0].StationSceneType = 0
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[1].StationSceneType = 1
		patches := Mock(apollo.FeatureToggle).Return(true).Build()
		res := hc.buildStationInfo(ctx)
		assert.Equal(t, res.SubTitle, "")
		assert.Equal(t, res.Title, "6月18日{17:11}出发")
		patches.UnPatch()
	})

	PatchConvey("test01 开关false-站到门", t, func() {
		patches := Mock(apollo.FeatureToggle).Return(false).Build()
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[0].StationSceneType = 0
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[1].StationSceneType = 1
		res := hc.buildStationInfo(ctx)
		assert.Equal(t, res.SubTitle, "")
		assert.Equal(t, res.Title, "预计{17:11}开始接人")
		patches.UnPatch()
	})

	PatchConvey("test01 开关false-门到站", t, func() {
		patches := Mock(apollo.FeatureToggle).Return(false).Build()
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[0].StationSceneType = 1
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[1].StationSceneType = 0
		res := hc.buildStationInfo(ctx)
		assert.Equal(t, res.SubTitle, "")
		assert.Equal(t, res.Title, "预计{17:11}开始接人")
		patches.UnPatch()
	})

	PatchConvey("test01 开关false-站到站", t, func() {
		patches := Mock(apollo.FeatureToggle).Return(false).Build()
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[0].StationSceneType = 99
		hc.product.Product.BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList[1].StationSceneType = 99
		res := hc.buildStationInfo(ctx)
		assert.Equal(t, res.SubTitle, "")
		assert.Equal(t, res.Title, "6月18日{17:11}出发")
		patches.UnPatch()
	})
}

func getStationJson() string {
	headCardJSON := `{
    "head_card":  {
    "icon": "https://img-hxy021.didistatic.com/static/starimg/img/zGLv9myz6z1693203456283.png",
    "title": "该线路由粤运交通提供服务",
    "background_img": "https://img-hxy021.didistatic.com/static/starimg/img/BCc2pghqnT1693904525858.png",
    "title_img_new": "https://s3-gz01.didistatic.com/packages-mait/img/dBrPgWEcVM1732866540653.png",
    "rebook_button": {
      "text": "退改签规则",
      "link_url": ""
    },
    "new_rebook_button": {
      "text": "购票须知",
      "font_color": "#666666",
      "link_url": "",
      "background_gradients": [],
      "action_type": "2"
    },
    "rebook_msg_info": {
      "default": {
        "text": "改签规则点击购票须知查看",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default4": {
        "text": "检票前可改签一次",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default6": {
        "text": "不支持改签",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      },
      "default7": {
        "text": "出发前10分钟可改签，改签后不可退",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
        "text_color": "#295DF3"
      }
    },
    "refund_msg_info": {
      "zero_fee_info": {
        "once_rule": {
          "text": "发车前免费退票",
          "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
          "text_color": "#295DF3"
        },
        "more_rule": {
          "text": "发车前{{date_time}}{{unit_str}}免费退票。",
          "icon": "https://dpubstatic.udache.com/static/dpubimg/_ZJ1tLA-QyULm5e4D5XPC.png",
          "text_color": "#295DF3"
        }
      },
      "has_fee": {
        "text": "发车前退票费{{rate}}%起",
        "icon": "https://dpubstatic.udache.com/static/dpubimg/ao-bi1fynXcZEGfXv4225.png",
        "text_color": "#295DF3"
      }
    },
    "station_info": {
      "title": "{{date}}{{{time}}}出发",
      "fence_bus_title": "预计{{{time}}}开始接人",
      "door_to_station_fence_bus_title": "{{month}}月{{day}}日{{{time}}}前出发",
      "door_to_station_fence_bus_sub_title": "司机会{提前{{minute}}分钟}开始接人",
      "middle_title": "途经{{num}}个站点",
      "from_pick_up_method": "【上门接/送】",
      "to_pick_up_method": "【上门接/送】",
      "pick_up_method": "【上门接/送】",
      "popup_data": {
        "title": "途经{{num}}个站点",
        "hour_minute": "{{hour}}小时{{minute}}分钟",
        "only_minute": "{{minute}}分钟",
        "sub_title": "全程预计用时{{time}}",
        "departure_msg": "预计{{time}}出发",
        "start_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/MYJcAJqKMu1693210222572.png",
        "end_station_icon": "https://img-hxy021.didistatic.com/static/starimg/img/yTVgyhrjal1693210231395.png",
        "middle_station_icon": ""
      }
    }
  }
}`
	return headCardJSON
}

func TestBuildStationOldInfo(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					RouteDetailV2: &models.RouteDetail{
						RouteBasicInfo: &Prfs.RouteBasicInfo{
							EarlyPickUpTimeEta: 30,
							StationList: []*Prfs.StationInfo{
								{
									StationId:        1,
									StationSceneType: 1,
								},
								{
									StationId: 2,
								},
							},
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				DepartureTime: 1750237876,
			},
			CommonBizInfo: models.CommonBizInfo{
				StartStationId: 1,
				EndStationId:   2,
			},
		},
	}

	text := map[string]gjson.Result{
		"head_card": gjson.Result{
			Type: gjson.JSON,
			Raw:  getStationJson(),
		},
	}
	hc := &headCard{
		product:       product,
		text:          text["head_card"].Map()["head_card"].Map(),
		NewPageSwitch: false,
	}
	patches := Mock(apollo.FeatureToggle).Return(false).Build()
	patches.UnPatch()
	res := hc.buildStationInfo(ctx)
	assert.NotNil(t, res.Title)
	assert.Equal(t, res.Title, "预计{17:11}开始接人")
}

func TestBuildStationOld2Info(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					RouteDetailV2: &models.RouteDetail{
						RouteBasicInfo: &Prfs.RouteBasicInfo{
							EarlyPickUpTimeEta: 30,
							StationList: []*Prfs.StationInfo{
								{
									StationId: 1,
								},
								{
									StationId:        2,
									StationSceneType: 1,
								},
							},
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				DepartureTime: 1750237876,
			},
			CommonBizInfo: models.CommonBizInfo{
				StartStationId: 1,
				EndStationId:   2,
			},
		},
	}

	text := map[string]gjson.Result{
		"head_card": gjson.Result{
			Type: gjson.JSON,
			Raw:  getStationJson(),
		},
	}
	hc := &headCard{
		product:       product,
		text:          text["head_card"].Map()["head_card"].Map(),
		NewPageSwitch: false,
	}
	patches := Mock(apollo.FeatureToggle).Return(false).Build()
	patches.UnPatch()
	res := hc.buildStationInfo(ctx)
	assert.NotNil(t, res.Title)
	assert.Equal(t, res.Title, "预计{17:11}开始接人")
}

func TestSortAscRule(t *testing.T) {
	Convey("测试sortAscRule方法", t, func() {
		hc := &headCard{}

		Convey("空规则切片", func() {
			rules := make([]*ticketPrice.RefundChargeRule, 0)
			result := hc.sortAscRule(rules)
			So(result, ShouldBeNil)
		})

		Convey("包含nil元素的规则切片", func() {
			rules := []*ticketPrice.RefundChargeRule{
				nil,
				{LeftTimeInterval: 30},
				nil,
				{LeftTimeInterval: 10},
			}
			result := hc.sortAscRule(rules)
			So(result, ShouldHaveLength, 2)
			So(result[0].LeftTimeInterval, ShouldEqual, 10)
			So(result[1].LeftTimeInterval, ShouldEqual, 30)
		})

		Convey("正常排序场景", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 50},
				{LeftTimeInterval: 20},
				{LeftTimeInterval: 30},
			}
			result := hc.sortAscRule(rules)
			So(result, ShouldHaveLength, 3)
			So(result[0].LeftTimeInterval, ShouldEqual, 20)
			So(result[1].LeftTimeInterval, ShouldEqual, 30)
			So(result[2].LeftTimeInterval, ShouldEqual, 50)
		})

		Convey("相同时间间隔的场景", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 10},
				{LeftTimeInterval: 10},
				{LeftTimeInterval: 5},
			}
			result := hc.sortAscRule(rules)
			So(result, ShouldHaveLength, 3)
			So(result[0].LeftTimeInterval, ShouldEqual, 5)
			So(result[1].LeftTimeInterval, ShouldEqual, 10)
			So(result[2].LeftTimeInterval, ShouldEqual, 10)
		})

		Convey("单个元素场景", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 100},
			}
			result := hc.sortAscRule(rules)
			So(result, ShouldHaveLength, 1)
			So(result[0].LeftTimeInterval, ShouldEqual, 100)
		})
	})
}

func TestMergeZeroRateRules(t *testing.T) {
	Convey("测试mergeZeroRateRules方法", t, func() {
		hc := &headCard{}

		Convey("空规则切片", func() {
			rules := make([]*ticketPrice.RefundChargeRule, 0)
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldBeNil)
		})

		Convey("单个规则", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 24, RightTimeInterval: 48, RefundChargeRate: 0},
			}
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldHaveLength, 1)
			So(result[0].RightTimeInterval, ShouldEqual, 48)
		})

		Convey("多个连续0费率规则", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 24, RightTimeInterval: 48, RefundChargeRate: 0},
				{LeftTimeInterval: 48, RightTimeInterval: 72, RefundChargeRate: 0},
				{LeftTimeInterval: 72, RightTimeInterval: 96, RefundChargeRate: 0},
			}
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldHaveLength, 1)
			So(result[0].RightTimeInterval, ShouldEqual, 96)
		})

		Convey("混合0费率和非0费率规则", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 24, RightTimeInterval: 48, RefundChargeRate: 0},
				{LeftTimeInterval: 48, RightTimeInterval: 72, RefundChargeRate: 50},
				{LeftTimeInterval: 72, RightTimeInterval: 96, RefundChargeRate: 0},
			}
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldHaveLength, 3)
			So(result[0].LeftTimeInterval, ShouldEqual, 24)
			So(result[2].RightTimeInterval, ShouldEqual, 96)
		})

		Convey("包含nil元素的规则切片", func() {
			rules := []*ticketPrice.RefundChargeRule{
				nil,
				{LeftTimeInterval: 24, RightTimeInterval: 48, RefundChargeRate: 0},
				nil,
				{LeftTimeInterval: 48, RightTimeInterval: 72, RefundChargeRate: 0},
			}
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldHaveLength, 2)
			So(result[0].RightTimeInterval, ShouldEqual, 48)
		})

		Convey("时间不连续的0费率规则", func() {
			rules := []*ticketPrice.RefundChargeRule{
				{LeftTimeInterval: 24, RightTimeInterval: 48, RefundChargeRate: 0},
				{LeftTimeInterval: 49, RightTimeInterval: 72, RefundChargeRate: 0},
			}
			result := hc.mergeZeroRateRules(rules)
			So(result, ShouldHaveLength, 2)
			So(result[1].RightTimeInterval, ShouldEqual, 72)
		})
	})
}
