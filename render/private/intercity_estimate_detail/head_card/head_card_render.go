package head_card

import (
	"context"
	"sort"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/door_and_station"

	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	From                   = 0
	To                     = 1
	FromPickUpMethod       = "from_pick_up_method"
	ToPickUpMethod         = "to_pick_up_method"
	AccurateStation        = 0
	FenceStation           = 1
	Default                = "default"
	defaultRenderFormat    = "DEFAULT"
	anyCarFlowRenderFormat = "ANYCAR_FLOW"
)

type headCard struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result

	stationNum             int
	isNewVersionAnyCarFlow bool
	NewPageSwitch          bool
}

// NewHeadCard ...
func NewHeadCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, isNewVersionAnyCarFlow, NewPageSwitch bool) *headCard {
	return &headCard{
		product:                product,
		text:                   text,
		isNewVersionAnyCarFlow: isNewVersionAnyCarFlow,
		NewPageSwitch:          NewPageSwitch,
	}
}

func (hc *headCard) BuildNewHeadInfo(ctx context.Context) (newTitleImg string, templateName string) {
	if hc.isNewVersionAnyCarFlow && hc.product.BaseReqData.CommonBizInfo.IsBestShift != 0 {
		// 导流位最优班次进入的确认购票页
		templateName = anyCarFlowRenderFormat
		newTitleImg = hc.text["title_img_new"].String()
	} else {
		templateName = defaultRenderFormat
		newTitleImg = ""
	}
	return newTitleImg, templateName
}

func (hc *headCard) BuildRebookRefundInfo(ctx context.Context) *proto.RebookRefundRuleInfo {
	if !hc.NewPageSwitch {
		return nil
	}
	rule := hc.product.GetBizInfo().IntercityRule
	if rule == nil {
		return nil
	}
	refundChargeRule := rule.RefundChargeRule
	var refundChargeMsgInfo *proto.RuleMsgInfo
	if refundChargeRule != nil {
		timeUnit := "hour"
		if refundChargeRule.TimeUnit != nil {
			timeUnit = *refundChargeRule.TimeUnit
		}

		refundChargeMsgInfo = hc.buildRefundRule(ctx, refundChargeRule, timeUnit)
	}

	rebookChargeRule := rule.RebookChargeRule
	var rebookMsgInfo *proto.RuleMsgInfo
	if rebookChargeRule != nil && len(*rebookChargeRule.RebookRuleId) != 0 {
		rebookMsgInfo = hc.buildRebookRule(rebookChargeRule)
	}

	return hc.buildMsgInfo(ctx, rebookMsgInfo, refundChargeMsgInfo)
}

func (hc *headCard) Render(ctx context.Context) *proto.HeadCard {
	return hc.getHeadCard(ctx)
}

// GetHeadCard ...
func (hc *headCard) getHeadCard(ctx context.Context) *proto.HeadCard {
	if hc.text == nil {
		return nil
	}
	businessName, icon := car_info.GetBusinessNameAndIcon(ctx, hc.product)
	title, backgroundImg := hc.GetBaseInfo(ctx, businessName)
	if door_and_station.HitNewStyle(ctx, hc.product) { //新版本统一不需要展示backgroundImg
		backgroundImg = ""
	}
	newTitleImg, templateName := hc.BuildNewHeadInfo(ctx)
	return &proto.HeadCard{
		Icon:                 icon,
		Title:                title,
		BackgroundImg:        backgroundImg,
		RebookButton:         hc.buildRebookButton(ctx),
		StationInfo:          hc.buildStationInfo(ctx),
		CarPlate:             &hc.product.BaseReqData.CommonBizInfo.CarPlate,
		TemplateName:         templateName,
		TitleImgNew:          newTitleImg,
		RebookRefundRuleInfo: hc.BuildRebookRefundInfo(ctx),
	}
}

// buildButton ...
func (hc *headCard) buildRebookButton(ctx context.Context) *proto.Button {
	if hc.text == nil || hc.product == nil || hc.product.Product == nil {
		return nil
	}
	if hc.NewPageSwitch {
		return nil
	}
	var (
		linkUrl string
		shiftID string
	)

	shiftID = hc.product.Product.ShiftID
	chargeRuleStr := dcmp.GetDcmpContent(ctx, intercity.ChargeRuleEntranceDcmpKey, nil)
	if chargeRuleStr != "" && gjson.Get(chargeRuleStr, "refund_click_url").String() != "" {
		refundClickUrl := gjson.Get(chargeRuleStr, "refund_click_url").String()
		linkUrl = util.ReplaceTag(ctx, refundClickUrl, map[string]string{
			"bus_service_shift_id": shiftID,
			"rules_type":           util.Int32String(intercity_estimate_detail.RulesTypeRefund),
			"product_id":           util.Int642String(hc.product.GetProductId()),
		})
	}

	return &proto.Button{
		Text:                hc.text["rebook_button"].Map()["text"].String(),
		FontColor:           util.StringPtr(hc.text["rebook_button"].Map()["font_color"].String()),
		LinkUrl:             util.StringPtr(linkUrl),
		BackgroundGradients: nil,
		ActionType:          util.Int32Ptr(int32(hc.text["rebook_button"].Map()["action_type"].Int())),
	}
}

// buildStationInfo ...
func (hc *headCard) buildStationInfo(ctx context.Context) *proto.StationFullInfo {
	if hc.text == nil || hc.product == nil || hc.product.GetBizInfo() == nil || hc.product.BaseReqData == nil {
		return nil
	}

	var (
		startStationID int64
		endStationID   int64
		startIndex     int
		endIndex       int
		startStation   *Prfs.StationInfo
		endStation     *Prfs.StationInfo
	)

	startStationID = hc.product.BaseReqData.CommonBizInfo.StartStationId
	endStationID = hc.product.BaseReqData.CommonBizInfo.EndStationId

	routeDetail := hc.product.GetBizInfo().RouteDetailV2
	if routeDetail == nil || routeDetail.RouteBasicInfo == nil || len(routeDetail.RouteBasicInfo.StationList) <= 0 {
		return nil
	}

	stationList := routeDetail.RouteBasicInfo.StationList
	for index, stationItem := range stationList {
		if stationItem.StationId == int32(startStationID) {
			startStation = stationItem
			startIndex = index
		}

		if stationItem.StationId == int32(endStationID) {
			endStation = stationItem
			endIndex = index
		}
	}

	if startIndex == 0 {
		startStation = stationList[0]
		startIndex = 0
	}
	if endIndex == 0 {
		endStation = stationList[len(stationList)-1]
		endIndex = len(stationList) - 1
	}

	res := &proto.StationFullInfo{
		StartStation: hc.buildSingleStation(ctx, startStation, From),
		EndStation:   hc.buildSingleStation(ctx, endStation, To),
	}
	// 新版本-{{month}}月{{day}}日{{{time}}}前出发
	if door_and_station.HitNewStyle(ctx, hc.product) {
		departTime := time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0)
		res.Title = util.ReplaceTag(ctx, hc.text["station_info"].Map()["door_to_station_fence_bus_title"].String(), map[string]string{
			"month": cast.ToString(int(departTime.Month())),
			"day":   cast.ToString(departTime.Day()),
			"time":  time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0).Format("15:04"),
		})
		bizInfo := hc.product.GetBizInfo()
		if getStationSceneType(startStation) == FenceStation && bizInfo.RouteDetailV2 != nil && bizInfo.RouteDetailV2.RouteBasicInfo != nil &&
			bizInfo.RouteDetailV2.RouteBasicInfo.EarlyPickUpTimeEta > 0 {
			res.SubTitle = util.ReplaceTag(ctx, hc.text["station_info"].Map()["door_to_station_fence_bus_sub_title"].String(), map[string]string{
				"minute": cast.ToString(bizInfo.RouteDetailV2.RouteBasicInfo.EarlyPickUpTimeEta),
			})
		}
	} else if getStationSceneType(startStation) == FenceStation || getStationSceneType(endStation) == FenceStation {
		res.Title = util.ReplaceTag(ctx, hc.text["station_info"].Map()["fence_bus_title"].String(), map[string]string{
			"time": time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0).Format("15:04"),
		})
	} else {
		res.Title = util.ReplaceTag(ctx, hc.text["station_info"].Map()["title"].String(), map[string]string{
			"date": time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0).Format("1月2日"),
			"time": time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0).Format("15:04"),
		})
	}

	// 兜底用户扫线路码进来，胡选站点引发的数组越界，等prfs针对线路码的过滤完上下站点后可以下掉了
	if startIndex > endIndex {
		startIndex, endIndex = endIndex, startIndex
	}
	resStationList := stationList[startIndex : endIndex+1]
	hc.stationNum = len(resStationList)
	if hc.stationNum-2 >= 0 {
		hc.stationNum = len(resStationList) - 2
	}
	res.MiddleStation = hc.buildMiddleStation(ctx, resStationList)

	return res
}

func getStationSceneType(station *Prfs.StationInfo) int32 {
	if station == nil {
		return AccurateStation
	}
	return station.StationSceneType
}

// buildMiddleData ...
func (hc *headCard) buildMiddleStation(ctx context.Context, stationList []*Prfs.StationInfo) *proto.MiddleStation {
	if hc.text == nil {
		return nil
	}

	res := &proto.MiddleStation{
		Title: util.ReplaceTag(ctx, hc.text["station_info"].Map()["middle_title"].String(), map[string]string{
			"num": util.Int2String(hc.stationNum),
		}),
	}

	// 支持修改站点不展示途经点
	if !hc.product.BaseReqData.CommonBizInfo.StationInfo.SupportCorrectStationId {
		res.PopupDetail = hc.buildPopupDetail(ctx, stationList)
	}
	return res
}

// buildPopupDetail ...
func (hc *headCard) buildPopupDetail(ctx context.Context, stationList []*Prfs.StationInfo) *proto.PopupDetail {
	if hc.product == nil || hc.product.BaseReqData == nil {
		return nil
	}

	var (
		etaSum int32

		resStationList = make([]*proto.StationItem, 0)
		timeStr        string
	)

	curTime := time.Unix(hc.product.BaseReqData.CommonInfo.DepartureTime, 0)

	for index, stationItem := range stationList {
		if stationItem == nil {
			continue
		}

		if index != 0 {
			etaSum += stationItem.Eta
			curTime = curTime.Add(time.Duration(stationItem.Eta) * time.Minute)
		}

		resStationItem := hc.buildStationItem(ctx, stationItem, curTime)
		if resStationItem == nil {
			continue
		}

		resStationList = append(resStationList, resStationItem)
	}

	if time.Duration(etaSum)*time.Minute > time.Hour {
		timeStr = util.ReplaceTag(ctx, hc.text["station_info"].Map()["popup_data"].Map()["hour_minute"].String(), map[string]string{
			"hour":   util.Int32String(etaSum / 60),
			"minute": util.Int32String(etaSum % 60),
		})
	} else {
		timeStr = util.ReplaceTag(ctx, hc.text["station_info"].Map()["popup_data"].Map()["only_minute"].String(), map[string]string{
			"minute": util.Int32String(etaSum),
		})
	}

	return &proto.PopupDetail{
		Title: util.ReplaceTag(ctx, hc.text["station_info"].Map()["popup_data"].Map()["title"].String(), map[string]string{
			"num": util.Int2String(hc.stationNum),
		}),
		Subtitle: util.ReplaceTag(ctx, hc.text["station_info"].Map()["popup_data"].Map()["sub_title"].String(), map[string]string{
			"time": timeStr,
		}),
		StationList: resStationList,
	}
}

// buildSingleStation ...
func (hc *headCard) buildSingleStation(ctx context.Context, station *Prfs.StationInfo, model int32) *proto.StationDataInfo {
	res := &proto.StationDataInfo{
		Lat: util.Float64Ptr(util.String2float64(ctx, station.StationLat)),
		Lng: util.Float64Ptr(util.String2float64(ctx, station.StationLng)),
	}
	if model == From && len(hc.product.GetCommonBizInfo().StationInfo.StartPoiName) > 0 && hc.product.GetCommonBizInfo().StartStation != nil && hc.product.GetCommonBizInfo().StartStation.StationSceneType == FenceStation {
		res.DisplayName = hc.text["station_info"].Map()[FromPickUpMethod].String() + hc.product.GetCommonBizInfo().StationInfo.StartPoiName
	} else if model == To && len(hc.product.GetCommonBizInfo().StationInfo.EndPoiName) > 0 && hc.product.GetCommonBizInfo().EndStation != nil && hc.product.GetCommonBizInfo().EndStation.StationSceneType == FenceStation {
		res.DisplayName = hc.text["station_info"].Map()[ToPickUpMethod].String() + hc.product.GetCommonBizInfo().StationInfo.EndPoiName
	}
	if len(res.DisplayName) == 0 {
		res.DisplayName = station.StationName
	}
	return res
}

// buildStationItem ...
func (hc *headCard) buildStationItem(ctx context.Context, stationItem *Prfs.StationInfo, curTime time.Time) *proto.StationItem {
	if hc.text == nil {
		return nil
	}

	resStationItem := &proto.StationItem{
		DisplayName: stationItem.StationName,
		DepartureMsg: util.ReplaceTag(ctx, hc.text["station_info"].Map()["popup_data"].Map()["departure_msg"].String(), map[string]string{
			"time": curTime.Format("15:04"),
		}),
		Type: util.String2int32(ctx, stationItem.StationType),
	}

	if stationItem.StationType == intercity_estimate_detail.AlightingPoint.ToString() {
		resStationItem.DepartureMsg = ""
	}

	if int64(stationItem.StationId) == hc.product.BaseReqData.CommonBizInfo.StartStationId {
		resStationItem.Icon = hc.text["station_info"].Map()["popup_data"].Map()["start_station_icon"].String()
	} else if int64(stationItem.StationId) == hc.product.BaseReqData.CommonBizInfo.EndStationId {
		resStationItem.Icon = hc.text["station_info"].Map()["popup_data"].Map()["end_station_icon"].String()
		resStationItem.DepartureMsg = ""
	} else {
		resStationItem.Icon = hc.text["station_info"].Map()["popup_data"].Map()["middle_station_icon"].String()
	}

	return resStationItem
}

// GetBaseInfo ...
func (hc *headCard) GetBaseInfo(ctx context.Context, businessName string) (title string, backgroundImg string) {
	if hc.product == nil {
		return "", ""
	}

	baseInfo := dcmp.GetJSONMap(ctx, "intercity_estimate-base_info", strconv.Itoa(int(hc.product.GetProductCategory())))
	if len(baseInfo) == 0 {
		baseInfo = dcmp.GetJSONMap(ctx, "intercity_estimate-base_info", Default)
		title = dcmp.TranslateTemplate(baseInfo["title"].String(), map[string]string{"product_name": businessName})
	} else {
		title = baseInfo["title"].String()
	}
	return title, baseInfo["background_img"].String()
}

func (hc *headCard) buildRebookRule(rebookRule *ticketPrice.RebookChargeRuleData) *proto.RuleMsgInfo {
	// 支持改签一次且不可退票-出发前10分钟可改签，改签后不可退
	// 支持改签一次-检票前可改签一次
	// 不支持改签-不支持改签
	// 兜底-改签规则点击购票须知查看
	RebookRuleId := cast.ToString(rebookRule.RebookRuleId)
	ruleTextConfig := hc.text["rebook_msg_info"].Map()["default"].Map()
	if len(hc.text["rebook_msg_info"].Map()[RebookRuleId].Map()) != 0 {
		ruleTextConfig = hc.text["rebook_msg_info"].Map()[RebookRuleId].Map()
	}
	if len(ruleTextConfig) == 0 {
		return nil
	}

	return &proto.RuleMsgInfo{
		Text:      ruleTextConfig["text"].String(),
		Icon:      util.StringPtr(ruleTextConfig["icon"].String()),
		TextColor: util.StringPtr(ruleTextConfig["text_color"].String()),
	}
}

func (hc *headCard) buildRefundRule(ctx context.Context, refundRule *ticketPrice.RefundChargeRuleData, unit string) *proto.RuleMsgInfo {
	if len(refundRule.BeforeDepartureRules) == 0 {
		return nil
	}
	ruleTextConfig := hc.text["refund_msg_info"].Map()
	if len(ruleTextConfig) == 0 {
		return nil
	}
	// 合并费率为0的规则
	sortRules := hc.mergeZeroRateRules(refundRule.BeforeDepartureRules)
	if len(sortRules) == 0 {
		return nil
	}
	lastBeforeRule := sortRules[len(sortRules)-1]
	refundChargeRate := lastBeforeRule.RefundChargeRate
	// 0 -无 退票费
	if intercity_estimate_detail.ZeroRule == refundChargeRate {
		return hc.getZeroMsgInfo(ctx, lastBeforeRule, sortRules, ruleTextConfig, unit)
	}
	// 非0 -有 退票费
	return hc.renderMsgShow(ctx, ruleTextConfig["has_fee"].Map(), true, map[string]string{
		"rate": cast.ToString(refundChargeRate),
	})
}

func (hc *headCard) sortAscRule(rules []*ticketPrice.RefundChargeRule) []*ticketPrice.RefundChargeRule {
	if len(rules) == 0 {
		return nil
	}
	nonNilRules := make([]*ticketPrice.RefundChargeRule, 0)
	for _, rule := range rules {
		if rule != nil {
			nonNilRules = append(nonNilRules, rule)
		}
	}
	// 从小到大时间进行排序
	sort.Slice(nonNilRules, func(i, j int) bool {
		return nonNilRules[i].LeftTimeInterval < nonNilRules[j].LeftTimeInterval
	})
	return nonNilRules
}

// 合并连续0费率的规则
func (hc *headCard) mergeZeroRateRules(rules []*ticketPrice.RefundChargeRule) []*ticketPrice.RefundChargeRule {
	if len(rules) == 0 {
		return nil
	}
	nonNilRules := hc.sortAscRule(rules)
	if len(nonNilRules) == 0 {
		return nil
	}
	var merged []*ticketPrice.RefundChargeRule
	current := nonNilRules[0] // 当前合并中的规则

	for i := 1; i < len(nonNilRules); i++ {
		// 检查是否满足合并条件：费率0、时间连续且其他字段相同
		if current.RefundChargeRate == 0 &&
			rules[i].RefundChargeRate == 0 &&
			current.RightTimeInterval == rules[i].LeftTimeInterval {

			// 扩展当前规则的时间区间
			current.RightTimeInterval = rules[i].RightTimeInterval
		} else {
			// 保存当前规则，开始新的合并
			merged = append(merged, current)
			current = rules[i]
		}
	}

	// 添加最后一个规则
	merged = append(merged, current)

	return merged
}

func (hc *headCard) getZeroMsgInfo(ctx context.Context, lastBeforeRule *ticketPrice.RefundChargeRule, refundRule []*ticketPrice.RefundChargeRule, ruleTextConfig map[string]gjson.Result, unit string) *proto.RuleMsgInfo {
	dayHour, unitStr := hc.getRuleTimeUtil(lastBeforeRule.GetLeftTimeInterval(), unit)
	// 只有1条时，文案展示为：发车前免费退票
	if len(refundRule) == intercity_estimate_detail.LastIndexFlagBit || cast.ToInt(dayHour) == intercity_estimate_detail.ZeroRule {
		return hc.renderMsgShow(ctx, ruleTextConfig["zero_fee_info"].Map()["once_rule"].Map(), false, nil)
	}
	// 发车前{{date_time}}{{unit_str}}免费退票.
	moreRule := ruleTextConfig["zero_fee_info"].Map()["more_rule"].Map()
	return hc.renderMsgShow(ctx, moreRule, true, map[string]string{
		"date_time": dayHour,
		"unit_str":  unitStr,
	})
}

func (hc *headCard) buildMsgInfo(ctx context.Context, rebookMsg, refundMsg *proto.RuleMsgInfo) *proto.RebookRefundRuleInfo {
	ruleMsgList := make([]*proto.RuleMsgInfo, 0)
	if refundMsg != nil {
		ruleMsgList = append(ruleMsgList, refundMsg)
	}
	if rebookMsg != nil {
		ruleMsgList = append(ruleMsgList, rebookMsg)
	}
	rebookButtonConfig := hc.text["new_rebook_button"].Map()
	list := rebookButtonConfig["background_gradients"].Array()
	var backgroundGradients []string
	if len(list) != 0 {
		for _, color := range list {
			backgroundGradients = append(backgroundGradients, cast.ToString(color))
		}
	}
	var linkUrl string
	chargeRuleStr := dcmp.GetDcmpContent(ctx, intercity.ChargeRuleEntranceDcmpKey, nil)
	if chargeRuleStr != "" && gjson.Get(chargeRuleStr, "refund_click_url").String() != "" {
		refundClickUrl := gjson.Get(chargeRuleStr, "refund_click_url").String()
		linkUrl = util.ReplaceTag(ctx, refundClickUrl, map[string]string{
			"bus_service_shift_id": hc.product.Product.ShiftID,
			"rules_type":           util.Int32String(intercity_estimate_detail.RulesTypeRefund),
			"product_id":           util.Int642String(hc.product.GetProductId()),
		})

	}
	if len(linkUrl) == 0 {
		return &proto.RebookRefundRuleInfo{
			RuleMsg: ruleMsgList,
		}
	}

	rebookButton := &proto.Button{
		Text:                rebookButtonConfig["text"].String(),
		FontColor:           util.String2PtrString(rebookButtonConfig["font_color"].String()),
		LinkUrl:             util.String2PtrString(linkUrl),
		BackgroundGradients: backgroundGradients,
		ActionType:          util.Int32Ptr(cast.ToInt32(rebookButtonConfig["action_type"].String())),
	}

	return &proto.RebookRefundRuleInfo{
		RuleMsg:             ruleMsgList,
		PurchaseRulesButton: rebookButton,
	}
}

func (hc *headCard) getRuleTimeUtil(rightTimeInterval int64, unit string) (string, string) {
	second := int64(3600)
	if unit == "min" {
		second = 60
	}
	var dayHour int64
	var unitStr string
	rightTimeInterval = rightTimeInterval * second
	if rightTimeInterval%intercity_estimate_detail.OneDateTime == 0 {
		dayHour = rightTimeInterval / intercity_estimate_detail.OneDateTime
		unitStr = intercity_estimate_detail.OneDateTimeStr
	} else {
		dayHour = rightTimeInterval / intercity_estimate_detail.OneHour
		unitStr = intercity_estimate_detail.OneHourStr
	}
	return cast.ToString(dayHour), unitStr
}

func (hc *headCard) renderMsgShow(ctx context.Context, textConfig map[string]gjson.Result, isHasReplace bool, replaceMap map[string]string) *proto.RuleMsgInfo {
	text := textConfig["text"].String()
	msgInfo := &proto.RuleMsgInfo{
		Text:      text,
		Icon:      util.StringPtr(textConfig["icon"].String()),
		TextColor: util.StringPtr(textConfig["text_color"].String()),
	}
	if isHasReplace && len(replaceMap) != 0 {
		msgInfo.Text = util.ReplaceTag(ctx, text, replaceMap)
	}
	return msgInfo
}
