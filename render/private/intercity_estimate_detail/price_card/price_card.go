package price_card

import (
	"context"
	"fmt"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"sort"

	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	StartStationType = "1"
	EndStationType   = "2"
)

type priceCard struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

// NewpriceCard ...
func NewPriceCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *priceCard {
	return &priceCard{
		product: product,
		text:    text,
	}
}

func (hc *priceCard) Render(ctx context.Context) *proto.PriceCard {
	return hc.getPriceCard(ctx)
}

func getPriceList(data []*ticketPrice.StationPriceItem) []float64 {
	tmp := make(map[float64]int32)
	var res []float64
	for _, v := range data {
		if _, ok := tmp[v.Price]; ok {
			continue
		} else {
			tmp[v.Price] = 1
			res = append(res, v.Price)
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i] < res[j] // 从低到高排序
	})
	return res
}

func (hc *priceCard) buildPriceItem(fee float64) *proto.PriceItem {
	resItem := &proto.PriceItem{}
	if fee < 0 {
		resItem.FeeAmount = fee
		resItem.FeeMsg = hc.text["price_item"].Map()["default_item"].String()
	} else {
		resItem.FeeAmount = fee
		resItem.FeeMsg = dcmp.TranslateTemplate(hc.text["price_item"].Map()["valid_item"].String(), map[string]string{
			"num": fmt.Sprintf("%.2f", fee),
		})
	}
	return resItem
}

func (hc *priceCard) buildPriceItems(fees []float64) []*proto.PriceItem {
	if len(fees) == 0 {
		return nil
	}
	var res []*proto.PriceItem
	for _, fee := range fees {
		res = append(res, hc.buildPriceItem(fee))
	}
	return res
}

func (hc *priceCard) getPriceCard(ctx context.Context) *proto.PriceCard {
	priceInfo := hc.product.BaseReqData.CommonBizInfo.RoutePrice
	if hc.product.BaseReqData.CommonBizInfo.AgentType == DetailConst.FormatScanCode ||
		priceInfo == nil {
		return nil
	}
	var (
		introMsg string
	)
	res := &proto.PriceCard{}
	priceList := getPriceList(priceInfo.StationPrice)
	// 一口价list为空，需要将一口价append
	if priceInfo.IsPrice == DetailConst.CapPrice {
		priceList = append(priceList, priceInfo.Price)
	}
	if len(priceList) > 9 {
		priceList = append(priceList, -1)
	}

	tmpPriceItems := hc.buildPriceItems(priceList)
	if len(tmpPriceItems) > 9 {
		introMsg = hc.text["fee_msg"].Map()["outer_price_intro"].String()
		newPriceItems := make([]*proto.PriceItem, len(tmpPriceItems))
		copy(newPriceItems, tmpPriceItems)
		res.ExtraPriceCard = &proto.PriceCardItem{
			PriceItems: newPriceItems[:len(tmpPriceItems)-1],
			IntroMsg:   &introMsg,
		}
		defaultPriceItem := tmpPriceItems[len(tmpPriceItems)-1]
		tmpPriceItems = tmpPriceItems[0:8]
		tmpPriceItems = append(tmpPriceItems, defaultPriceItem)
	}
	res.OuterPriceCard = &proto.PriceCardItem{
		ShowTieredPrice: priceInfo.IsPrice == DetailConst.TieredPrice,
		PriceItems:      tmpPriceItems,
	}
	if len(res.OuterPriceCard.PriceItems) == 1 {
		if priceInfo.IsPrice == DetailConst.CapPrice {
			introMsg = hc.text["fee_msg"].Map()["cap_price_intro"].String()
		} else {
			introMsg = hc.text["fee_msg"].Map()["tiried_price_intro"].String()
		}
	} else if len(res.OuterPriceCard.PriceItems) > 1 {
		if hc.product.BaseReqData.CommonBizInfo.AdultPrice == 0 {
			introMsg = hc.text["fee_msg"].Map()["outer_price_intro"].String()
		} else {
			introMsg = hc.text["fee_msg"].Map()["tiried_price_intro"].String()
		}
	}
	res.OuterPriceCard.IntroMsg = &introMsg
	return res
}
