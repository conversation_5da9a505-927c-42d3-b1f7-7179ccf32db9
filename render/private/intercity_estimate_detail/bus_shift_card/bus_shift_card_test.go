package bus_shift_card

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_multi_station"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/distance_render"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/apolloconf/bus_shift"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"github.com/bytedance/mockey"
	"github.com/spf13/cast"
	"testing"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

func TestBusShiftCard_Render(t *testing.T) {
	Convey("测试BusShiftCard的Render方法", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// 使用mockey打桩
		getDcmpContentMock := mockey.Mock(dcmp.GetDcmpContent).To(func(ctx context.Context, key string, params map[string]string) string {
			switch key {
			case "intercity_station-stationinfo":
				return `{
					"start_distance": "距您{{distance}}",
					"end_distance": "距终点{{distance}}",
					"start_portrait_icon": "https://example.com/start_icon.png",
					"end_portrait_icon": "https://example.com/end_icon.png",
					"hour": "小时",
					"min": "分钟",
					"title": "途经{{n}}站"
				}`
			case dcmpBusShiftCardKey:
				return `{
					"time_msg": "{{date}} {{time}}出发",
					"jump_button": {
						"url": "didi://intercity_station/bus_shift_list?start_station_id={{start_station_id}}&end_station_id={{end_station_id}}&start_county_id={{start_county_id}}&end_county_id={{end_county_id}}&start_city={{start_city}}&end_city={{end_city}}&client_type={{client_type}}&page_type={{page_type}}&channel={{channel}}&day_time={{day_time}}&trans_data={{trans_data}}&omg_data={{omg_data}}",
						"url_v2": "didi://intercity_station/bus_shift_list_v2?start_poi_id={{start_poi_id}}&start_poi_name={{start_poi_name}}&start_poi_city={{start_poi_city}}&start_poi_city_name={{start_poi_city_name}}&start_lat={{start_lat}}&start_lng={{start_lng}}&end_poi_id={{end_poi_id}}&end_poi_name={{end_poi_name}}&end_poi_city={{end_poi_city}}&end_poi_city_name={{end_poi_city_name}}&end_lat={{end_lat}}&end_lng={{end_lng}}&start_station_id={{start_station_id}}&end_station_id={{end_station_id}}&client_type={{client_type}}&page_type={{page_type}}&channel={{channel}}&day_time={{day_time}}&trans_data={{trans_data}}&omg_data={{omg_data}}",
						"text": "查看更多班次"
					}
				}`
			default:
				return ""
			}
		}).Build()
		defer getDcmpContentMock.UnPatch()

		// 模拟dcmp.GetDcmpPlainContent
		getDcmpPlainContent := mockey.Mock(dcmp.GetDcmpPlainContent).To(func(ctx context.Context, key string) string {
			if key == "intercity_station-suffix" {
				return `{
					"color": "#666666"
				}`
			}
			return ""
		}).Build()
		defer getDcmpPlainContent.UnPatch()

		// 模拟dcmp.TranslateTemplate
		translateTemplateMock := mockey.Mock(dcmp.TranslateTemplate).To(func(template string, params map[string]string) string {
			return template // 简化处理，直接返回模板
		}).Build()
		defer translateTemplateMock.UnPatch()

		// 模拟bus_shift.GetTagNameByTagId
		getTagNameByTagIdMock := mockey.Mock(bus_shift.GetTagNameByTagId).To(func(ctx context.Context, tagId string) string {
			return "舒适大巴"
		}).Build()
		defer getTagNameByTagIdMock.UnPatch()

		// 模拟util.IsToday
		isTodayMock := mockey.Mock(util.IsToday).To(func(timestamp int64) bool {
			return true
		}).Build()
		defer isTodayMock.UnPatch()

		// 模拟util.EarthDistance
		earthDistanceMock := mockey.Mock(util.EarthDistance).To(func(lat1, lng1, lat2, lng2 float64) float64 {
			return 1000.0
		}).Build()
		defer earthDistanceMock.UnPatch()

		// 模拟util.LessOrEqualThanZero
		lessOrEqualThanZeroMock := mockey.Mock(util.LessOrEqualThanZero).To(func(f float64) bool {
			return false
		}).Build()
		defer lessOrEqualThanZeroMock.UnPatch()

		// 模拟distance_render.GetDistanceInfo
		getDistanceInfoMock := mockey.Mock(distance_render.GetDistanceInfo).To(func(distance int64) (string, string) {
			return "1公里", "distance"
		}).Build()
		defer getDistanceInfoMock.UnPatch()

		// 模拟intercity_multi_station.DetailTitle
		detailTitleMock := mockey.Mock(intercity_multi_station.DetailTitle).To(func(ctx context.Context, minRes string) string {
			return "约" + minRes
		}).Build()
		defer detailTitleMock.UnPatch()

		// 模拟SwitchToNewMainCardEstimate
		switchToNewMainCardEstimateMock := mockey.Mock(SwitchToNewMainCardEstimate).To(func(ctx context.Context, phone string, pid string, accessKeyId string, appVersion string, cityId string) bool {
			return true
		}).Build()
		defer switchToNewMainCardEstimateMock.UnPatch()

		// 模拟apollo.FeatureToggle
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return true
		}).Build()
		defer featureToggleMock.UnPatch()

		// 模拟util.String2PtrString
		string2PtrStringMock := mockey.Mock(util.String2PtrString).To(func(s string) *string {
			return &s
		}).Build()
		defer string2PtrStringMock.UnPatch()

		Convey("当不是新版任意车流程时，应返回nil", func() {
			ctx := context.Background()
			product := createTestProduct()
			commonData := &model.CommonData{}
			isNewVersionAnyCarFlow := false
			req := createTestRequest()

			card := NewBusShiftCard(product, commonData, isNewVersionAnyCarFlow)
			result := card.Render(ctx, req)

			So(result, ShouldBeNil)
		})

		Convey("当不是最优班次时，应返回nil", func() {
			ctx := context.Background()
			product := createTestProduct()
			commonData := &model.CommonData{}
			isNewVersionAnyCarFlow := true
			req := createTestRequest()
			req.IsBestShift = int32Ptr(0)

			card := NewBusShiftCard(product, commonData, isNewVersionAnyCarFlow)
			result := card.Render(ctx, req)

			So(result, ShouldBeNil)
		})

		Convey("正常渲染场景", func() {
			ctx := context.Background()
			product := createTestProduct()
			commonData := &model.CommonData{
				Quotation: &biz_runtime.Quotation{
					ShiftId: stringPtr("12345"),
				},
			}
			isNewVersionAnyCarFlow := true
			req := createTestRequest()

			card := NewBusShiftCard(product, commonData, isNewVersionAnyCarFlow)
			result := card.Render(ctx, req)

			So(result, ShouldNotBeNil)
			So(result.TimeMsg, ShouldNotBeEmpty)
			So(result.StationInfo, ShouldNotBeNil)
			So(result.StationInfo.Start, ShouldNotBeNil)
			So(result.StationInfo.End, ShouldNotBeNil)
			So(result.StationInfo.Detail, ShouldNotBeNil)
			So(result.JumpButton, ShouldNotBeNil)
			So(result.OmegaInfo, ShouldNotBeNil)
		})

		Convey("测试围栏站点场景", func() {
			ctx := context.Background()
			product := createTestProduct()
			// 设置起点为围栏站点
			product.BaseReqData.CommonBizInfo.StartStation.StationSceneType = FenceStation
			// 设置终点为围栏站点
			product.BaseReqData.CommonBizInfo.EndStation.StationSceneType = FenceStation

			commonData := &model.CommonData{
				Quotation: &biz_runtime.Quotation{
					ShiftId: stringPtr("12345"),
				},
			}
			isNewVersionAnyCarFlow := true
			req := createTestRequest()

			card := NewBusShiftCard(product, commonData, isNewVersionAnyCarFlow)
			result := card.Render(ctx, req)

			So(result, ShouldNotBeNil)
			So(result.StationInfo.Start.Name, ShouldEqual, req.StartPoiName)
			So(result.StationInfo.End.Name, ShouldEqual, req.EndPoiName)
		})
	})
}

// 辅助函数
func int32Ptr(i int32) *int32 {
	return &i
}

func stringPtr(s string) *string {
	return &s
}

func int64Ptr(i int64) *int64 {
	return &i
}

func createTestProduct() *biz_runtime.ProductInfoFull {
	departureTime := int64(1625097600) // 2021-07-01 10:00:00

	startStationId := int32(1001)
	endStationId := int32(1002)

	startStation := &Prfs.StationInfo{
		StationId:        startStationId,
		StationName:      "起点站",
		StationLat:       "39.9",
		StationLng:       "116.4",
		City:             1,
		County:           101,
		StationSceneType: NormalStation,
	}

	endStation := &Prfs.StationInfo{
		StationId:        endStationId,
		StationName:      "终点站",
		StationLat:       "40.0",
		StationLng:       "116.5",
		City:             1,
		County:           102,
		StationSceneType: NormalStation,
	}

	// 创建班次信息
	stationDetails := []*CarpoolOpenApi.BusStationDetail{
		{
			StationID:     cast.ToInt64(startStationId),
			DepartureTime: int64Ptr(departureTime),
			Type:          1,
		},
		{
			StationID:     123456,
			DepartureTime: int64Ptr(departureTime + 1800), // +30分钟
			Type:          1,
		},
		{
			StationID:     cast.ToInt64(endStationId),
			DepartureTime: int64Ptr(departureTime + 3600), // +60分钟
			Type:          1,
		},
	}

	return &biz_runtime.ProductInfoFull{
		BaseReqData: &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				DepartureTime: departureTime,
				ClientType:    1,
				PageType:      2,
				Channel:       int64(0),
				GuideTraceId:  "test_guide_trace_id",
				AppVersion:    "6.2.4",
				AccessKeyID:   1,
			},
			CommonBizInfo: models.CommonBizInfo{
				StartStationId: cast.ToInt64(startStationId),
				EndStationId:   cast.ToInt64(endStationId),
				StartStation:   startStation,
				EndStation:     endStation,
				IsBestShift:    1,
				ShiftInfo: &CarpoolOpenApi.ShiftDetail{
					StationDetails: stationDetails,
				},
				BusShiftInventoryRobinData: map[string]*carpoolstation.BusServerShift{
					"12345": {
						ShiftTagId: "1",
					},
				},
			},
			PassengerInfo: models.PassengerInfo{
				Phone: "13800138000",
				PID:   1,
			},
		},
		Product: &models.Product{
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					BusMode: "舒适大巴",
					RouteDetailV2: &models.RouteDetail{
						RouteBasicInfo: &Prfs.RouteBasicInfo{
							RouteSceneType: StationToStationRoute,
						},
					},
				},
			},
		},
	}
}

func createTestRequest() *proto.IntercityEstimateDetailRequest {
	return &proto.IntercityEstimateDetailRequest{
		IsBestShift:      int32Ptr(1),
		StartPoiId:       "start_poi_id",
		StartPoiName:     "起点POI",
		StartPoiCity:     1,
		StartPoiCityName: "北京市",
		StartLat:         "39.91",
		StartLng:         "116.41",
		EndPoiId:         "end_poi_id",
		EndPoiName:       "终点POI",
		EndPoiCity:       1,
		EndPoiCityName:   "北京市",
		EndLat:           "40.01",
		EndLng:           "116.51",
	}
}
