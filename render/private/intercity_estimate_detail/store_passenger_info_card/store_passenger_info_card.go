package store_passenger_info_card

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

type storePassengerInfoCard struct {
	product       *biz_runtime.ProductInfoFull
	text          map[string]gjson.Result
	supportInfo   *intercity_estimate_detail.SupportInfo
	newPageSwitch bool
}

// NewStorePassengerInfoCard ...
func NewStorePassengerInfoCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, supportInfo *intercity_estimate_detail.SupportInfo, newPageSwitch bool) *storePassengerInfoCard {
	return &storePassengerInfoCard{
		product:       product,
		text:          text,
		supportInfo:   supportInfo,
		newPageSwitch: newPageSwitch,
	}
}

// Render ...
func (sic *storePassengerInfoCard) Render(ctx context.Context) *proto.StorePassengerInfoCard {
	return sic.getStorePassengerInfoCard(ctx)
}

// GetHeadCard ...
func (sic *storePassengerInfoCard) getStorePassengerInfoCard(ctx context.Context) *proto.StorePassengerInfoCard {
	if sic.text == nil {
		return nil
	}

	subtitle, childAgeLimit := sic.buildSubtitleAndAgeLimit(ctx)

	return &proto.StorePassengerInfoCard{
		Title:             sic.text["title"].String(),
		Subtitle:          subtitle,
		FormDetail:        sic.buildFormDetail(ctx),
		ChildAgeLimit:     childAgeLimit,
		ConfirmButton:     sic.buildConfirmButton(ctx),
		AlertInfo:         sic.buildAlertInfo(ctx),
		GetIdentityButton: sic.buildGetIdentityButton(ctx),
	}
}

func (sic *storePassengerInfoCard) buildGetIdentityButton(ctx context.Context) *proto.JumpButton {
	if !sic.product.BaseReqData.CommonBizInfo.CanGetIdentityFromGuardian {
		return nil
	}
	if _, ok := sic.text["get_identity_button"]; !ok {
		return nil
	}

	buttonMap := sic.text["get_identity_button"].Map()
	return &proto.JumpButton{
		Text:     buttonMap["text"].String(),
		Icon:     buttonMap["icon"].String(),
		GrayIcon: buttonMap["gray_icon"].String(),
	}
}

func (sic *storePassengerInfoCard) buildAlertInfo(ctx context.Context) *proto.AlertInfo {
	alertMap := sic.text["alert_card"].Map()

	return &proto.AlertInfo{
		Title:       alertMap["title"].String(),
		Description: alertMap["description"].String(),
		LeftButton:  alertMap["left_button"].String(),
		RightButton: alertMap["right_button"].String(),
	}
}

// buildSubtitleAndAgeLimit ...
func (sic *storePassengerInfoCard) buildSubtitleAndAgeLimit(ctx context.Context) (*string, *int32) {
	if sic.text == nil {
		return nil, nil
	}
	childAgeLimit := int32(sic.text["child_age_limit"].Int())

	if sic.newPageSwitch {
		return nil, &childAgeLimit
	}

	// 如果售儿童票
	if !sic.isSupportChildTicket() {
		subtitle := sic.text["subtitle"].String()
		return &subtitle, nil
	} else {
		return nil, &childAgeLimit
	}

}

// buildFormDetail ...
func (sic *storePassengerInfoCard) buildFormDetail(ctx context.Context) []*proto.FormDetail {
	if sic.text == nil {
		return nil
	}

	resFormDetail := make([]*proto.FormDetail, 0)

	formDetail := sic.text["form_detail"].Array()
	newPageSwitch := sic.newPageSwitch
	for _, formItem := range formDetail {
		if !formItem.Exists() {
			continue
		}

		formDetailConf := formItem.Map()
		// 选座组件 在新版端上需要根据班次是否有儿童票下发 班次不支持儿童票 则不下发
		if !newPageSwitch && ("ticket_type_select" == formDetailConf["key"].String() && !sic.isSupportChildTicket() && sic.isSupportNoType(ctx)) {
			continue
		}

		resFormDetail = append(resFormDetail, &proto.FormDetail{
			Key:          formDetailConf["key"].String(),
			Type:         int32(formDetailConf["type"].Int()),
			Title:        formDetailConf["title"].String(),
			DefaultValue: util.String2PtrString(formDetailConf["default_value"].String()),
			DefaultIndex: sic.buildDefaultIndex(ctx, formDetailConf),
			SelectItem:   sic.buildSelectItem(ctx, formDetailConf["select_item"].Array()),
		})
	}

	return resFormDetail
}

// buildSelectItem ...
func (sic *storePassengerInfoCard) buildSelectItem(ctx context.Context, selectItem []gjson.Result) []*proto.SelectItem {
	if len(selectItem) <= 0 {
		return nil
	}

	resSelectItem := make([]*proto.SelectItem, 0)

	for _, item := range selectItem {
		if !item.Exists() {
			continue
		}
		itemConf := item.Map()
		ticketType := int(itemConf["ticket_type"].Int())
		// 优待tab, && 不支持优待票，&& 不满足版本，则过滤
		if ticketType == seat_selection_consts.PreferentialPeople.ToInt() && (!sic.supportInfo.SupportCouponTicket || !sic.couponTicketOpenSwitch(ctx)) {
			continue
		}
		tempItem := &proto.SelectItem{
			DisplayName:  itemConf["display_name"].String(),
			TicketType:   util.Int32PtrNoZero(int32(itemConf["ticket_type"].Int())),
			IdentityType: util.Int32PtrNoZero(int32(itemConf["identity_type"].Int())),
		}
		// 只有优待票可以展示提示，成人和儿童要等放量..
		if ticketType == seat_selection_consts.PreferentialPeople.ToInt() || sic.newPageSwitch {
			tempItem.BubbleText = util.String2PtrString(itemConf["bubble_text"].String())
		}

		resSelectItem = append(resSelectItem, tempItem)
	}

	return resSelectItem
}

// buildConfirmButton ...
func (sic *storePassengerInfoCard) buildConfirmButton(ctx context.Context) *proto.Button {
	if sic.text == nil {
		return nil
	}

	return &proto.Button{
		Text:                sic.text["confirm_button"].Map()["text"].String(),
		FontColor:           util.StringPtr(sic.text["confirm_button"].Map()["font_color"].String()),
		LinkUrl:             util.StringPtr(sic.text["confirm_button"].Map()["link_url"].String()),
		BackgroundGradients: nil,
		ActionType:          util.Int32Ptr(int32(sic.text["confirm_button"].Map()["action_type"].Int())),
	}
}

// buildDefaultIndex ...
func (sic *storePassengerInfoCard) buildDefaultIndex(ctx context.Context, conf map[string]gjson.Result) *int32 {
	if !conf["default_index"].Exists() {
		return nil
	}

	defaultIndex := int32(conf["default_index"].Int())

	return &defaultIndex
}

// isSupportChildTicket ...
func (sic *storePassengerInfoCard) isSupportChildTicket() bool {
	if sic.product == nil || sic.product.BaseReqData == nil {
		return true
	}

	cbi := sic.product.GetCommonBizInfo()

	if cbi.GetChildTicketNew() != nil {
		return true
	}

	return false
}

// isSupportNoType 端是否支持不下发票型选择组件
func (sic *storePassengerInfoCard) isSupportNoType(ctx context.Context) bool {
	pidKey, params := sic.product.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "ticket_rules_support_rich_text", pidKey, params)
}

// couponTicketOpenSwitch 优待票开关
func (sic *storePassengerInfoCard) couponTicketOpenSwitch(ctx context.Context) bool {
	apolloParams := map[string]string{
		"pid":           cast.ToString(sic.product.BaseReqData.PassengerInfo.PID),
		"app_version":   cast.ToString(sic.product.BaseReqData.CommonInfo.AppVersion),
		"phone":         cast.ToString(sic.product.BaseReqData.PassengerInfo.Phone),
		"city":          cast.ToString(sic.product.BaseReqData.AreaInfo.Area),
		"access_key_id": cast.ToString(sic.product.BaseReqData.CommonInfo.AccessKeyID),
		"product_id":    cast.ToString(sic.product.GetBizInfo().RouteDetailV2.ProductId),
		"route_id":      cast.ToString(sic.product.GetBizInfo().RouteDetailV2.RouteId),
	}

	return apollo.FeatureToggle(ctx, "gs_coupon_ticket_switch", cast.ToString(sic.product.BaseReqData.PassengerInfo.PID), apolloParams)
}
