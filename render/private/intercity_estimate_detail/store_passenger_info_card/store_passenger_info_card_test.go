package store_passenger_info_card

import (
	"context"
	"testing"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
)

func createMockRealData(realJSON, key string) map[string]gjson.Result {
	text := map[string]gjson.Result{
		key: gjson.Result{
			Type: gjson.JSON,
			Raw:  realJSON,
		},
	}

	return text[key].Map()
}

// 创建模拟的realNameSeatInfoCard
func createMockRealNameSeatInfoCard(newPageSwitch bool) *storePassengerInfoCard {
	mockProduct := createProduct()
	mockText := createMockRealData(` {
    "title": "请录入乘客信息",
    "subtitle": "{该路线统一票价 不区分成人儿童}",
    "get_identity_button": {
      "text": "滴滴实名信息快捷导入",
      "icon": "https://s3-gz01.didistatic.com/packages-mait/img/nw9c9UAqP31733730549312.png",
      "gray_icon": "https://s3-gz01.didistatic.com/packages-mait/img/mRyTFHtQar1733731429262.png"
    },
    "form_detail": [
      {
        "key": "passenger_input",
        "type": 0,
        "title": "乘车人",
        "default_value": "请录入乘车人姓名"
      },
      {
        "key": "identity_type_select",
        "type": 1,
        "title": "证件类型",
        "default_index": 0,
        "select_item": [
          {
            "display_name": "身份证",
            "identity_type": 1
          },
          {
            "display_name": "港澳台居住证",
            "identity_type": 2
          },
          {
            "display_name": "港澳居民来往内地通行证",
            "identity_type": 3
          },
          {
            "display_name": "台湾居民来往大陆通行证",
            "identity_type": 4
          },
          {
            "display_name": "护照",
            "identity_type": 5
          },
          {
            "display_name": "外国人身份证",
            "identity_type": 6
          }
        ]
      },
      {
        "key": "identity_number_input",
        "type": 0,
        "title": "证件号码",
        "default_value": "请与证件类型保持一致"
      },
      {
        "key": "ticket_type_select",
        "type": 2,
        "title": "乘客类型",
        "default_index": 0,
        "select_item": [
          {
            "display_name": "成人",
            "ticket_type": 1,
            "bubble_text": "请如实录入乘客类型，具体运行政策已运行班次为主。"
          },
          {
            "display_name": "儿童",
            "ticket_type": 2,
            "bubble_text": "请如实录入乘客类型，具体运行政策已运行班次为主。"
          },
          {
            "display_name": "优待",
            "ticket_type": 7,
            "bubble_text": "乘车需出示相关证件"
          }
        ]
      }
    ],
    "confirm_button": {
      "text": "保存"
    },
    "child_age_limit": 14,
    "alert_card": {
      "title": "儿童监护人确认",
      "description": "您正在录入儿童个人信息，我们会按照《个人信息处理规则》《儿童个人信息处理规则》，与向您提供服务的第三方处理相关个人信息，请确认您是监护人或已取得监护人同意。",
      "left_button": "暂缓",
      "right_button": "确认"
    }
  }`, "store_passenger_info_card")
	supportInfo := &intercity_estimate_detail.SupportInfo{
		SupportChildrenTicket: true,
		SupportCouponTicket:   true,
	}
	rns := NewStorePassengerInfoCard(mockProduct, mockText, supportInfo, newPageSwitch)
	return rns
}

func createProduct() *biz_runtime.ProductInfoFull {
	//sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo = nil
	return &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					IntercityRule: &ticketPrice.RuleData{
						RebookChargeRule: &ticketPrice.RebookChargeRuleData{
							RebookRuleId: util.StringPtr("default7"),
							RuleName:     util.StringPtr("可改签改签后不可退"),
							RuleDetail: &ticketPrice.RebookChargeRuleDetail{
								MaxRebookTimes: 1,
								ChargeRate: []*ticketPrice.RebookChargeRate{
									{Times: 1,
										Rate: 100},
								},
							},
						},
						RefundChargeRule: &ticketPrice.RefundChargeRuleData{
							BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
								{
									LeftTimeInterval:  24,
									RightTimeInterval: 48,
									RefundChargeRate:  50,
								},
							},
							TimeUnit: util.String2PtrString("hour"),
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				IsBestShift: 1,
				IdentityPageInfo: &dirpcSdkBrick.IdentityPageInfoData{
					IdentityHistoryInfo: []*dirpcSdkBrick.IdentityItem{
						{
							Id:           1,
							Name:         "test",
							IdentityType: 1,
							IdentityNo:   "123456789012345678",
							Type:         1,
						},
					},
					RuleInfo: &dirpcSdkBrick.RuleData{},
				},
				RuleData: &dirpcSdkBrick.RuleData{},
			},
		},
	}
}

// TestGetStorePassengerInfoCard
func TestGetStorePassengerInfoCard(t *testing.T) {
	ctx := context.Background()

	t.Run("测试 01- 无text", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		sic.text = nil
		// 执行
		result := sic.getStorePassengerInfoCard(ctx)
		// 断言
		assert.Nil(t, result, "规则需要有可支持的内容")
	})

	t.Run("测试 02- 新场景不需要副标题", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		// 执行
		subTitle, childAge := sic.buildSubtitleAndAgeLimit(ctx)
		// 断言
		assert.Nil(t, subTitle, "无标题")
		assert.Equal(t, *childAge, int32(14))
	})

	t.Run("测试 03- 支持儿童票", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(false)
		// 执行
		b := mockey.Mock((*storePassengerInfoCard).isSupportChildTicket).Return(true).Build()
		defer b.UnPatch()
		subTitle, childAge := sic.buildSubtitleAndAgeLimit(ctx)
		// 断言
		assert.Nil(t, subTitle, "为空")
		assert.NotNil(t, childAge, "不为空")
		assert.Equal(t, *childAge, int32(14))
	})

	t.Run("测试 04- 不支持儿童票", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(false)
		sic.supportInfo.SupportChildrenTicket = false
		b := mockey.Mock((*storePassengerInfoCard).isSupportChildTicket).Return(false).Build()
		defer b.UnPatch()
		// 执行
		subTitle, childAge := sic.buildSubtitleAndAgeLimit(ctx)
		// 断言
		assert.NotNil(t, subTitle, "不为空")
		assert.Equal(t, *subTitle, "{该路线统一票价 不区分成人儿童}")
		assert.Nil(t, childAge, "为空")
	})

	t.Run("测试 04- 不支持儿童票", func(t *testing.T) {
		// 准备 - 文本为空的场景
		sic := createMockRealNameSeatInfoCard(true)
		b := mockey.Mock((*storePassengerInfoCard).couponTicketOpenSwitch).Return(false).Build()
		defer b.UnPatch()
		result := sic.buildFormDetail(ctx)
		assert.NotNil(t, result, "不为空")
	})
}
