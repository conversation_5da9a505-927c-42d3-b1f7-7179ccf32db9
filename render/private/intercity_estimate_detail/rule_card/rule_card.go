package rule_card

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type ruleCard struct {
	product       *biz_runtime.ProductInfoFull
	text          map[string]gjson.Result
	supportInfo   *intercity_estimate_detail.SupportInfo
	newPageSwitch bool
}

// NewRuleCard ...
func NewRuleCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, supportInfo *intercity_estimate_detail.SupportInfo, newPageSwitch bool) *ruleCard {
	return &ruleCard{
		product:       product,
		text:          text,
		supportInfo:   supportInfo,
		newPageSwitch: newPageSwitch,
	}
}

func (rc *ruleCard) Render(ctx context.Context) *proto.RuleCard {
	return rc.getRuleCard(ctx)
}

// GetHeadCard ...
func (rc *ruleCard) getRuleCard(ctx context.Context) *proto.RuleCard {
	if rc.text == nil {
		return nil
	}

	return &proto.RuleCard{
		Title:    util.StringPtr(rc.text["title"].String()),
		RuleList: rc.buildRuleList(ctx),
	}
}

func (rc *ruleCard) getCommonJudge(ctx context.Context) (bool, bool) {
	routeDetail := rc.product.GetBizInfo().RouteDetailV2
	verified := routeDetail.IsNeedVerified()
	richText := rc.isSupportRichText(ctx)
	return verified, richText
}

func (rc *ruleCard) buildNewRuleList(ctx context.Context) []string {
	var (
		conf map[string]gjson.Result
	)

	ruleListV3Config := dcmp.GetDcmpContent(ctx, "intercity_estimate-rule_list_v3", nil)
	if len(ruleListV3Config) == 0 {
		return nil
	}

	newConfig := gjson.Get(ruleListV3Config, intercity_estimate_detail.RuleCard).Map()

	verified, _ := rc.getCommonJudge(ctx)
	conf = newConfig["no_real_name_rule_list_v3"].Map()
	if verified {
		conf = newConfig["real_name_rule_list_v3"].Map()
	}
	key := rc.getRuleKey(ctx)
	if key == intercity_estimate_detail.HongKong {
		conf = newConfig["rule_list_hong_kong"].Map()
	}

	return rc.buildRuleListCommon(conf, key)
}

func (rc *ruleCard) getRuleKey(ctx context.Context) string {
	var key string
	if rc.getHongKongSwitch(ctx) {
		return intercity_estimate_detail.HongKong
	}
	if rc.supportInfo.SupportChildrenTicket {
		key += intercity_estimate_detail.SupportChildTicket
	} else {
		key += intercity_estimate_detail.NoSupportChildTicket
	}

	// 有无携童票 是否占座
	if rc.supportInfo.SupportCarryChildrenTicket {
		key += intercity_estimate_detail.SupportCarryChildTicket
		if rc.supportInfo.CarryChildrenOccupySeat {
			key += intercity_estimate_detail.WithOccupy
		} else {
			key += intercity_estimate_detail.WithoutOccupy
		}
	} else {
		key += intercity_estimate_detail.NoSupportCarryChildTicket
	}

	// 支持优待票
	if rc.supportInfo.SupportCouponTicket {
		key += intercity_estimate_detail.SupportCouponTicket
	}
	return key
}

// buildRuleList ...
func (rc *ruleCard) buildRuleList(ctx context.Context) []string {
	if rc.text == nil || rc.product == nil || rc.product.BaseReqData == nil || rc.product.GetBizInfo() == nil || rc.product.GetBizInfo().RouteDetailV2 == nil {
		return nil
	}

	if rc.newPageSwitch {
		return rc.buildNewRuleList(ctx)
	}

	var (
		conf map[string]gjson.Result
	)

	// ProductId  产品线id、 RouteId  路线id、RouteName 路线名称、RouteBasicInfo 路线基准信息、RouteExtendInfo 路线扩展信息
	verified, richText := rc.getCommonJudge(ctx)

	if verified && richText {
		conf = rc.text["real_name_rule_list_v2"].Map()
	} else if verified && !richText {
		conf = rc.text["real_name_rule_list"].Map()
	} else if !verified && richText {
		conf = rc.text["no_real_name_rule_list_v2"].Map()
	} else {
		conf = rc.text["no_real_name_rule_list"].Map()
	}
	key := rc.getRuleKey(ctx)
	if key == intercity_estimate_detail.HongKong {
		conf = rc.text["rule_list_hong_kong"].Map()
	}

	return rc.buildRuleListCommon(conf, key)
}

func (rc *ruleCard) buildRuleListCommon(conf map[string]gjson.Result, key string) []string {
	ruleList := conf[key].Array()
	resRuleList := make([]string, 0)

	for _, ruleItem := range ruleList {
		if ruleItem.Exists() {
			resRuleList = append(resRuleList, ruleItem.String())
		}
	}
	return resRuleList
}

func (rc *ruleCard) getHongKongSwitch(ctx context.Context) bool {
	// intercity_estimate-confirm_order
	apolloParams := map[string]string{
		"pid":           cast.ToString(rc.product.BaseReqData.PassengerInfo.PID),
		"phone":         cast.ToString(rc.product.BaseReqData.PassengerInfo.Phone),
		"city":          cast.ToString(rc.product.BaseReqData.AreaInfo.Area),
		"access_key_id": cast.ToString(rc.product.BaseReqData.CommonInfo.AccessKeyID),
		"product_id":    cast.ToString(rc.product.GetBizInfo().RouteDetailV2.ProductId),
		"route_id":      cast.ToString(rc.product.GetBizInfo().RouteDetailV2.RouteId),
	}
	return apollo.FeatureToggle(ctx, "gs_rule_card_hong_kong_switch", cast.ToString(rc.product.BaseReqData.PassengerInfo.PID), apolloParams)
}

// isSupportRichText ...
func (rc *ruleCard) isSupportRichText(ctx context.Context) bool {
	pidKey, params := rc.product.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "ticket_rules_support_rich_text", pidKey, params)
}
