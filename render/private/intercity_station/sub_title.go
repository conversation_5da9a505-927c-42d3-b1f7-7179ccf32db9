package intercity_station

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type SubTitleProv interface {
	GetScene() string
	GetSelectedBusServiceShiftId() string
}

func SubTitle(ctx context.Context, prov SubTitleProv) string {
	if prov.GetScene() == ticket_detail_consts.SceneRebook {
		if prov.GetSelectedBusServiceShiftId() != "" {
			return dcmp.GetJSONContentWithPath(ctx, "intercity_sku-sub_title_rebook", nil, "after")
		} else {
			return dcmp.GetJSONContentWithPath(ctx, "intercity_sku-sub_title_rebook", nil, "before")
		}
	}

	return ""
}
