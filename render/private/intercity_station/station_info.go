package intercity_station

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/distance_render"
	"github.com/tidwall/gjson"
)

type StationInfoProv interface {
	GetAllStationInfo() []*Prfs.StationInfo
	GetFromStationIndex() int
	GetDistance() []int64
	GetSelectInfo() *models.StationInventorySelectInfo
	GetPageType() int32
}

func StationInfo(ctx context.Context, prov StationInfoProv) *proto.StationInfo {
	config := dcmp.GetDcmpContent(ctx, "intercity_station-station_list", nil)
	res := proto.StationInfo{
		Detail: &proto.StationDetail{
			Title:       dcmp.GetDcmpContent(ctx, "intercity_station-station_info", nil),
			Icon:        dcmp.GetDcmpContent(ctx, "intercity_station-station_info_icon", nil),
			StationList: []*proto.BaseStation{},
		},
	}
	fromStationIndex := prov.GetFromStationIndex()
	if len(prov.GetAllStationInfo()) != 0 {
		for k, v := range prov.GetAllStationInfo() {
			stationType := int32(0)
			if k == fromStationIndex {
				stationType = 1
			} else if k == len(prov.GetAllStationInfo())-1 {
				stationType = 2
			}
			res.Detail.StationList = append(res.Detail.StationList, &proto.BaseStation{
				Name:        v.StationName,
				DisplayName: v.StationName,
				Lat:         v.StationLat,
				Lng:         v.StationLng,
				Id:          v.StationId,
				Type:        stationType,
			})

		}
	}
	if len(res.Detail.StationList) >= 2 && len(prov.GetDistance()) == 2 {
		distance, dcmpKey := distance_render.GetDistanceInfo(prov.GetDistance()[0])
		res.Detail.StationList[fromStationIndex].Distance = dcmp.TranslateTemplate(gjson.Get(config, "start."+dcmpKey).String(), map[string]string{
			"distance": distance,
		})

		distance, dcmpKey = distance_render.GetDistanceInfo(prov.GetDistance()[1])
		res.Detail.StationList[len(res.Detail.StationList)-1].Distance = dcmp.TranslateTemplate(gjson.Get(config, "end."+dcmpKey).String(), map[string]string{
			"distance": distance,
		})
		res.Start = &proto.Station{
			DisplayName: res.Detail.StationList[fromStationIndex].Name,
			Content: dcmp.TranslateTemplate(gjson.Get(config, "start.content").String(), map[string]string{
				"name": res.Detail.StationList[fromStationIndex].Name,
			}),
			Id:       res.Detail.StationList[fromStationIndex].Id,
			Lat:      res.Detail.StationList[fromStationIndex].Lat,
			Lng:      res.Detail.StationList[fromStationIndex].Lng,
			Distance: res.Detail.StationList[fromStationIndex].Distance,
		}
		res.End = &proto.Station{
			DisplayName: res.Detail.StationList[len(res.Detail.StationList)-1].Name,
			Content: dcmp.TranslateTemplate(gjson.Get(config, "end.content").String(), map[string]string{
				"name": res.Detail.StationList[len(res.Detail.StationList)-1].Name,
			}),
			Id:       res.Detail.StationList[len(res.Detail.StationList)-1].Id,
			Lat:      res.Detail.StationList[len(res.Detail.StationList)-1].Lat,
			Lng:      res.Detail.StationList[len(res.Detail.StationList)-1].Lng,
			Distance: res.Detail.StationList[len(res.Detail.StationList)-1].Distance,
		}

		res.Detail.StationList[0].Tag = gjson.Get(config, "start.tag").String()
		res.Detail.StationList[fromStationIndex].Icon = gjson.Get(config, "start.icon").String()
		res.Detail.StationList[fromStationIndex].Name = dcmp.TranslateTemplate(gjson.Get(config, "start.name").String(), map[string]string{
			"name": res.Detail.StationList[fromStationIndex].Name,
		})
		res.Detail.StationList[len(res.Detail.StationList)-1].Icon = gjson.Get(config, "end.icon").String()
		res.Detail.StationList[len(res.Detail.StationList)-1].Name = dcmp.TranslateTemplate(gjson.Get(config, "end.name").String(), map[string]string{
			"name": res.Detail.StationList[len(res.Detail.StationList)-1].Name,
		})

		if prov.GetPageType() == page_type.PageTypeIntercityStationEstimate {
			res.Start.Distance = ""
			res.End.Distance = ""
			res.Detail.StationList[fromStationIndex].Distance = ""
			res.Detail.StationList[len(res.Detail.StationList)-1].Distance = ""
		}
	}
	res.EstimateMsg = util.StringPtr(estimateMsg(ctx, prov))

	return &res
}

func estimateMsg(ctx context.Context, prov StationInfoProv) string {
	if prov == nil {
		return ""
	}

	stationInfos := prov.GetAllStationInfo()
	selectInfo := prov.GetSelectInfo()
	if selectInfo == nil {
		return ""
	}

	var (
		cntFlag    bool
		stationCnt int32
		etaSum     int32
	)

	for _, station := range stationInfos {
		if cntFlag {
			stationCnt += 1
			etaSum += station.Eta
		}

		if station.StationId == int32(selectInfo.FromStationId) {
			cntFlag = true
		}
		if station.StationId == int32(selectInfo.DestStationId) {
			cntFlag = false
		}
	}

	if etaSum == 0 {
		return ""
	}

	return dcmp.GetDcmpContent(ctx, "intercity_estimate-estimate_duration", map[string]string{
		"stationCnt": util.ToString(stationCnt - 1),
		"etaSum":     util.ToString(etaSum),
	})
}
