package intercity_anycar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"testing"
)

func TestGetPreferentialIcon(t *testing.T) {
	ctx := context.Background()

	convey.Convey("当特性开关开启且参数存在时", t, func() {
		testProv := &TestApolloProvider{}

		ExpAssignment := model.NewAssignment("intercity_carpool_subscribe_preferential_toggle", "", "control_group", map[string]interface{}{
			"fee_desc_icon": "test_icon",
		})
		patches := gomonkey.ApplyFunc(apollo.FeatureExp, func() (bool, *model.Assignment) {
			return true, ExpAssignment
		})
		defer patches.Reset()

		// 调用被测函数
		icon := getPreferentialIcon(ctx, testProv)

		// 验证结果
		convey.So(icon, convey.ShouldEqual, "test_icon")
	})
}

type TestApolloProvider struct {
}

func (a *TestApolloProvider) GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (key string, params map[string]string) {
	return "test_key", map[string]string{
		"combo_id":    "123",
		"route_group": "456",
		"product_id":  "789",
	}
}
