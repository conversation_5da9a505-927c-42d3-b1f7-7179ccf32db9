all: install

install: clean gen_output build
	mkdir -p output/bin
	mv mamba output/bin/

	cp /usr/local/bin/nuwa_goc output/bin/
	cp /usr/local/bin/goc_ticker output/bin/
	@if [ -e "mamba_cover" ]; then \
		mv mamba_cover output/bin/; \
	fi

gen_output:
	mkdir -p output/log
	mkdir -p output/conf
	cp -rf conf/* output/conf/
	cp -rf control.sh output/

clean:
	rm -rf output/bin
	rm -rf output/conf
	rm -rf output/control.sh

build:
	#go build -o mamba
ifeq ($(COV),yes)
	goc build -o mamba
else                # 该行必须顶行
	go build -o mamba
	nuwa_goc build -o mamba_cover --mode set
endif

run:
	./output/bin/mamba # 前台运行,方便退出

lint:
	golangci-lint run --fast ./...
