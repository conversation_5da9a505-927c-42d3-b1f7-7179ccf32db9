package errors

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type BizError interface {
	error
	Errno() BizErrno
}

type bizError struct {
	no BizErrno
}
type BizErrno = int

const (
	ErrnoSuccess BizErrno = 0

	ErrnoNotLogin BizErrno = 101

	ErrnoInvalidArgument          BizErrno = 400
	ErrnoCheckRiskFailed          BizErrno = 401
	ErrnoReqNotAllow              BizErrno = 402
	ErrnoLocsvrFailed             BizErrno = 403
	ErrnoGetTcRoutePrice          BizErrno = 439
	ErrNoGetShiftEmpty            BizErrno = 451
	ErrNoGetTicketRuleEmpty       BizErrno = 452
	ErrNoGetOrderInfoFailed       BizErrno = 453
	ErrNoGetRoueDetailFailed      BizErrno = 454
	ErrnoNotOpenPetsTravelService BizErrno = 455
	ErrnoPetsTravelRender         BizErrno = 456
	ErrnoOverseaNoOpenCity        BizErrno = 457      // 境外打车dds未开城
	ErrnoOverseaNoProductPrice    BizErrno = 20250108 // 境外打车未获取到品类价格

	WeakErr              BizErrno = 456
	ErrorSelectedBusCard BizErrno = 458

	ErrnoRiskBlock BizErrno = 520

	ErrnoSystemError BizErrno = 999
	ErrnoRpcFailed   BizErrno = 992
	ErrnoRender      BizErrno = 993

	ErrnoMetroNotOpen BizErrno = 998

	ErrnoStopoverPointsConflictsWithScenes BizErrno = 530012
	ErrnoNotSupportStopoverPoints          BizErrno = 530013 // 不支持途经点
	ErrnoAreaNotOpenService                BizErrno = 521600 //未开城
	EstimateDegradePriceEmpty                       = 596004
	EstimatePetTripMetaError                        = 530017

	// 景区接驳车特定errno
	ErrnoNotInSceneArea                     BizErrno = 530014 // 不在景区
	ErrnoInSceneAreaAndWithoutOperationTime BizErrno = 530015 // 非运营时段+在景区

	ErrnoInterception BizErrno = 530016

	// ErrnoStartFenceNotOpen ======================  错误码 【 997 】 顺风车场景专用错误码，其他场景勿用！！ ==========================
	ErrnoStartFenceNotOpen BizErrno = 997

	// ErrnoEmergencyServiceStopFenceNotOpen =======  错误码 【 990、993 994，995，996】 为保障车队专用错误码，其他场景勿用！！！=========

	ErrnoEmergencyServiceStopFenceNotOpen  BizErrno = 996
	ErrnoEmergencyServiceAllFenceNotOpen   BizErrno = 995
	ErrnoEmergencyServiceTimeNotOpen       BizErrno = 994
	ErrnoEmergencyServiceNotOpen           BizErrno = 993
	ErrnoEmergencyServiceStartFenceNotOpen BizErrno = 990

	// EmergencyService ============================ 错误码 【 990、993 994，995，996】 为保障车队专用错误码，其他场景勿用！！！===========

)

var (
	Success                               = BizErrorFromErrNo(ErrnoSuccess)
	ErrNotLogin                           = BizErrorFromErrNo(ErrnoNotLogin)
	ErrInvalidArgument                    = BizErrorFromErrNo(ErrnoInvalidArgument)
	ErrReqNotAllow                        = BizErrorFromErrNo(ErrnoReqNotAllow)
	ErrLocsvrFailed                       = BizErrorFromErrNo(ErrnoLocsvrFailed)
	ErrRiskBlock                          = BizErrorFromErrNo(ErrnoRiskBlock)
	ErrSystem                             = BizErrorFromErrNo(ErrnoSystemError)
	ErrMetroNotOpen                       = BizErrorFromErrNo(ErrnoMetroNotOpen)
	ErrStopoverPointsConflictsWithScenes  = BizErrorFromErrNo(ErrnoStopoverPointsConflictsWithScenes)
	ErrStartFenceNotOpen                  = BizErrorFromErrNo(ErrnoStartFenceNotOpen)
	ErrEmergenceyServiceStopFenceNotOpen  = BizErrorFromErrNo(ErrnoEmergencyServiceStopFenceNotOpen)
	ErrEmergenceyServiceAllFenceNotOpen   = BizErrorFromErrNo(ErrnoEmergencyServiceAllFenceNotOpen)
	ErrEmergencyServiceTimeNotOpen        = BizErrorFromErrNo(ErrnoEmergencyServiceTimeNotOpen)
	ErrEmergenceyServiceNotOpen           = BizErrorFromErrNo(ErrnoEmergencyServiceNotOpen)
	ErrEmergencyServiceStartFenceNotOpen  = BizErrorFromErrNo(ErrnoEmergencyServiceStartFenceNotOpen)
	ErrRpcFailed                          = BizErrorFromErrNo(ErrnoRpcFailed)
	ErrNotSupportStopoverPoints           = BizErrorFromErrNo(ErrnoNotSupportStopoverPoints)
	ErrCheckRiskFailed                    = BizErrorFromErrNo(ErrnoCheckRiskFailed)
	ErrAreaNotOpenService                 = BizErrorFromErrNo(ErrnoAreaNotOpenService)
	ErrEstimateDegradePriceEmpty          = BizErrorFromErrNo(EstimateDegradePriceEmpty)
	ErrRender                             = BizErrorFromErrNo(ErrnoRender)
	ErrGetRoutePriceFail                  = BizErrorFromErrNo(ErrnoGetTcRoutePrice)
	ErrGetShiftEmpty                      = BizErrorFromErrNo(ErrNoGetShiftEmpty)
	ErrGetTicketRuleEmpty                 = BizErrorFromErrNo(ErrNoGetTicketRuleEmpty)
	ErrGetRoueDetailFailed                = BizErrorFromErrNo(ErrNoGetRoueDetailFailed)
	ErrNotInSceneArea                     = BizErrorFromErrNo(ErrnoNotInSceneArea)
	ErrInSceneAreaAndWithoutOperationTime = BizErrorFromErrNo(ErrnoInSceneAreaAndWithoutOperationTime)
	ErrNotOpenPetsTravelService           = BizErrorFromErrNo(ErrnoNotOpenPetsTravelService)
	ErrPetsTravelRender                   = BizErrorFromErrNo(ErrnoPetsTravelRender)
	ErrOverseaNoOpenCity                  = BizErrorFromErrNo(ErrnoOverseaNoOpenCity)
	ErrOverseaNoProductPrice              = BizErrorFromErrNo(ErrnoOverseaNoProductPrice)
	ErrInterception                       = BizErrorFromErrNo(ErrnoInterception)
	ErrEstimatePetTripMeta                = BizErrorFromErrNo(EstimatePetTripMetaError)
)

func BizErrorFromErrNo(errNo BizErrno) BizError {
	return bizError{
		no: errNo,
	}
}

func (e bizError) toStrNo() string {
	return strconv.Itoa(e.no)
}

func (e bizError) String() string {
	return dcmp.GetJSONContentWithPath(context.Background(), "exception-err_msg", nil, e.toStrNo())
}

func (e bizError) Error() string {
	return e.String()
}
func (e bizError) Errno() BizErrno {
	return e.no
}

const StatusRateLimit = 596

func IsDegradeErrno(errno BizErrno) bool {
	return util.InArrayInt(errno, []int{
		EstimateDegradePriceEmpty,
	})
}
