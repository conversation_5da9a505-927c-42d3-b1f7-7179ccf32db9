pid = "var/run/nuwa.pid"

[module]
name = "mamba"

[rpc]
# http server地址
http_addr = ":9990"
# Recieve http request timeout(ms), including the body, default 5000
http_read_timeout = 5000
# Keep-alive timeout(ms), default 60000
http_idle_timeout = 60000

[redis]
disf_enable=true
disf="disf!codis-coproxy-gs-gsstore_coproxy"

addrs = []

auth = ""

# pool_size, max_con 根据qps 设置
pool_size = 500
max_con = 1000

conn_timeout = "20ms"
read_timeout = "20ms"
write_timeout = "20ms"

whitelist_password = "8b51TdLx6F1YlWNH"

[redis_estimate]
disf_enable = true
addrs = []
auth = ""


disf = "disf!codis-codis-dkproxy-biz-public-estimateapi2_kproxy"
whitelist_password = ""

max_con = 1000
pool_size = 500

conn_timeout = "20ms"
read_timeout = "20ms"
write_timeout = "20ms"

[carpool_core]
# pool_size, max_con 根据qps 设置
pool_size = 100
max_con = 1000
#unit ms
conn_timeout = 10
read_timeout = 10
write_timeout = 20
addrs = []
disf_enable = true
service_name = "disf!codis-codis-dkproxy-biz-carpoolcore_kproxy"
max_conn_lifetime = 60
isOnline = false
whitelist_password = ""

[carpool_other]
# pool_size, max_con 根据qps 设置
pool_size = 100
max_con = 1000
#unit ms
conn_timeout = 10
read_timeout = 10
write_timeout = 200
addrs = []
disf_enable = true
service_name = "disf!codis-codis-dkproxy-biz-carpoolother_kproxy"
max_conn_lifetime = 60
isOnline = false
whitelist_password = ""

# 顺风车缓存
#[redis_beatles]
#disf_enable = true
#addrs = []
##auth = ""
#
#disf = "disf!codis-fuproxy-beatles-gsbeatles_fuproxy"
#whitelist_password = "2145072c"
#
#max_con = 1000
#pool_size = 100
#
#conn_timeout = "10ms"
#read_timeout = "10ms"
#write_timeout = "20ms"

[redis_fusion]
# pool_size, max_con 根据qps 设置
pool_size = 100
max_con = 1000
#unit ms
conn_timeout = 10
read_timeout = 10
write_timeout = 20
disf_enable = true
service_name = "disf!codis-codis-fuproxy-biz-gscommonstore_fuproxy"
mode = 0 # 2 <-> block, 1 <-> timeout, 0 <-> nonblock
whitelist_password = "2145072c"

[mysql]
dsn = "root:123456@(127.0.0.1:3306)/test?timeout=50ms&readTimeout=100ms&writeTimeout=100ms&charset=utf8&parseTime=true&loc=Local"
disf_enable = true
# connect_timeout 在disf 模式使用
connect_timeout = 50
metrics_enable = true

# conn_max_lifetime <=0 connections are reused forever
conn_max_lifetime = 60
max_idle_conns = 20
max_open_conns = 20
disf_name = "DISF_NAME_OF_MYSQL"

[log]
file_prefix = "didi"
dir = "./log"
level = "debug"
auto_clear = true
clear_hours = 168
separate = true
async_write = true
async_model = "chan"
async_wait_strategy = "discard"
## 打开脱敏开关，设置慢脱敏阈值
enable_masking = true
slow_masking_latency = 2000  # 微秒

[public]
file_prefix = "public"
dir = "./log"
auto_clear = true
clear_hours = 168
separate = true

[disf]
disf_path = "./conf/disf.yaml"

[locsvr]
token = "6738B0470C487ABFEF4CA1E27CBE3CF4"

[ddmq]
env="test"
poolSize=10
proxyTimeout=50
clientTimeout=100
clientRetry = 2

[intercity_mutli_estimate]
allow_domain = "*"

[estimate_v4]
render_timeout = "800ms"

[baichuan]
disf = "disf!biz-gs-baichuan-confucius"

[dai_jia]
app_key = "b14e7f88664a4f6398ed80f8d70824f3"