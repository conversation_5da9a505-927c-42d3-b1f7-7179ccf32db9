package param_handler

import (
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	passenger_common "git.xiaojukeji.com/gulfstream/passenger-common/dto"
)

type EstimateRequest struct {
	ReqFromParams interface{} // 端参数
	CommonData    CommonData  // 公共数据
}

type CommonData struct {
	// 用户信息
	Passenger *passport.UserInfo
	// 地理信息
	AreaInfo *models.AreaInfo
	// 订单信息  出发时间 实时单/预约单

	// 地理位置信息 起终点信息

	// 公参
	pageType     int32  // 业务赋值的page_type
	fromType     int16  // 业务赋值的from_type
	guideTraceId string // 导流位trace id

	// 这是存放公共数据 让大家获取公共数据不重复写代码 不是各个接口的业务数据

	PassengerDetail      []*passenger_common.PassengerDetail
	PreBusServiceShiftId string
	PreDepartureTime     string
	PreEstimateId        string
	IsCommonCashierOrder bool
	RouteType            string
	Dchn                 string
}

func (e *EstimateRequest) GetPassengerInfo() *passport.UserInfo {
	return e.CommonData.Passenger
}

func (e *EstimateRequest) GeOriginReq() interface{} {
	return e.ReqFromParams
}

func (e *EstimateRequest) GetPageType() int32 {
	return e.CommonData.pageType
}

func (e *EstimateRequest) SetPageType(pageType int32) {
	e.CommonData.pageType = pageType
}

func (e *EstimateRequest) GetFromType() int16 {
	return e.CommonData.fromType
}

func (e *EstimateRequest) SetFromType(fromType int16) {
	e.CommonData.fromType = fromType
}

func (e *EstimateRequest) GetGuideTraceId() string {
	return e.CommonData.guideTraceId
}

func (e *EstimateRequest) SetGuideTraceId(guideTraceId string) {
	e.CommonData.guideTraceId = guideTraceId
}

func (e *EstimateRequest) GetDchn() string {
	return e.CommonData.Dchn
}

func (e *EstimateRequest) SetDchn(dchn *string) {
	if dchn != nil {
		e.CommonData.Dchn = *dchn
	}
}
