package param_handler

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"strconv"
)

func GetLocSvrInfo(ctx context.Context, serviceReq *EstimateRequest) BizError.BizError {
	if serviceReq.CommonData.AreaInfo == nil {
		return BizError.ErrSystem
	}
	a := serviceReq.CommonData.AreaInfo

	var coords = make([]map[string]float64, 0)

	coords = append(coords, map[string]float64{"lat": a.FromLat, "lng": a.FromLng})
	coords = append(coords, map[string]float64{"lat": a.ToLat, "lng": a.ToLng})

	cityInfos, err := locsvr.MultiAreaInfoByCoords(ctx, coords, a.MapType)
	if err != nil || len(cityInfos) != 2 || cityInfos[0] == nil || cityInfos[1] == nil {
		return BizError.ErrLocsvrFailed
	}

	if cityInfos[0] != nil {
		a.Area = cityInfos[0].Cityid
		a.City = a.Area
		if cityInfos[0].DistrictCode != nil {
			a.District = *cityInfos[0].DistrictCode
		}
		a.FromCounty = cityInfos[0].Countyid
		if cityInfos[0].CanonicalCountryCode != nil {
			a.TripCountry = *cityInfos[0].CanonicalCountryCode
		}
		a.FromCityName = cityInfos[0].CityDesc
		a.FromCountyName = cityInfos[0].CountyDesc
	}

	if cityInfos[1] != nil {
		a.ToArea = cityInfos[1].Cityid
		a.ToCounty = cityInfos[1].Countyid
		a.ToCityName = cityInfos[1].CityDesc
		a.ToCountyName = cityInfos[1].CountyDesc
	}
	a.AbstractDistrict = a.District + "," + strconv.Itoa(int(a.FromCounty))

	return nil
}
