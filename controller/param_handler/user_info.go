package param_handler

import (
	"context"
	"strconv"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func GetUserInfo(ctx context.Context, token string, accessKeyId int32) RequestWrapper {

	return func(ctx context.Context, serviceReq *EstimateRequest) BizError.BizError {

		if token == "" {
			param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyId)))
			toggle, err := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
			if err != nil || toggle.IsAllow() {
				serviceReq.CommonData.Passenger = &passport.UserInfo{}
				return BizError.ErrNotLogin
			}
		} else {
			passenger, err := passport.GetUserInfo(ctx, token, "")
			if err == passport.ErrOffline {
				return BizError.ErrNotLogin
			}
			if err != nil {
				return BizError.ErrSystem
			}
			serviceReq.CommonData.Passenger = passenger
			return nil
		}

		serviceReq.CommonData.Passenger = &passport.UserInfo{}
		return nil
	}
}
