package param_handler

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

func CheckRisk(ctx context.Context, getParams func(req2 interface{}, user *passport.UserInfo) *risk.HackNewPriceDataParams) RequestWrapper {
	return func(ctx context.Context, serviceReq *EstimateRequest) BizError.BizError {
		params := getParams(serviceReq.ReqFromParams, serviceReq.CommonData.Passenger)
		if params == nil {
			return BizError.ErrCheckRiskFailed
		}

		//if err := risk.HackNewPrice(ctx, &risk.HackNewPricePayload{
		//	Data: &risk.HackNewPriceDataBody{
		//		Header: &risk.HackNewPriceDataHeader{},
		//		Params: params,
		//	},
		//}); errors.Is(err, risk.ErrRiskHack) {
		//	return BizError.ErrCheckRiskFailed
		//}
		if err := risk.HackNewPrice(ctx, &risk.HackNewPriceDataBody{
			Header: &risk.HackNewPriceDataHeader{},
			Params: params,
		}); errors.Is(err, risk.ErrRiskHack) {
			return BizError.ErrCheckRiskFailed
		}
		return nil
	}
}

// CheckCompositeRisk 组合出行风控
func CheckCompositeRisk(ctx context.Context, getParams func(req2 interface{}) *risk.HackCompositeDataParams) RequestWrapper {
	return func(ctx context.Context, serviceReq *EstimateRequest) BizError.BizError {
		params := getParams(serviceReq.ReqFromParams)
		if params == nil {
			return BizError.ErrCheckRiskFailed
		}

		if err := risk.HackCompositeNewPrice(ctx, &risk.HackCompositeDataBody{
			Header: &risk.HackCompositeDataHeader{
				Referer:       proto.GetHeader(ctx, "Referer"),
				DidiHeaderRID: proto.GetHeader(ctx, "didi-header-rid"),
				XRealIP:       proto.GetHeader(ctx, "X-Real-IP"),
				UserAgent:     proto.GetHeader(ctx, "User-Agent"),
				Wsgdid:        proto.GetHeader(ctx, "wsgdid"),
				Wsgsig:        proto.GetHeader(ctx, "wsgsig"),
			},
			Params: params,
		}); errors.Is(err, risk.ErrRiskHack) {
			// 只有风控拦截时才失败
			return BizError.ErrCheckRiskFailed
		}
		return nil
	}
}

func CheckEstimatePriceRisk(ctx context.Context, getParams func(req2 interface{}, user *passport.UserInfo) *risk.HacSfcEstimatePriceDataParams) RequestWrapper {
	return func(ctx context.Context, serviceReq *EstimateRequest) BizError.BizError {
		params := getParams(serviceReq.ReqFromParams, serviceReq.CommonData.Passenger)
		if params == nil {
			return BizError.ErrCheckRiskFailed
		}

		if _, err := risk.HackSfcEstimatePrice(ctx, &risk.HacSfcEstimatePriceDataBody{
			Header: &risk.HacSfcEstimatePriceDataHeader{
				Referer:       proto.GetHeader(ctx, "Referer"),
				DidiHeaderRID: proto.GetHeader(ctx, "didi-header-rid"),
				XRealIP:       proto.GetHeader(ctx, "X-Real-IP"),
				UserAgent:     proto.GetHeader(ctx, "User-Agent"),
				Wsgdid:        proto.GetHeader(ctx, "wsgdid"),
			},
			Params: params,
		}); errors.Is(err, risk.ErrRiskHack) {
			return BizError.ErrCheckRiskFailed
		}
		return nil
	}

}
