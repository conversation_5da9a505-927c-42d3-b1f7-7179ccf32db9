package base_controller

import (
	"context"
	"runtime/debug"

	"git.xiaojukeji.com/gobiz/ctxutil"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	trace "git.xiaojukeji.com/lego/context-go"
)

type BaseController struct {
	WritePublicLogEnv
}

type WritePublicLogEnv struct {
	isShieldPressureTraffic bool // 写public是否屏蔽压测流量
	isAsync                 bool // 是否异步
}

type ResponseCommon interface {
	SetErrno(int32)
	SetErrmsg(string)
	SetTraceId(string)
}

// RecoverPanic 处理panic
func (b *BaseController) RecoverPanic(ctx context.Context, tag string, resp ResponseCommon) {
	if r := recover(); r != nil {
		log.Trace.Errorf(ctx, tag, "PANIC=%v, stack: %s \n %v", r, string(debug.Stack()))
		resp.SetErrno(int32(NewErrors.ErrSystem.Errno()))
		resp.SetErrmsg(NewErrors.ErrSystem.Error())
	}
}

// HandleResp 处理resp
func (b *BaseController) HandleResp(ctx context.Context, resp ResponseCommon, err NewErrors.BizError) {
	var bizErr NewErrors.BizError

	if resp == nil {
		return
	}

	if err != nil {
		switch err.Errno() {
		case NewErrors.ErrnoNotLogin, NewErrors.ErrnoInvalidArgument:
			bizErr = err
			break
		default:
			bizErr = NewErrors.BizErrorFromErrNo(NewErrors.ErrnoSystemError)
		}
	} else {
		bizErr = NewErrors.BizErrorFromErrNo(NewErrors.ErrnoSuccess)
	}

	resp.SetTraceId(trace.GetTrace(ctx).GetTraceId())
	resp.SetErrno(int32(bizErr.Errno()))
	resp.SetErrmsg(bizErr.Error())
}

// WritePublicLog 写public日志
func (b *BaseController) WritePublicLog(ctx context.Context, process public_log.WritePublicFunc) {
	if process == nil {
		return
	}

	// 屏蔽压测流量
	if b.isShieldPressureTraffic {
		hintCode, err := ctxutil.GetHintCode(ctx)
		if err == nil && hintCode == commonConsts.PressureHintCode {
			return
		}
	}

	// 是否异步
	if b.isAsync {
		util.Go(ctx, func() {
			process()
		})
	} else {
		process()
	}
}

func (b *BaseController) SetIsShieldPressureTraffic(isShieldPressureTraffic bool) *BaseController {
	b.isShieldPressureTraffic = isShieldPressureTraffic

	return b
}

func (b *BaseController) SetIsAsync(isAsync bool) *BaseController {
	b.isAsync = isAsync

	return b
}
