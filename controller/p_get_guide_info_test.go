package controller_test

import (
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info"
	context2 "git.xiaojukeji.com/lego/context-go"
	"github.com/bytedance/mockey"
)

// Mock 请求对象
func mockPGetGuideInfoReq(validateErr error) *proto.PGetGuideInfoReq {
	req := &proto.PGetGuideInfoReq{OrderId: "xxxxx"}
	mockey.Mock(req.Validate).Return(validateErr).Build()
	return req
}

// Mock guide_info.Handler - Declaration only, setup moved inside PatchConvey
func mockGuideInfoHandler(data *proto.GuideInfoData, errno int) {
	mockey.Mock(guide_info.Handler).Return(data, errno).Build()
}

// Mock context trace using the local mock implementation
func mockTrace(traceId string) {
	// Ensure context2.Trace interface (or whatever GetTrace returns)
	// is satisfied by mockTracer (at least the GetTraceId method).
	tracer := &context2.DefaultTrace{TraceId: traceId}
	mockey.Mock(context2.GetTrace).Return(tracer).Build()
}

//func TestPGetGuideInfoController_PGetGuideInfo(t *testing.T) {
//	ctrl := &controller.PGetGuideInfoController{}
//	ctx := context.Background()
//	dummyTraceId := "test-trace-id"
//
//	t.Run("参数验证失败", func(t *testing.T) {
//		mockey.PatchConvey("当请求参数验证失败时", t, func() {
//			// Setup Mocks inside PatchConvey
//			mockTrace(dummyTraceId)
//			req := mockPGetGuideInfoReq(errors.New("validation error")) // Mock Validate 返回错误
//			// Mock Handler even for validation failure due to bug in original code
//			mockGuideInfoHandler(nil, consts.NoErr)
//
//			// Execute
//			resp := ctrl.PGetGuideInfo(ctx, req)
//
//			// Assert
//			assert.Equal(t, int32(consts.ErrnoParams), resp.Errno, "错误码应为参数错误")
//			assert.NotEmpty(t, resp.Errmsg, "错误信息不应为空")
//			assert.Equal(t, dummyTraceId, resp.TraceId, "Trace ID 应匹配")
//		})
//	})
//
//	t.Run("guide_info.Handler 处理失败", func(t *testing.T) {
//		mockey.PatchConvey("当 guide_info.Handler 返回错误时", t, func() {
//			// Setup Mocks inside PatchConvey
//			mockTrace(dummyTraceId)
//			req := mockPGetGuideInfoReq(nil) // Mock Validate 返回 nil
//			// Use the integer value 21 for system error as consts.ErrnoSystem is undefined
//			mockGuideInfoHandler(nil, 21) // Mock Handler 返回系统错误 (code 21)
//
//			// Execute
//			resp := ctrl.PGetGuideInfo(ctx, req)
//
//			// Assert
//			assert.Equal(t, int32(21), resp.Errno, "错误码应为系统错误 (code 21)")
//			assert.NotEmpty(t, resp.Errmsg, "错误信息不应为空")
//			assert.Equal(t, dummyTraceId, resp.TraceId, "Trace ID 应匹配")
//			assert.Nil(t, resp.Data, "处理失败时 Data 应为 nil")
//		})
//	})
//
//	t.Run("处理成功", func(t *testing.T) {
//		mockey.PatchConvey("当 guide_info.Handler 处理成功时", t, func() {
//			// Setup Mocks inside PatchConvey
//			mockTrace(dummyTraceId)
//			req := mockPGetGuideInfoReq(nil) // Mock Validate 返回 nil
//			mockData := &proto.GuideInfoData{ /* 初始化模拟数据 */ }
//			mockGuideInfoHandler(mockData, consts.NoErr) // Mock Handler 返回成功
//
//			// Execute
//			resp := ctrl.PGetGuideInfo(ctx, req)
//
//			// Assert
//			assert.Equal(t, int32(consts.NoErr), resp.Errno, "错误码应为 NoErr")
//			assert.Empty(t, resp.Errmsg, "成功时错误信息应为空")
//			assert.Equal(t, mockData, resp.Data, "返回的数据应与 Handler 返回的一致")
//			assert.Equal(t, dummyTraceId, resp.TraceId, "Trace ID 应匹配")
//		})
//	})
//
//	t.Run("PGetGuideInfoPost 调用 PGetGuideInfo", func(t *testing.T) {
//		mockey.PatchConvey("当调用 PGetGuideInfoPost 时应直接调用 PGetGuideInfo 并返回相同结果", t, func() {
//			// Setup Mocks inside PatchConvey
//			mockTrace(dummyTraceId)
//			req := mockPGetGuideInfoReq(nil)
//			mockData := &proto.GuideInfoData{PredictManageCardV2: &proto.PredictManageCard{}}
//			mockGuideInfoHandler(mockData, consts.NoErr)
//
//			// Execute Post method
//			resp := ctrl.PGetGuideInfoPost(ctx, req)
//
//			// Assert (same as success case for PGetGuideInfo)
//			assert.Equal(t, int32(consts.NoErr), resp.Errno, "错误码应为 NoErr (Post)")
//			assert.Empty(t, resp.Errmsg, "成功时错误信息应为空 (Post)")
//			assert.Equal(t, mockData, resp.Data, "返回的数据应与 Handler 返回的一致 (Post)")
//			assert.Equal(t, dummyTraceId, resp.TraceId, "Trace ID 应匹配 (Post)")
//		})
//	})
//}
