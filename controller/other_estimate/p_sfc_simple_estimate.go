package other_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/sfc_simple_estimate"
)

// SFCEstimateSimpleController 顺风车预估
type SFCEstimateSimpleController struct {
}

// PSFCSimpleEstimate 顺风车预估
func (c *SFCEstimateSimpleController) PSFCSimpleEstimate(ctx context.Context, req *proto.SFCSimpleEstimateReq) (resp *proto.SFCSimpleEstimateResponse) {
	//var resp *proto.SFCModifyEstimateResponse
	resp = new(proto.SFCSimpleEstimateResponse)
	var (
		err      error
		logic    *sfc_simple_estimate.Logic
		bizError = BizError.Success
	)
	resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

	defer func() {
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	//获取一些品类，出发时间，最大座位数等参数信息
	if logic, err = sfc_simple_estimate.NewLogic(ctx, req, resp.TraceId); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return resp
	}
	//获取一些价格信息
	if resp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return resp
	}

	return resp
}
