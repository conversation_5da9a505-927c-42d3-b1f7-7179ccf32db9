package other_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/cancel_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type PCancelEstimateController struct {
}

func (c *PCancelEstimateController) PCancelEstimate(ctx context.Context, req *proto.CancelEstimateReq) (rsp *proto.CancelEstimateRsp) {
	var (
		rspData *proto.CancelEstimateData
		errno   int
	)
	rsp = &proto.CancelEstimateRsp{}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	rspData, errno = c.do(ctx, req)
	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}
	return rsp
}

func (c *PCancelEstimateController) do(ctx context.Context, req *proto.CancelEstimateReq) (rspData *proto.CancelEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	productsGenerator, errno := cancel_estimate.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products = cancel_estimate.GenProducts(ctx, productsGenerator)
	if len(products) == 0 {
		return nil, consts.ErrnoNoProductOpen
	}

	log.Trace.Debugf(ctx, trace.DLTagUndefined, "products: %s", util.JustJsonEncode(products))

	rspData, errno = cancel_estimate.BuildResponse(ctx, products)
	if consts.NoErr != errno {
		return nil, errno
	}

	return rspData, consts.NoErr
}
