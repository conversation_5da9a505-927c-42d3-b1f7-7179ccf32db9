package other_estimate

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/order_estimate_without_render"
)

type EstimateOrderWithoutRenderController struct {
}

func (p *EstimateOrderWithoutRenderController) EstimateOrderWithoutRender(ctx context.Context, req *proto.EstimateOrderWithoutRenderReq) *proto.EstimateOrderWithoutRenderRsp {
	var (
		err  error
		resp = &proto.EstimateOrderWithoutRenderRsp{}
	)
	defer func() {
		if err != nil {
			resp.Errno = -1
			resp.Errmsg = err.Error()
			var bizErr BizError.BizError
			if errors.As(err, &bizErr) {
				resp.Errno = int32(bizErr.Errno())
			}
		}
	}()
	service := order_estimate_without_render.NewRuntime(req)
	if err = service.Init(ctx); err != nil {
		return resp
	}
	data, err := service.Do(ctx)
	if err != nil {
		return resp
	}
	resp.Data = &proto.EstimateOrderWithoutRenderData{
		EstimateData: data,
	}
	return resp
}
