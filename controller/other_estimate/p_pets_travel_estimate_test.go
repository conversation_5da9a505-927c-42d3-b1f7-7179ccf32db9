package other_estimate

import (
	"context"
	"encoding/json"
	Dirpc_SDK_PetTripApi "git.xiaojukeji.com/dirpc/dirpc-go-http-PetTripApi"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pettripapi"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pets_travel_estimate"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestPPetsTravelEstimate(t *testing.T) {

	var basePatches []*mockey.Mocker
	defer func() {
		for _, p := range basePatches {
			p.UnPatch()
		}
	}()
	basePatches = append(basePatches, mockey.Mock((*ddlog.PubLog).Public).Return().Build())
	basePatches = append(basePatches, mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build())

	Convey("测试 PPetsTravelEstimate", t, func() {
		ctx := context.Background()
		userInfo := &passport.UserInfo{
			PID: 1111,
			UID: 1111,
		}
		patches0 := mockey.Mock(passport.GetUserInfo).Return(userInfo, nil).Build()

		petsTravelEstimateController := &PetsTravelEstimateController{}
		req := &proto.PetsTravelEstimateReq{
			Token:           "tzlvKlnukxvVZ4fqBi5LCvnIh5ck_lAApGJK9t-3ABsUzjtOBTEMRuG9nNq6-u3EyVy39OyBx_BoggSiGrF3lP7o07lYomg33YSxnHJjBeWSZKxGTbVxzD4j-3EPY3XKZ4Za5EhjJcXDI8YTBcYz1dKP3ma0-8hMN16pYZzUxc_X7_fLSenPeNtU7xva1DuFJA8pNPbSB4VjfO7-PwAA__8%3D",
			AccessKeyId:     2,
			AppVersion:      "7.0.14",
			Channel:         73397,
			ClientType:      1,
			Lang:            "zh-CN",
			MapType:         "soso",
			TerminalId:      1,
			PlatformType:    2,
			UserType:        1,
			OrderType:       0,
			FromArea:        1,
			FromLat:         40.0677,
			FromLng:         116.27486,
			FromPoiId:       "R1152921774621207733",
			FromPoiType:     "pgcstation",
			FromAddress:     "滴滴新橙海大厦-北门",
			FromName:        "滴滴新橙海大厦-北门",
			ToArea:          1,
			ToLat:           40.062902,
			ToLng:           116.271798,
			ToPoiId:         "2000000000000076778",
			ToPoiType:       "ud_Dfm_top",
			ToAddress:       "海淀区-土井东路图景嘉园东南门",
			ToName:          "图景嘉园-东南门",
			Lat:             40.*********,
			Lng:             116.*********,
			ChooseFSearchid: "359cebe567f7b8be00005cb6b2acf214",
			ChooseTSearchid: "",
			ScreenPixels:    new(string),
			ScreenScale:     new(float64),
			Xpsid:           new(string),
			XpsidRoot:       new(string),
		}
		patches := mockey.Mock(pets_travel_estimate.BuildService).Return(nil, BizError.ErrInterception).Build()
		jsonData := `{
		"pet_list": [
			{
				"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/2nscomgIK1TFLsm9YjaM_.png",
				"pet_nick_name": "狗狗",
				"pet_type": 2,
				"pet_type_name": "狗狗",
				"weight_category_list": [
					{
						"weight_category": 1,
						"weight_category_desc": "小型",
						"weight_min": 0,
						"weight_max": 5,
						"weight_category_subtitle": "5公斤以内"
					},
					{
						"weight_category": 2,
						"weight_category_desc": "中小型",
						"weight_min": 6,
						"weight_max": 15,
						"weight_category_subtitle": "6-15公斤"
					},
					{
						"weight_category": 3,
						"weight_category_desc": "中大型",
						"weight_min": 16,
						"weight_max": 30,
						"weight_category_subtitle": "16-30公斤"
					},
					{
						"weight_category": 4,
						"weight_category_desc": "大型",
						"weight_min": 31,
						"weight_max": 0,
						"weight_category_subtitle": "31公斤以上"
					}
				],
				"show_type": 1,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/fe-agility-wyc/kMHFquxWrosgCtFKcR2HV.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/uvq2rw7gQCase-7rsDRHn.gif"
				},
				"sub_pet_list": null
			},
			{
				"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/8rX1Ljl4lxeUni16IC-d0.png",
				"pet_nick_name": "猫咪",
				"pet_type": 1,
				"pet_type_name": "猫咪",
				"weight_category_list": [
					{
						"weight_category": 1,
						"weight_category_desc": "小型",
						"weight_min": 0,
						"weight_max": 5,
						"weight_category_subtitle": "5公斤以内"
					},
					{
						"weight_category": 2,
						"weight_category_desc": "中小型",
						"weight_min": 6,
						"weight_max": 15,
						"weight_category_subtitle": "6-15公斤"
					},
					{
						"weight_category": 3,
						"weight_category_desc": "中大型",
						"weight_min": 16,
						"weight_max": 30,
						"weight_category_subtitle": "16-30公斤"
					},
					{
						"weight_category": 4,
						"weight_category_desc": "大型",
						"weight_min": 31,
						"weight_max": 0,
						"weight_category_subtitle": "31公斤以上"
					}
				],
				"show_type": 1,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/fe-agility-wyc/e6DW_hHxsSkErCBnPIHKP.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/C0WeE_M7qwyCCPLV6DMbB.gif"
				},
				"sub_pet_list": null
			},
			{
				"pet_avatar": "",
				"pet_nick_name": "",
				"pet_type": 0,
				"pet_type_name": "其他",
				"weight_category_list": null,
				"show_type": 0,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/didi-global-passenger/362jdrOGfX9EVYYJqK_0Y.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/d6g0I5dMzm6MPMJzAGKBO.gif"
				},
				"sub_pet_list": [
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/E6qjXJMrdLq1lvYV7gAnt.png",
						"pet_nick_name": "鸟类",
						"pet_type": 3,
						"pet_type_name": "鸟类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					},
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/xym8siDK7t2npr3m4XqLj.png",
						"pet_nick_name": "鱼类",
						"pet_type": 4,
						"pet_type_name": "鱼类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					},
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/AjTYzNtvahq-jNDGP5fHw.png",
						"pet_nick_name": "龟类",
						"pet_type": 5,
						"pet_type_name": "龟类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					}
				]
			}
		],
		"protocol": {
			"protocol_txt": "请先阅读{《乘客携宠出行规范》}",
			"protocol_url": "https://v.didi.cn/lvDoObm"
		},
		"add_pet": {
			"button_txt": "同意规范并确认",
			"title": "选择宠物信息",
			"pet_type_title": "宠物类型  {*}",
			"pet_weight_title": "宠物体重  {*}",
			"pet_type_toast": "请选择宠物类型",
			"pet_weight_toast": "请选择宠物体重"
		}
	}`

		var petMetaConfig *Dirpc_SDK_PetTripApi.PetMetaConfig
		json.Unmarshal([]byte(jsonData), &petMetaConfig)
		//GetPetApolloConfigResp := &Dirpc_SDK_PetTripApi.GetPetApolloConfigResp{
		//	Errno:   0,
		//	Errmsg:  "",
		//	TraceId: "xxxxxxxx",
		//	Data:    petMetaConfig,
		//}
		//patches1 := mockey.Mock(pettripapi.GetPetList).Return(petMetaConfig, nil).Build()
		patches1 := mockey.Mock(pettripapi.GetPetMetaData).Return(petMetaConfig, nil).Build()
		patches2 := mockey.Mock(apollo.FeatureToggle).Return(true).Build()
		petsTravelEstimateController.PPetsTravelEstimate(ctx, req)
		patches0.UnPatch()
		patches1.UnPatch()
		patches2.UnPatch()
		patches.UnPatch()
	})

	Convey("测试 PPetsTravelEstimate unmarshal error", t, func() {
		ctx := context.Background()
		userInfo := &passport.UserInfo{
			PID: 1111,
			UID: 1111,
		}
		patches0 := mockey.Mock(passport.GetUserInfo).Return(userInfo, nil).Build()

		petsTravelEstimateController := &PetsTravelEstimateController{}
		req := &proto.PetsTravelEstimateReq{
			Token:           "tzlvKlnukxvVZ4fqBi5LCvnIh5ck_lAApGJK9t-3ABsUzjtOBTEMRuG9nNq6-u3EyVy39OyBx_BoggSiGrF3lP7o07lYomg33YSxnHJjBeWSZKxGTbVxzD4j-3EPY3XKZ4Za5EhjJcXDI8YTBcYz1dKP3ma0-8hMN16pYZzUxc_X7_fLSenPeNtU7xva1DuFJA8pNPbSB4VjfO7-PwAA__8%3D",
			AccessKeyId:     2,
			AppVersion:      "7.0.14",
			Channel:         73397,
			ClientType:      1,
			Lang:            "zh-CN",
			MapType:         "soso",
			TerminalId:      1,
			PlatformType:    2,
			UserType:        1,
			OrderType:       0,
			FromArea:        1,
			FromLat:         40.0677,
			FromLng:         116.27486,
			FromPoiId:       "R1152921774621207733",
			FromPoiType:     "pgcstation",
			FromAddress:     "滴滴新橙海大厦-北门",
			FromName:        "滴滴新橙海大厦-北门",
			ToArea:          1,
			ToLat:           40.062902,
			ToLng:           116.271798,
			ToPoiId:         "2000000000000076778",
			ToPoiType:       "ud_Dfm_top",
			ToAddress:       "海淀区-土井东路图景嘉园东南门",
			ToName:          "图景嘉园-东南门",
			Lat:             40.*********,
			Lng:             116.*********,
			ChooseFSearchid: "359cebe567f7b8be00005cb6b2acf214",
			ChooseTSearchid: "",
			ScreenPixels:    new(string),
			ScreenScale:     new(float64),
			Xpsid:           new(string),
			XpsidRoot:       new(string),
		}
		patches := mockey.Mock(pets_travel_estimate.BuildService).Return(nil, BizError.ErrInterception).Build()
		jsonData := `{
		"pet_list": [
			{
				"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/2nscomgIK1TFLsm9YjaM_.png",
				"pet_nick_name": "狗狗",
				"pet_type": 2,
				"pet_type_name": "狗狗",
				"weight_category_list": [
					{
						"weight_category": 1,
						"weight_category_desc": "小型",
						"weight_min": 0,
						"weight_max": 5,
						"weight_category_subtitle": "5公斤以内"
					},
					{
						"weight_category": 2,
						"weight_category_desc": "中小型",
						"weight_min": 6,
						"weight_max": 15,
						"weight_category_subtitle": "6-15公斤"
					},
					{
						"weight_category": 3,
						"weight_category_desc": "中大型",
						"weight_min": 16,
						"weight_max": 30,
						"weight_category_subtitle": "16-30公斤"
					},
					{
						"weight_category": 4,
						"weight_category_desc": "大型",
						"weight_min": 31,
						"weight_max": 0,
						"weight_category_subtitle": "31公斤以上"
					}
				],
				"show_type": 1,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/fe-agility-wyc/kMHFquxWrosgCtFKcR2HV.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/uvq2rw7gQCase-7rsDRHn.gif"
				},
				"sub_pet_list": null
			},
			{
				"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/8rX1Ljl4lxeUni16IC-d0.png",
				"pet_nick_name": "猫咪",
				"pet_type": 1,
				"pet_type_name": "猫咪",
				"weight_category_list": [
					{
						"weight_category": 1,
						"weight_category_desc": "小型",
						"weight_min": 0,
						"weight_max": 5,
						"weight_category_subtitle": "5公斤以内"
					},
					{
						"weight_category": 2,
						"weight_category_desc": "中小型",
						"weight_min": 6,
						"weight_max": 15,
						"weight_category_subtitle": "6-15公斤"
					},
					{
						"weight_category": 3,
						"weight_category_desc": "中大型",
						"weight_min": 16,
						"weight_max": 30,
						"weight_category_subtitle": "16-30公斤"
					},
					{
						"weight_category": 4,
						"weight_category_desc": "大型",
						"weight_min": 31,
						"weight_max": 0,
						"weight_category_subtitle": "31公斤以上"
					}
				],
				"show_type": 1,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/fe-agility-wyc/e6DW_hHxsSkErCBnPIHKP.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/C0WeE_M7qwyCCPLV6DMbB.gif"
				},
				"sub_pet_list": null
			},
			{
				"pet_avatar": "",
				"pet_nick_name": "",
				"pet_type": 0,
				"pet_type_name": "其他",
				"weight_category_list": null,
				"show_type": 0,
				"pet_avatar_style": {
					"grey_logo": "https://ut-static.udache.com/webx/didi-global-passenger/362jdrOGfX9EVYYJqK_0Y.png",
					"highlight_logo": "https://ut-static.udache.com/webx/didi-global-passenger/d6g0I5dMzm6MPMJzAGKBO.gif"
				},
				"sub_pet_list": [
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/E6qjXJMrdLq1lvYV7gAnt.png",
						"pet_nick_name": "鸟类",
						"pet_type": 3,
						"pet_type_name": "鸟类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					},
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/xym8siDK7t2npr3m4XqLj.png",
						"pet_nick_name": "鱼类",
						"pet_type": 4,
						"pet_type_name": "鱼类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					},
					{
						"pet_avatar": "https://ut-static.udache.com/webx/fe-agility-wyc/AjTYzNtvahq-jNDGP5fHw.png",
						"pet_nick_name": "龟类",
						"pet_type": 5,
						"pet_type_name": "龟类",
						"weight_category_list": [
							{
								"weight_category": 1,
								"weight_category_desc": "小型",
								"weight_min": 0,
								"weight_max": 5,
								"weight_category_subtitle": "5公斤以内"
							},
							{
								"weight_category": 2,
								"weight_category_desc": "中小型",
								"weight_min": 6,
								"weight_max": 15,
								"weight_category_subtitle": "6-15公斤"
							},
							{
								"weight_category": 3,
								"weight_category_desc": "中大型",
								"weight_min": 16,
								"weight_max": 30,
								"weight_category_subtitle": "16-30公斤"
							},
							{
								"weight_category": 4,
								"weight_category_desc": "大型",
								"weight_min": 31,
								"weight_max": 0,
								"weight_category_subtitle": "31公斤以上"
							}
						],
						"show_type": 2,
						"pet_avatar_style": {
							"grey_logo": "",
							"highlight_logo": ""
						},
						"sub_pet_list": null
					}
				]
			}
		],
		"protocol": {
			"protocol_txt": "请先阅读{《乘客携宠出行规范》}",
			"protocol_url": "https://v.didi.cn/lvDoObm"
		},
		"add_pet": {
			"button_txt": "同意规范并确认",
			"title": "选择宠物信息",
			"pet_type_title": "宠物类型  {*}",
			"pet_weight_title": "宠物体重  {*}",
			"pet_type_toast": "请选择宠物类型",
			"pet_weight_toast": "请选择宠物体重",,,,
		}
	}`

		var petMetaConfig *Dirpc_SDK_PetTripApi.PetMetaConfig
		json.Unmarshal([]byte(jsonData), &petMetaConfig)
		//GetPetApolloConfigResp := &Dirpc_SDK_PetTripApi.GetPetApolloConfigResp{
		//	Errno:   0,
		//	Errmsg:  "",
		//	TraceId: "xxxxxxxx",
		//	Data:    petMetaConfig,
		//}
		//patches1 := mockey.Mock(pettripapi.GetPetList).Return(petMetaConfig, nil).Build()
		patches1 := mockey.Mock(pettripapi.GetPetMetaData).Return(petMetaConfig, nil).Build()
		patches2 := mockey.Mock(apollo.FeatureToggle).Return(true).Build()
		petsTravelEstimateController.PPetsTravelEstimate(ctx, req)
		patches0.UnPatch()
		patches1.UnPatch()
		patches2.UnPatch()
		patches.UnPatch()
	})
}
