package other_estimate

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_multi_station_price"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type PbdStationBusMultiStationPriceController struct{}

func (p *PbdStationBusMultiStationPriceController) PPbdStationBusMultiStationPrice(ctx context.Context, req *proto.PbdStationBusMultiStationPriceReq) (resp *proto.PbdStationBusMultiStationPriceRsp) {
	resp = new(proto.PbdStationBusMultiStationPriceRsp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.StationBusMultiStationPriceData)
		}

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, b := NewMultiStationPriceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := pbd_station_bus_multi_station_price.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		if errors.Is(bizError, BizError.ErrGetShiftEmpty) { //班次为空属于正常情况，不返回错误码，不记录wf日志
			bizError = BizError.Success
			log.Trace.Infof(ctx, consts.TagErrGenProducts, "err=%v", err)
			return
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewMultiStationPriceRequest(ctx context.Context, httpReq *proto.PbdStationBusMultiStationPriceReq) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypeUndefined)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckPbdStationBusMultiParams(ctx, serviceReq), // 参数校验
	})
	return serviceReq, do

}

func CheckPbdStationBusMultiParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.PbdStationBusMultiStationPriceReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.EndCity == 0 || req.StartCity == 0 || req.DayTime == 0 {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
