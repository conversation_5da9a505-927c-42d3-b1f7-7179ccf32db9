package other_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/service_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type ServiceEstimateController struct {
}

// PServiceEstimate 保障车队 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=922504082
func (c *ServiceEstimateController) PServiceEstimate(ctx context.Context, req *proto.ServiceEstimateRequest) (resp *proto.ServiceMultiEstimateResponse) {
	resp = new(proto.ServiceMultiEstimateResponse)
	var (
		err      error
		logic    *service_estimate.Logic
		bizError = BizError.Success
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	if logic, err = service_estimate.NewLogic(ctx, req); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	// 保障车队识别准入
	if err = logic.CheckAccess(ctx); err != nil {
		bizError = BizError.ErrEmergenceyServiceNotOpen
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	if resp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	return resp
}
