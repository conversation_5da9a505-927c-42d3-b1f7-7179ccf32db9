package other_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/invited_estimate"
)

// PInvitedEstimateController 分享地址受邀预估
type PInvitedEstimateController struct {
}

func (c *PInvitedEstimateController) PInvitedEstimate(ctx context.Context, req *proto.InvitedEstimateReq) *proto.InvitedEstimateRsp {
	var (
		rspData *proto.InvitedEstimateData
		errno   int
		rsp     = &proto.InvitedEstimateRsp{}
	)

	rspData, errno = invited_estimate.InvitedEstimate(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
