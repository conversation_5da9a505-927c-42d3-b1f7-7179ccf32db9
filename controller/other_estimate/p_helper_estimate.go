package other_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	helperEstimate "git.xiaojukeji.com/gulfstream/mamba/logic/helper_estimate"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

type PHelperEstimateController struct {
}

// PHelperEstimate 助手预估
func (c *PHelperEstimateController) PHelperEstimate(ctx context.Context,
	req *proto.MultiEstimatePriceRequest) (res *proto.HelperEstimateRes) {
	var (
		resData *proto.HelperEstimateResData
		errno   int
	)
	res = &proto.HelperEstimateRes{}
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, LegoContext.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			res.Errno = consts.ErrnoPanic
			res.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	resData, errno = c.do(ctx, req)
	if consts.NoErr != errno {
		res.Errno = int32(errno)
		res.Errmsg = consts.GetErrMessage(errno)
	} else {
		res.Errno = consts.NoErr
		res.Data = resData
	}
	return res
}

func (c *PHelperEstimateController) do(ctx context.Context, req *proto.MultiEstimatePriceRequest) (
	resData *proto.HelperEstimateResData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	defer func() {
		helperEstimate.AddPublicLog(ctx, products)
	}()

	// 1 初始化productsGenerator：初始化用户信息、地理信息，注册product过滤器
	productsGenerator, errno := helperEstimate.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	// 2 生成预估数据：根据产品开城获取预估价并执行product过滤器
	products = helperEstimate.GenProducts(ctx, productsGenerator)
	if len(products) == 0 {
		return nil, consts.ErrnoNoProductOpen
	}
	log.Trace.Debugf(ctx, LegoContext.DLTagUndefined, "products: %s", util.JustJsonEncode(products))

	// 3 渲染数据
	resData, errno = helperEstimate.BuildResponse(ctx, products)
	if consts.NoErr != errno {
		return nil, errno
	}

	return resData, consts.NoErr
}
