package other_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/logic/business_estimate/mq"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type PBusinessEstimateController struct {
}

func (c *PBusinessEstimateController) PBusinessEstimate(ctx context.Context, req *proto.B2BEstimateReq) (rsp *proto.B2BEstimateRsp) {
	var (
		errno    int
		respData *proto.B2BEstimateData
	)
	rsp = &proto.B2BEstimateRsp{}
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	respData, errno = c.do(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = respData
	}
	return rsp
}

func (c *PBusinessEstimateController) do(ctx context.Context, req *proto.B2BEstimateReq) (rsp *proto.B2BEstimateData, err int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	defer func() {
		business_estimate.AddPublicLog(ctx, products)
	}()

	errno := business_estimate.CheckParams(ctx, req)
	if errno != consts.NoErr {
		return nil, errno
	}

	productsGenerator, errno := business_estimate.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}
	products, errno = business_estimate.GenProducts(ctx, productsGenerator)
	if errno != consts.NoErr {
		return nil, errno
	}

	res, errno := business_estimate.BuildResponse(ctx, productsGenerator.BaseReqData, products)
	if errno != consts.NoErr {
		return nil, errno
	}

	// 写mq
	for _, prod := range products {
		go mq.SyncSendMQMessageNocheck(ctx, prod)
	}

	return res, consts.NoErr
}
