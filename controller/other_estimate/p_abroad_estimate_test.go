package other_estimate

import (
	"context"
	"errors"
	"testing"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/abroad_estimate"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func mockLogAndTraceID() {
	mockey.Mock((*ddlog.DiLogHandle).Errorf).Return().Build()
	mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
	mockey.Mock(util.GetTraceIDFromCtxWithoutCheck).Return("traceid").Build()
}

func TestOverseaEstimateController_POverseaEstimate(t *testing.T) {
	ctx := context.Background()
	ctrl := &OverseaEstimateController{}

	t.Run("正常场景-返回正常数据", func(t *testing.T) {
		mockLogAndTraceID()
		mockService := &abroad_estimate.BizLogic{}
		mockey.Mock(abroad_estimate.BuildService).Return(mockService, nil).Build()
		mockey.Mock((*abroad_estimate.BizLogic).DoBizLogicList).Return(&proto.OverseaEstimateData{EstimateTraceId: "traceid"}, nil).Build()
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(&param_handler.EstimateRequest{}, nil).Build()
		mockey.Mock((*OverseaEstimateController).writePublicLog).Return().Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoSuccess), resp.Errno)
		assert.NotNil(t, resp.Data)
		assert.Equal(t, "traceid", resp.Data.EstimateTraceId)
	})

	t.Run("参数校验失败-直接返回", func(t *testing.T) {
		mockLogAndTraceID()
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(nil, BizError.ErrInvalidArgument).Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno)
		assert.NotNil(t, resp.Data)
	})

	t.Run("BuildService失败-系统异常", func(t *testing.T) {
		mockLogAndTraceID()
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(&param_handler.EstimateRequest{}, nil).Build()
		mockey.Mock(abroad_estimate.BuildService).Return(nil, errors.New("fail")).Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoSystemError), resp.Errno)
		assert.NotNil(t, resp.Data)
	})

	t.Run("DoBizLogicList失败-品类开城失败", func(t *testing.T) {
		mockLogAndTraceID()
		mockService := &abroad_estimate.BizLogic{}
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(&param_handler.EstimateRequest{}, nil).Build()
		mockey.Mock(abroad_estimate.BuildService).Return(mockService, nil).Build()
		mockey.Mock((*abroad_estimate.BizLogic).DoBizLogicList).Return(nil, consts.ErrorGetFromDDSFail).Build()
		mockey.Mock((*OverseaEstimateController).writePublicLog).Return().Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoOverseaNoOpenCity), resp.Errno)
		assert.NotNil(t, resp.Data)
	})

	t.Run("DoBizLogicList失败-tc报价失败，NoCarInfo兜底", func(t *testing.T) {
		mockLogAndTraceID()
		mockService := &abroad_estimate.BizLogic{}
		mockNoCarInfo := &proto.OverseaNoCarInfo{Img: "img", BottomText: "no car"}
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(&param_handler.EstimateRequest{}, nil).Build()
		mockey.Mock(abroad_estimate.BuildService).Return(mockService, nil).Build()
		mockey.Mock((*abroad_estimate.BizLogic).DoBizLogicList).Return(nil, consts.ErrorGetFromPriceApiFail).Build()
		mockey.Mock((*abroad_estimate.BizLogic).BuildNoCarInfo).Return(mockNoCarInfo).Build()
		mockey.Mock((*OverseaEstimateController).writePublicLog).Return().Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoOverseaNoProductPrice), resp.Errno)
		assert.NotNil(t, resp.NoCarInfo)
		assert.Equal(t, "img", resp.NoCarInfo.Img)
		assert.Equal(t, "no car", resp.NoCarInfo.BottomText)
	})

	t.Run("DoBizLogicList失败-其它业务错误", func(t *testing.T) {
		mockLogAndTraceID()
		mockService := &abroad_estimate.BizLogic{}
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).Return(&param_handler.EstimateRequest{}, nil).Build()
		mockey.Mock(abroad_estimate.BuildService).Return(mockService, nil).Build()
		mockey.Mock((*abroad_estimate.BizLogic).DoBizLogicList).Return(nil, BizError.ErrInvalidArgument).Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoInvalidArgument), resp.Errno)
		assert.NotNil(t, resp.Data)
	})

	t.Run("panic场景-兜底系统异常", func(t *testing.T) {
		mockLogAndTraceID()
		mockey.Mock((*OverseaEstimateController).NewServiceRequest).To(func(_ *OverseaEstimateController, _ context.Context, _ *proto.OverseaEstimateReq) (*param_handler.EstimateRequest, BizError.BizError) {
			panic("mock panic")
		}).Build()
		defer mockey.UnPatchAll()

		resp := ctrl.POverseaEstimate(ctx, &proto.OverseaEstimateReq{})
		assert.Equal(t, int32(BizError.ErrnoSystemError), resp.Errno)
		assert.NotNil(t, resp.Data)
	})
}
