package other_estimate

import (
	"context"
	"encoding/json"
	"runtime/debug"
	"strconv"
	"time"

	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	passenger_common "git.xiaojukeji.com/gulfstream/passenger-common/dto"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type IntercityEstimatePriceController struct {
}

// PIntercityEstimate 城际独立页预估
func (c IntercityEstimatePriceController) IntercityEstimatePrice(ctx context.Context, req *proto.CarpoolEstimateRequest) (resp *proto.IntercityEstimateResp) {
	resp = new(proto.IntercityEstimateResp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.IntercityEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
			//resp.Data.ErrorUrl = util.StringPtr(errInfo.ErrUrl)
		}
		if bizError.Errno() == BizError.ErrnoSystemError {
			log.Trace.Warnf(ctx, "errno=999", "access_key_id=%v||app_version=%v||from_lat=%v||from_lng=%v||to_lat=%v||to_lng=%v||routeId=%v||agent_type=%v",
				req.AccessKeyId, req.AppVersion, req.FromLat, req.FromLng, req.ToLat, req.ToLng, req.RouteId, GetBusServiceShiftId(req))
		}
	}()
	estimateRequest, b := NewServiceRequest(ctx, req)
	if b != nil {
		return
	}

	// service
	service, err := carpool_estimate.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if resp.Data != nil && resp.Data.ErrorUrl != nil {
		bizError = BizError.ErrAreaNotOpenService
		return
	}
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func GetBusServiceShiftId(req *proto.CarpoolEstimateRequest) string {
	if req != nil && req.BusServiceShiftId != nil {
		return *(req.BusServiceShiftId)
	}
	return ""
}

// intercityEstimateRequestToRiskParam 预估防刷
func intercityEstimateRequestToRiskParam(req *proto.IntercityEstimateRequest, user *passport.UserInfo) *risk.HackNewPriceDataParams {
	nilStringToEmpty := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	departureTime := strconv.FormatInt(time.Now().Unix(), 10)
	if req.DepartureRange != "" && req.OrderType == 1 {
		departureRng := intercity_common.DecodeDepartureRangeV3(req.DepartureRange)
		if departureRng != nil {
			departureTime = strconv.FormatInt(departureRng.To.Unix(), 10)
		}
	}

	return &risk.HackNewPriceDataParams{
		ToPoiType:          req.ToPoiType,
		AppVersion:         req.AppVersion,
		Channel:            util.IntParseWithDefault(req.Channel, 0),
		PassportIsTestCell: user.IsTestCell,
		ClientType:         int64(req.ClientType),
		A3Token:            nilStringToEmpty(req.A3Token),
		UserType:           int64(req.UserType),
		PlatformType:       int64(req.PlatformType),
		FromPoiType:        req.FromPoiType,
		FromAddress:        req.FromAddress,
		DepartureTime:      departureTime,
		OrderType:          int64(req.OrderType),
		TerminalID:         "",
		Lat:                req.Lat,
		Lng:                req.Lng,
		Suuid:              "",
		PassportUID:        int64(user.UID),
		ToAddress:          req.ToAddress,
		PassportDuid:       int64(user.DUid),
		PassportCell:       user.Cell,
		ActivityID:         int64(req.AccessKeyId),
		FromLng:            req.FromLng,
		OriginID:           util.IntParseWithDefault(user.OriginId, 0),
		PassportAppid:      int64(user.AppID),
		FromName:           req.FromName,
		PaymentsType:       int64(req.PaymentsType),
		PassportCreateTime: user.CreateTime,
		//PassportCountryID: user,
		DepartureRange:             req.DepartureRange,
		ToLng:                      req.ToLng,
		PassportRole:               strconv.Itoa(int(user.Role)),
		Lang:                       req.Lang,
		PassportOriginID:           user.OriginId,
		FromPoiID:                  req.FromPoiId,
		FromLat:                    req.FromLat,
		ToLat:                      req.ToLat,
		PassportCountryCallingCode: user.CountryCallingCode,
		ToName:                     req.ToName,
		URL:                        "/gulfstream/mamba/v1/pIntercityEstimate",
		Token:                      req.Token,
		Maptype:                    req.MapType,
	}
}

// 返回true表示通过校验
func intercityReqCheck(req *proto.IntercityEstimateRequest) (status bool) {
	if req.StartStationId == 0 || req.EndStationId == 0 || req.RouteId == 0 {
		return false
	}

	return true
}

func CheckParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {
		_, ok := serviceReq.ReqFromParams.(*proto.CarpoolEstimateRequest)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}

		return nil
	}
}

func NewServiceRequest(ctx context.Context, httpReq *proto.CarpoolEstimateRequest) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}

	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckParams(ctx, serviceReq),                                       // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
		InitParamFromDos(ctx, serviceReq),                                  // 从dos获取信息（改签场景）
		SetPageType(ctx, serviceReq),                                       // 必须在上一步之后，因为改签场景routeId依赖dos todo check xyq
		param_handler.CheckRisk(ctx, intercityRiskParam),                   // 预估安全
	})

	// 兜底安卓bug的临时逻辑，通过调prfs获取站点经纬度mock到req上 来解,之后删掉
	RewritePositionData(ctx, httpReq, serviceReq)

	return serviceReq, do

}

func RewritePositionData(ctx context.Context, req *proto.CarpoolEstimateRequest, serviceReq *param_handler.EstimateRequest) {
	if req == nil || serviceReq == nil || serviceReq.CommonData.Passenger == nil {
		return
	}
	if serviceReq.GetPageType() != page_type.PageTypeIntercityStationEstimate {
		return
	}
	param := ApolloModel.NewUser("").
		With("access_key_id", strconv.Itoa(int(req.AccessKeyId))).
		With("app_version", req.AppVersion).
		With("phone", serviceReq.CommonData.Passenger.Phone)
	toggle, err := ApolloSDK.FeatureToggle("gs_ab_open_rewrite_position", param)
	if err != nil || !toggle.IsAllow() {
		return
	}
	if req.RouteId == 0 {
		return
	}
	//获取prfs站点列表
	rep := prfs.GetRouteDetail(ctx, &Prfs.RouteDetailReq{
		RouteId:     strconv.Itoa(int(req.RouteId)),
		CallerScene: rpc_process.PRFS_CALLER_SCENE,
		CarpoolType: int32(consts.CarPoolTypeInterCityStation),
	})
	if rep == nil || rep.Errno != 0 || len(rep.Data) == 0 {
		return
	}
	if _, v := rep.Data[int32(req.RouteId)]; !v {
		return
	}
	res := rep.Data[int32(req.RouteId)]
	if res.RouteBasicInfo == nil || len(res.RouteBasicInfo.StationList) == 0 {
		return
	}
	stationList := res.RouteBasicInfo.StationList
	startStationID := req.StartStationId
	endStationId := req.EndStationId
	var startStation *Prfs.StationInfo
	var endStation *Prfs.StationInfo
	if startStationID == 0 || endStationId == 0 {
		return
	}
	for _, v := range stationList {
		if v == nil {
			continue
		}
		if int64(v.StationId) == startStationID {
			startStation = v
		}
		if int64(v.StationId) == endStationId {
			endStation = v
		}
	}
	if startStation == nil || endStation == nil {
		return
	}

	req.FromLat, _ = strconv.ParseFloat(startStation.StationLat, 64)
	req.FromLng, _ = strconv.ParseFloat(startStation.StationLng, 64)
	req.ToLat, _ = strconv.ParseFloat(endStation.StationLat, 64)
	req.ToLng, _ = strconv.ParseFloat(endStation.StationLng, 64)

}

func intercityRiskParam(req2 interface{}, user *passport.UserInfo) *risk.HackNewPriceDataParams {
	req, ok := req2.(*proto.CarpoolEstimateRequest)
	if !ok {
		return nil
	}

	nilStringToEmpty := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	departureTime := strconv.FormatInt(time.Now().Unix(), 10)
	if req.DepartureRange != "" && req.OrderType == 1 {
		departureRng := intercity_common.DecodeDepartureRangeV3(req.DepartureRange)
		if departureRng != nil {
			departureTime = strconv.FormatInt(departureRng.To.Unix(), 10)
		}
	}

	return &risk.HackNewPriceDataParams{
		ToPoiType:                  req.ToPoiType,
		AppVersion:                 req.AppVersion,
		Channel:                    util.IntParseWithDefault(req.Channel, 0),
		PassportIsTestCell:         user.IsTestCell,
		ClientType:                 int64(req.ClientType),
		A3Token:                    nilStringToEmpty(req.A3Token),
		UserType:                   int64(req.UserType),
		PlatformType:               int64(req.PlatformType),
		FromPoiType:                req.FromPoiType,
		FromAddress:                req.FromAddress,
		DepartureTime:              departureTime,
		OrderType:                  int64(req.OrderType),
		TerminalID:                 "",
		Lat:                        req.Lat,
		Lng:                        req.Lng,
		Suuid:                      "",
		PassportUID:                int64(user.UID),
		ToAddress:                  req.ToAddress,
		PassportDuid:               int64(user.DUid),
		PassportCell:               user.Cell,
		ActivityID:                 int64(req.AccessKeyId),
		FromLng:                    req.FromLng,
		OriginID:                   util.IntParseWithDefault(user.OriginId, 0),
		PassportAppid:              int64(user.AppID),
		FromName:                   req.FromName,
		PaymentsType:               int64(req.PaymentsType),
		PassportCreateTime:         user.CreateTime,
		DepartureRange:             req.DepartureRange,
		ToLng:                      req.ToLng,
		PassportRole:               strconv.Itoa(int(user.Role)),
		Lang:                       req.Lang,
		PassportOriginID:           user.OriginId,
		FromPoiID:                  req.FromPoiId,
		FromLat:                    req.FromLat,
		ToLat:                      req.ToLat,
		PassportCountryCallingCode: user.CountryCallingCode,
		ToName:                     req.ToName,
		URL:                        "/gulfstream/mamba/v1/pIntercityEstimate",
		Token:                      req.Token,
		Maptype:                    req.MapType,
	}
}

func InitParamFromDos(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {
		httpReq, _ := serviceReq.ReqFromParams.(*proto.CarpoolEstimateRequest)
		if httpReq == nil {
			return BizError.ErrInvalidArgument
		}

		if httpReq.GetBizSceneType() == ticket_detail_consts.SceneRebook {
			if httpReq.GetPreOrderId() == "" || len(httpReq.GetPreRebookTicketIds()) == 0 {
				return BizError.ErrInvalidArgument
			}

			orderID, district, err := util.DecodeOrderID(httpReq.GetPreOrderId())
			if err != nil {
				log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
				return BizError.ErrInvalidArgument
			}

			// 请求 dos
			orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
			if err != nil {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
				return BizError.ErrRpcFailed
			}

			//httpReq.UserType = ??

			httpReq.Lat = util.ToFloat64(orderInfo.CurrentLat)
			httpReq.Lng = util.ToFloat64(orderInfo.CurrentLng)

			httpReq.FromLat = util.ToFloat64(orderInfo.StartingLat)
			httpReq.FromLng = util.ToFloat64(orderInfo.StartingLng)
			httpReq.FromPoiId = orderInfo.StartingPoiId
			//httpReq.FromPoiType = ???
			httpReq.FromAddress = orderInfo.FromAddress
			httpReq.FromName = orderInfo.FromName

			httpReq.ToLat = util.ToFloat64(orderInfo.DestLat)
			httpReq.ToLng = util.ToFloat64(orderInfo.DestLng)
			httpReq.ToPoiId = orderInfo.DestPoiId
			//httpReq.ToPoiType = ??
			httpReq.ToAddress = orderInfo.ToAddress
			httpReq.ToName = orderInfo.ToName

			httpReq.PaymentsType = util.ToInt32(orderInfo.PayType)
			httpReq.OrderType = proto.OrderType(util.ToInt32(orderInfo.Type))

			if util.ToInt(httpReq.DepartureTime) == 0 {
				httpReq.DepartureTime = util.ToString(time.Now().Unix()) // xyq 首次预估用当前时间，二次预估用端上的用户勾选班次时间
			}

			httpReq.StartStationId = util.ToInt64(orderInfo.StationId)
			httpReq.EndStationId = util.StrPtrToI64(orderInfo.BusDestStationId)
			httpReq.RouteId = util.ToInt64(orderInfo.ComboId)

			serviceReq.CommonData.PreBusServiceShiftId = util.StringPtr2String(orderInfo.BusServiceShiftId)
			serviceReq.CommonData.PreDepartureTime = orderInfo.DepartureTime
			serviceReq.CommonData.PreEstimateId = orderInfo.EstimateId
			serviceReq.CommonData.RouteType = orderInfo.RouteType

			if orderInfo.PassengerDetail != nil && *orderInfo.PassengerDetail != "\"\"" {
				str := "`" + *orderInfo.PassengerDetail + "`"
				s, _ := strconv.Unquote(str)
				var passengers []*passenger_common.PassengerDetail
				err = json.Unmarshal([]byte(s), &passengers)
				serviceReq.CommonData.PassengerDetail = passengers
				if err != nil {
					log.Trace.Warnf(ctx, trace.DLTagUndefined, "Unmarshal PassengerDetail err: %v,oid:%v,PassengerDetail:%v", err, orderID, orderInfo.PassengerDetail)
					return BizError.ErrInvalidArgument
				}
			}

			if orderInfo.CarpoolCashierRegisterType != nil {
				serviceReq.CommonData.IsCommonCashierOrder = util2.NewOrderUtil().IsCommonCashierOrder(util.String2Int(ctx, *orderInfo.CarpoolCashierRegisterType))
			}
		}
		return nil
	}
}

func SetPageType(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {
		if httpReq, ok := serviceReq.ReqFromParams.(*proto.CarpoolEstimateRequest); ok {
			serviceReq.SetPageType(intercity_common.GetPageType(httpReq.RouteId))
		}
		return nil
	}
}
