package other_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/assistant_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_estimate"
)

type PAssistantEstimateController struct {
}

func (c *PAssistantEstimateController) PAssistantEstimate(ctx context.Context, req *proto.PBDEstimateReq) (rsp *proto.B2BEstimateRsp) {
	rsp = new(proto.B2BEstimateRsp)
	var (
		bizError  = BizError.Success
		err       error
		passenger *passport.UserInfo
	)
	defer func() {
		if rsp.Data == nil {
			rsp.Data = &proto.B2BEstimateData{}
		}
		rsp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()
	err = pbd_estimate.CheckParams(ctx, req)
	if err != nil {
		return
	}
	if passenger, err = tryGetUserInfo(ctx, req.Token, int(req.AccessKeyId)); err != nil {
		err = BizError.ErrNotLogin
		return
	}
	pg, err := assistant_estimate.InitLogic(ctx, passenger, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	rsp.Data, err = pg.DoBizLogic(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}
	return
}
