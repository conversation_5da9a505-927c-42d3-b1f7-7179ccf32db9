package other_estimate

import (
	"context"
	"strconv"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_estimate"
)

const AccessKeyIDPBD = 25

type PPBDEstimateController struct {
}

func (c *PPBDEstimateController) PPbdEstimate(ctx context.Context, req *proto.PBDEstimateReq) (rsp *proto.B2BEstimateRsp) {
	rsp = new(proto.B2BEstimateRsp)
	var (
		bizError  = BizError.Success
		err       error
		passenger *passport.UserInfo
	)
	defer func() {
		if rsp.Data == nil {
			rsp.Data = &proto.B2BEstimateData{}
		}
		rsp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()
	err = pbd_estimate.CheckParams(ctx, req)
	if err != nil {
		return
	}
	if passenger, err = tryGetUserInfo(ctx, req.Token, int(req.AccessKeyId)); err != nil {
		err = BizError.ErrNotLogin
		return
	}
	pg, err := pbd_estimate.InitLogic(ctx, passenger, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	rsp.Data, err = pg.DoBizLogic(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		// log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}
	return
}

func tryGetUserInfo(ctx context.Context, token string, accessKeyID int) (*passport.UserInfo, error) {
	if token != "" {
		return passport.GetUserInfo(ctx, token, "")
	}
	// 上线中死锁pbd场景，需要手动处理一下，上线后删除即可
	if accessKeyID == AccessKeyIDPBD {
		return &passport.UserInfo{}, nil
	}
	param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(accessKeyID))
	toggle, err := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
	if err != nil || toggle.IsAllow() {
		return nil, BizError.ErrNotLogin
	}
	return &passport.UserInfo{}, nil
}
