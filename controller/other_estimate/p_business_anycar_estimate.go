package other_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v1"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_anycar_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type PBusinessAnyCarEstimateController struct {
}

func (c *PBusinessAnyCarEstimateController) PBusinessAnyCarEstimate(ctx context.Context, req *proto.B2BAnyCarEstimateReq) (rsp *proto.B2BAnyCarEstimateRsp) {
	var (
		respData *proto.B2BEstimateData
		errno    int
	)
	rsp = &proto.B2BAnyCarEstimateRsp{}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	respData, errno = c.do(ctx, req)
	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = respData
	}

	return rsp
}

func (c *PBusinessAnyCarEstimateController) do(ctx context.Context, req *proto.B2BAnyCarEstimateReq) (rsp *proto.B2BEstimateData, err int) {

	var products []*biz_runtime.ProductInfoFull

	defer func() {
		util.Go(ctx, func() {
			business_estimate.AddPublicLog(ctx, products)
		})
	}()

	productsGenerator, errno := business_anycar_estimate.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, _ = anycar_v1.GenProducts(ctx, productsGenerator)
	if len(products) == 0 {
		return nil, consts.ErrnoNoProductOpen
	}

	res, errno := business_estimate.BuildResponse(ctx, productsGenerator.BaseReqData, products)
	if errno != consts.NoErr {
		return nil, errno
	}
	return res, consts.NoErr
}
