package other_estimate

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/passenger-common/common/errcode"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/logic/abroad_estimate"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type OverseaEstimateController struct{}

func (a *OverseaEstimateController) POverseaEstimate(ctx context.Context, req *proto.OverseaEstimateReq) (resp *proto.OverseaEstimateResponse) {
	resp = new(proto.OverseaEstimateResponse)
	var (
		bizError  = BizError.Success
		err       error
		noCarInfo *proto.OverseaNoCarInfo
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.OverseaEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
		if bizError.Errno() == BizError.ErrnoOverseaNoProductPrice {
			resp.NoCarInfo = noCarInfo
		}
	}()
	estimateRequest, b := a.NewServiceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	service, err := abroad_estimate.BuildService(ctx, estimateRequest)
	if err != nil || service == nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx)
	if err != nil || resp.Data == nil {
		bizError = BizError.ErrSystem
		// 品类开城失败
		if errors.Is(err, consts.ErrorGetFromDDSFail) {
			bizError = BizError.ErrOverseaNoOpenCity
			a.writePublicLog(ctx, bizError.Errno(), estimateRequest)
			return
		}
		// tc报价失败
		if errors.Is(err, consts.ErrorGetFromPriceApiFail) {
			bizError = BizError.ErrOverseaNoProductPrice
			noCarInfo = service.BuildNoCarInfo(ctx)
			a.writePublicLog(ctx, bizError.Errno(), estimateRequest)
			return
		}
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}
	a.writePublicLog(ctx, errcode.ErrSuccess.Code(), estimateRequest)
	return
}

func (a *OverseaEstimateController) NewServiceRequest(ctx context.Context, httpReq *proto.OverseaEstimateReq) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypePageTypeAbroadEstimate)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		a.CheckParamsMutli(ctx, serviceReq),                                // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
	})

	return serviceReq, do

}

func (a *OverseaEstimateController) CheckParamsMutli(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.OverseaEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.Appversion == "" || req.Lang == "" {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}

// 生产public日志，用于做固定错误码上报，监控大盘使用
func (a *OverseaEstimateController) writePublicLog(ctx context.Context, errNo int, req *param_handler.EstimateRequest) {
	logInfo := map[string]interface{}{
		"error_code": errNo,
	}
	// 用户信息
	if req != nil && req.CommonData.Passenger != nil {
		logInfo["passenger_id_no"] = req.CommonData.Passenger.PID
		logInfo["user_id_no"] = req.CommonData.Passenger.UID
		if req.CommonData.Passenger.IsTestCell {
			logInfo["is_test_cell"] = 1
		} else {
			logInfo["is_test_cell"] = 0
		}
	}
	// 城市信息
	request, ok := req.ReqFromParams.(*proto.OverseaEstimateReq)
	if ok {
		logInfo["from_area"] = request.FromArea
		logInfo["from_name"] = util.StringEscape(request.FromName)
		logInfo["to_area"] = request.ToArea
		logInfo["to_name"] = util.StringEscape(request.ToName)
		logInfo["app_version"] = request.Appversion
		logInfo["access_key_id"] = request.AccessKeyId
	}
	log.Public.Public(ctx, "g_jw_estimate_monitor", logInfo)
}
