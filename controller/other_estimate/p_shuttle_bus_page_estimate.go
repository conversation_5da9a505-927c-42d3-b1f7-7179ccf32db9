package other_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"runtime/debug"
	"time"
)

const (
	ErrmsgWithoutOperationTimeDefault = "抱歉，不在运营时间内"
	ErrmsgWithoutOperationTime        = "抱歉，不在运营时间内，\n当前区域最近的运营时间为"
)

type ShuttleBusPageEstimateController struct{}

func (p *ShuttleBusPageEstimateController) PShuttleBusPageEstimate(ctx context.Context, req *proto.ShuttleBusPageEstimateReq) (rsp *proto.ShuttleBusPageEstimateRsp) {
	rsp = new(proto.ShuttleBusPageEstimateRsp)
	var (
		err      error
		logic    *shuttle_bus_estimate.LandingPageLogic
		bizError = BizError.Success
	)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		rsp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			errmsg := bizError.Error()
			if bizError.Errno() == BizError.ErrnoInSceneAreaAndWithoutOperationTime {
				errmsg = getLastOperationTime(logic.CommonLogic)
			}
			rsp.Errmsg = errmsg
		}
	}()
	if logic, err = shuttle_bus_estimate.LandingPageNewLogic(ctx, req); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	if err = logic.Do(ctx, rsp); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	return rsp
}

func getLastOperationTime(commonLogic *shuttle_bus_estimate.ShuttleBusCommonLogic) string {
	var (
		times   []string
		hitTime int64
		dataStr string
		timeStr string
	)
	if len(commonLogic.RegionInfo.Date) == 0 || len(commonLogic.RegionInfo.EveryDayTime) == 0 {
		return ErrmsgWithoutOperationTimeDefault
	}
	timeNum := len(commonLogic.RegionInfo.EveryDayTime)
	// 获取当前时间点的运营时间区间
	for i := 0; i < timeNum; i++ {
		item := commonLogic.RegionInfo.EveryDayTime[i]
		if len(item.OnePeriod) != 2 {
			continue
		}
		timePrefix := time.Now().Format("2006-01-02")
		startTimeStr := timePrefix + " " + item.OnePeriod[0]
		if startTimeStr == "" {
			continue
		}
		startTime := util.ParseAllDate(startTimeStr)
		nowTime := time.Now().Unix()
		if nowTime < startTime {
			times = append(item.OnePeriod)
			break
		}
	}
	// 获取日期
	data := commonLogic.RegionInfo.Date
	startStr := cast.ToString(data[0])
	endStr := cast.ToString(data[1])
	if startStr == "" || endStr == "" {
		return ErrmsgWithoutOperationTimeDefault
	}
	startTime := shuttle_bus_estimate.ParseAllDate(startStr)
	endTime := shuttle_bus_estimate.ParseAllDate(endStr)
	nowTime := time.Now().Unix()
	if nowTime < startTime {
		hitTime = startTime
	} else if nowTime > endTime {
		return ErrmsgWithoutOperationTimeDefault
	} else {
		if len(times) > 0 {
			hitTime = nowTime
		} else {
			// 加一天
			now := time.Now()
			oneDay := 24 * time.Hour
			nextDay := now.Add(oneDay)
			nextDataInt := nextDay.Unix()
			if nextDataInt > endTime {
				return ErrmsgWithoutOperationTimeDefault
			}
			hitTime = nextDataInt
		}
	}
	t := time.Unix(hitTime, 0)
	month := int(t.Month())
	dataStr = cast.ToString(month) + "月" + cast.ToString(t.Day()) + "日"
	if len(times) > 0 {
		timeStr = times[0] + "~" + times[1]
	} else {

		times = append(commonLogic.RegionInfo.EveryDayTime[0].OnePeriod)
		timeStr = times[0] + "~" + times[1]
	}
	if dataStr != "" && timeStr != "" {
		return ErrmsgWithoutOperationTime + dataStr + timeStr
	}
	return ErrmsgWithoutOperationTimeDefault
}
