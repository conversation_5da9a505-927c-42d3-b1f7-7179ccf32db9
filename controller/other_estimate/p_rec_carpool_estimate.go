package other_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/rec_carpool_estimate"
)

type PRecCarpoolEstimateController struct{}

func (c *PRecCarpoolEstimateController) PRecCarpoolEstimate(ctx context.Context, req *proto.RecCarpoolEstimateReq) (rsp *proto.RecCarpoolEstimateResp) {
	rsp = new(proto.RecCarpoolEstimateResp)
	var (
		bizError  = BizError.Success
		err       error
		passenger *passport.UserInfo
	)
	defer func() {
		if rsp.Data == nil {
			rsp.Data = &proto.RecCarpoolEstimateResponse{}
		}
		rsp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
			if BizError.IsDegradeErrno(int(rsp.Errno)) {
				proto.SetHttpCode(ctx, BizError.StatusRateLimit)
			}
		}
	}()

	err = rec_carpool_estimate.CheckParams(ctx, req)
	if err != nil {
		return
	}
	if passenger, err = tryGetUserInfo(ctx, req.Token, int(req.AccessKeyId)); err != nil {
		if err == passport.ErrOffline {
			err = BizError.ErrNotLogin
		} else {
			err = BizError.ErrSystem
		}
		return
	}
	pg, err := rec_carpool_estimate.InitLogic(ctx, passenger, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}

	rsp.Data, err = pg.DoBizLogic(ctx)

	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		if err == decision.ErrWithErrnoStopoverPointsConflictsWithScenes {
			bizError = BizError.ErrStopoverPointsConflictsWithScenes
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}

	return
}
