package other_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	homepageEstimate "git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2"
	"git.xiaojukeji.com/nuwa/trace"
)

type HomeEstimateV2Controller struct{}

func (h *HomeEstimateV2Controller) PHomePageCallCarEstimateV2(ctx context.Context, req *proto.HomePageCallCarEstimateReq) (
	rsp *proto.HomePageCallCarEstimateRspV2) {

	rsp = new(proto.HomePageCallCarEstimateRspV2)
	var (
		err      error
		bizError = BizError.Success
	)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		rsp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()

	serviceRequest, paramErr := homepageEstimate.NewServiceRequest(ctx, req)
	if paramErr != nil {
		bizError = paramErr
		return
	}

	logic, err := homepageEstimate.NewService(ctx, serviceRequest)
	if err != nil {
		bizError = BizError.ErrNotLogin
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	if logic == nil {
		return
	}

	if rsp.Data, err = logic.Do(ctx, req); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	return rsp
}
