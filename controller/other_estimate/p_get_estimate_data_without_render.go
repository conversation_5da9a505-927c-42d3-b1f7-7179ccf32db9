package other_estimate

import (
	"context"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_estimate_data_without_render"
	trace "git.xiaojukeji.com/lego/context-go"
)

type PGetEstimateDataWithoutRenderController struct {
}

func (pewr PGetEstimateDataWithoutRenderController) PGetEstimateDataWithoutRender(ctx context.Context, req *proto.MultiEstimatePriceRequest) (resp *proto.PGetEstimateDataWithoutRenderResp) {
	var (
		errno int
		rsp   map[string]*proto.PGetEstimateDataWithoutRenderEstimateData
	)

	resp = new(proto.PGetEstimateDataWithoutRenderResp)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	}()

	rsp, errno = get_estimate_data_without_render.EstimateWithoutRender(ctx, req)

	if consts.NoErr != errno {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = rsp
	}

	return resp
}
