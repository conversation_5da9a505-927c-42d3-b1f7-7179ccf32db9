package other_estimate

import (
	"context"
	"errors"
	"fmt"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	didimini "git.xiaojukeji.com/gulfstream/mamba/logic/didimini_estimate"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type PDidiMiniEstimateController struct {
}

func (c *PDidiMiniEstimateController) PDidiMiniEstimate(ctx context.Context, req *proto.PDidiMiniEstimateReq) (rsp *proto.PDidiMiniEstimateResp) {
	var (
		rspData *proto.PDidiMiniEstimateData
		errno   int
	)
	rsp = &proto.PDidiMiniEstimateResp{
		TraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
	}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	rspData, errno = c.do(ctx, req)
	if rspData == nil {
		rspData = &proto.PDidiMiniEstimateData{}
	}
	rspData.SetEstimateTraceId(util.GetTraceIDFromCtxWithoutCheck(ctx))
	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}
	return rsp
}

func (c *PDidiMiniEstimateController) do(ctx context.Context, req *proto.PDidiMiniEstimateReq) (rsp *proto.PDidiMiniEstimateData, errno BizError.BizErrno) {
	var (
		logic *didimini.Service
		err   error
	)
	errno = BizError.ErrnoSystemError
	if logic, err = didimini.NewService(ctx, req); err != nil {
		var _err BizError.BizError
		if errors.As(err, &_err) {
			errno = _err.Errno()
		}
		return
	}

	if rsp, err = logic.Do(ctx); err != nil {
		var _err BizError.BizError
		if errors.As(err, &_err) {
			errno = _err.Errno()
		}
		return
	}
	errno = BizError.ErrnoSuccess
	return

}
