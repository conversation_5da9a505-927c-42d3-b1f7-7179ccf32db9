package other_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_multi_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type IntercityMutliEstimateController struct{}

func (p *IntercityMutliEstimateController) PIntercityMultiEstimateEstimate(ctx context.Context, req *proto.IntercityMultiEstimateRequest) (resp *proto.IntercityMultiEstimateResponse) {
	resp = new(proto.IntercityMultiEstimateResponse)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.IntercityMultiEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, b := NewServiceRequestMutli(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := intercity_multi_estimate.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewServiceRequestMutli(ctx context.Context, httpReq *proto.IntercityMultiEstimateRequest) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypeIntercityStationEstimate)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckParamsMutli(ctx, serviceReq),                                  // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
	})

	return serviceReq, do

}

func CheckParamsMutli(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.IntercityMultiEstimateRequest)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.Lang == "" || req.EndCity == 0 || req.StartCity == 0 || req.Token == "" {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
