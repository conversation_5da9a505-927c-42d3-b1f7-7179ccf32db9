// Package controller顺风车预估接口
package other_estimate

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/sfc_estimate"
)

// SFCEstimateController 顺风车预估
type SFCEstimateController struct {
}

// PSFCEstimate 顺风车预估
func (c *SFCEstimateController) PSFCEstimate(ctx context.Context, req *proto.SFCEstimateRequest) (resp *proto.SFCMultiEstimateResponse) {
	resp = new(proto.SFCMultiEstimateResponse)
	var (
		err      error
		logic    *sfc_estimate.Logic
		bizError = BizError.Success
	)
	resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

	defer func() {
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	//参数校验
	validateErr := req.Validate()
	if validateErr != nil {
		resp.Errno = int32(BizError.ErrnoInvalidArgument)
		resp.Errmsg = validateErr.Error()
		return resp
	}

	//获取一些品类，出发时间，最大座位数等参数信息
	if logic, err = sfc_estimate.NewLogic(ctx, req, resp.TraceId); err != nil {
		if errors.Is(err, BizError.ErrCheckRiskFailed) {
			resp.Errno = int32(BizError.ErrnoCheckRiskFailed)
			resp.Errmsg = err.Error()
			return
		}

		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	//获取一些价格信息
	if resp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	return resp
}
