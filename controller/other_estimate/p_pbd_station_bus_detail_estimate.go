package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_detail_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type PbdStationBusDetailEstimateController struct{}

func (p *PbdStationBusDetailEstimateController) PPbdStationBusDetailEstimate(ctx context.Context, req *proto.PbdStationBusDetailEstimateReq) (resp *proto.PbdStationBusDetailEstimateRsp) {
	resp = new(proto.PbdStationBusDetailEstimateRsp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.PsbEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, quotation, b := NewPbdStationBusDetailEstimateServiceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := pbd_station_bus_detail_estimate.BuildService(ctx, quotation, estimateRequest)
	if err != nil {
		bizError = BizError.ErrInvalidArgument
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewPbdStationBusDetailEstimateServiceRequest(ctx context.Context, httpReq *proto.PbdStationBusDetailEstimateReq) (*param_handler.EstimateRequest, *biz_runtime.Quotation, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypeIntercityStationEstimate)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckPbdStationBusDetailParams(ctx, serviceReq), // 参数校验
	})
	quotationInfo, err := pbd_station_bus_detail_estimate.NewQuotationInfo(httpReq.EstimateId).Do(ctx)
	if err != nil {
		return nil, nil, err
	}
	return serviceReq, quotationInfo, do

}

func CheckPbdStationBusDetailParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.PbdStationBusDetailEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.EstimateId == "" {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
