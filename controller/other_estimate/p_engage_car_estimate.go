package other_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/engage_car_estimate"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type EngageCarEstimateController struct{}

func (e *EngageCarEstimateController) PEngageCarEstimate(ctx context.Context, req *proto.EngageCarEstimateReq) (resp *proto.EngageCarEstimateResponse) {
	resp = new(proto.EngageCarEstimateResponse)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.EngageCarEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, b := e.NewServiceRequestMutli(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := engage_car_estimate.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrAreaNotOpenService
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func (e *EngageCarEstimateController) NewServiceRequestMutli(ctx context.Context, httpReq *proto.EngageCarEstimateReq) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		e.checkParams(ctx, serviceReq), // 参数校验
	})

	return serviceReq, do

}

func (e *EngageCarEstimateController) checkParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.EngageCarEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.Lang == "" || req.FromCity == 0 || req.Uid == 0 || req.StartTime == 0 {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
