package other_estimate

import (
	"context"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/logic/bargain_range_estimate"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type BargainRangeEstimateController struct{}

func (p *BargainEstimateController) PBargainRangeEstimate(ctx context.Context, req *proto.BargainRangeEstimateReq) (rsp *proto.BargainRangeEstimateRsp) { //nolint
	rsp = new(proto.BargainRangeEstimateRsp)
	var (
		err      error
		logic    *bargain_range_estimate.Logic
		bizError = BizError.Success
	)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		rsp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "pBargainRangeEstimate error no=%v", bizError.Errno())
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()

	if logic, err = bargain_range_estimate.NewLogic(ctx, req); err != nil || logic == nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	if rsp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	return rsp
}
