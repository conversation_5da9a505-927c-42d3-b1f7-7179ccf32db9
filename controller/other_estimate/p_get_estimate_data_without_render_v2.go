package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_estimate_data_without_render"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type PGetEstimateDataWithoutRenderV2Controller struct {
}

func (p PGetEstimateDataWithoutRenderV2Controller) PGetEstimateDataWithoutRenderV2(ctx context.Context, req *proto.PGetEstimateDataWithoutRenderReq) (resp *proto.PGetEstimateDataWithoutRenderResp) {
	var (
		errno int
		rsp   map[string]*proto.PGetEstimateDataWithoutRenderEstimateData
	)

	resp = new(proto.PGetEstimateDataWithoutRenderResp)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	}()

	rsp, errno = get_estimate_data_without_render.EstimateWithoutRenderV2(ctx, req)

	if consts.NoErr != errno {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = rsp
	}

	return resp
}
