package other_estimate

import (
	"context"
	"errors"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate"

	"git.xiaojukeji.com/nuwa/trace/v2"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/minibus_estimate"
)

type MiniBusEstimatePriceController struct {
}

func (c MiniBusEstimatePriceController) MiniBusEstimatePrice(ctx context.Context, req *proto.MiniBusEstimateRequest) (resp *proto.MiniBusEstimateResp) {
	resp = new(proto.MiniBusEstimateResp)
	var (
		bizError  = BizError.Success
		err       error
		passenger *passport.UserInfo
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.MiniBusEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	err = minibus_estimate.CheckParams(ctx, req)
	if err != nil {
		bizError = BizError.ErrInvalidArgument
		return
	}

	// 兼容端上逻辑，如果是智能小巴的来源，转发到智能小巴controller逻辑里
	if smart_bus_estimate.SmartBusPageType(ctx, req) {
		sc := &SmartBusEstimatePriceController{}
		return sc.SmartBusEstimatePrice(ctx, req)
	}

	//请求 passport
	if passenger, err = passport.GetUserInfo(ctx, req.Token, ""); err != nil {
		if err == passport.ErrOffline {
			bizError = BizError.ErrNotLogin
		} else {
			bizError = BizError.ErrSystem
		}
		return
	}

	pg, err := minibus_estimate.InitLogic(ctx, passenger, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp, err = pg.DoBizLogic(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if errors.Is(err, consts.ErrorGetFromDDSFail) {
			bizError = BizError.ErrInvalidArgument
			return
		}
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}

	return resp
}
