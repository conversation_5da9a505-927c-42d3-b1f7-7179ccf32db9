package controller

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/passenger_begin_charge"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// PPassengerBeginChargeController 开始计费，在司机开始计费节点，提供透传数据给账单。弱依赖
type PPassengerBeginChargeController struct {
	base_controller.BaseController
}

func (p *PPassengerBeginChargeController) PPassengerBeginCharge(ctx context.Context, req *proto.PPassengerBeginChargeRequest) *proto.PPassengerBeginChargeResponse {
	var (
		err     NewErrors.BizError
		resp    = &proto.PPassengerBeginChargeResponse{}
		process public_log.WritePublicFunc
	)

	defer p.RecoverPanic(ctx, "p_passenger_begin_charge", resp)

	resp.Data, err = passenger_begin_charge.PassengerBeginCharge(ctx, req)
	p.HandleResp(ctx, resp, err)
	p.SetIsAsync(true).WritePublicLog(ctx, process)

	return resp
}
