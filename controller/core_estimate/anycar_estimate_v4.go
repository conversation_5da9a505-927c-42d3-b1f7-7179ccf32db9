package core_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4"
)

// AnyCarEstimateV4Controller 追加车型3.0(带缓存)
type AnyCarEstimateV4Controller struct {
}

func (c *AnyCarEstimateV4Controller) AnyCarEstimateV4(ctx context.Context, req *proto.AnyCarEstimateV4Req) *proto.AnyCarEstimateV4Resp {
	var (
		rspData *proto.AnyCarEstimateAppendCarForm
		errno   int
		rsp     = &proto.AnyCarEstimateV4Resp{}
	)
	rspData, errno = anycar_v4.EstimateAnyCar(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
