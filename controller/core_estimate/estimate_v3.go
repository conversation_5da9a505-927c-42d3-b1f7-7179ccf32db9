package core_estimate

import (
	"context"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v3"
)

// EstimateV3Controller 主预估go版本
type EstimateV3Controller struct {
	base_controller.BaseController
}

func (e *EstimateV3Controller) PEstimateV3(ctx context.Context, req *proto.PEstimateV3DataReq) *proto.PEstimateV3DataResponse {
	var (
		resp = &proto.PEstimateV3DataResponse{}
		err  NewErrors.BizError
	)
	defer e.RecoverPanic(ctx, "p_estimate_v3_data", resp)
	resp.Data, err = estimate_v3.RenderEstimateData(ctx, req)
	e.HandleResp(ctx, resp, err)
	return resp
}
