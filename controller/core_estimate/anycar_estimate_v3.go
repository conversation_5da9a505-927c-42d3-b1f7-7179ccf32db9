package core_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3"
)

// AnyCarEstimateV3Controller 追加车型3.0
type AnyCarEstimateV3Controller struct {
}

func (c *AnyCarEstimateV3Controller) AnyCarEstimateV3(ctx context.Context, req *proto.AnyCarEstimateReq) *proto.AnyCarEstimateV3Rsp {
	var (
		rspData *proto.NewFormEstimateResponse
		errno   int
		rsp     = &proto.AnyCarEstimateV3Rsp{}
	)
	rspData, errno = anycar_v3.EstimateAnyCar(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
