package core_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type CarpoolEstimateController struct {
	base_controller.BaseController
}

// PCarpoolEstimatePrice 拼车预估
func (cc CarpoolEstimateController) PCarpoolEstimatePrice(ctx context.Context, req *proto.CarpoolEstimateRequest) (resp *proto.CarpoolEstimateResponse) {
	resp = new(proto.CarpoolEstimateResponse)
	var (
		writePublicLogFunc public_log.WritePublicFunc
		bizError           = BizError.Success
		err                error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	serviceRequest, paramErr := carpool_estimate.NewServiceRequest(ctx, req, resp)
	if paramErr != nil {
		if BizError.ErrNotSupportStopoverPoints == paramErr {
			return resp
		}
		bizError = paramErr
		return
	}

	// service
	service, err := carpool_estimate.BuildService(ctx, serviceRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}

	resp.Data, writePublicLogFunc, err = service.DoBizLogic(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}

	cc.SetIsAsync(true).WritePublicLog(ctx, writePublicLogFunc)

	return
}
