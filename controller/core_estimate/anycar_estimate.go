package core_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v1"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type AnyCarEstimateController struct {
}

func (c *AnyCarEstimateController) AnyCarEstimate(ctx context.Context, req *proto.AnyCarEstimateReq) (rsp *proto.AnyCarEstimateRsp) {
	var (
		rspData *proto.AnyCarEstimateData
		errno   int
	)
	rsp = &proto.AnyCarEstimateRsp{}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	rspData, errno = c.do(ctx, req)
	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}
	return rsp
}

func (c *AnyCarEstimateController) do(ctx context.Context, req *proto.AnyCarEstimateReq) (rspData *proto.AnyCarEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	defer func() {
		anycar_v1.AddPublicLog(ctx, products, rspData)
	}()

	productsGenerator, errno := anycar_v1.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, err := anycar_v1.GenProducts(ctx, productsGenerator)
	if len(products) == 0 && err == consts.ErrorNoProductCanEstimate {
		return nil, 0
	} else if 0 == len(products) && nil == err {
		return nil, consts.NoErr
	} else if err != nil {
		return nil, consts.ErrnoNoProductOpen
	}

	rspData, errno = anycar_v1.RenderProductList(ctx, productsGenerator, products)

	if consts.NoErr != errno {
		return nil, errno
	}

	return rspData, consts.NoErr
}
