package controller

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	meetingcarestimate "git.xiaojukeji.com/gulfstream/mamba/logic/meeting_car_estimate"
	lego "git.xiaojukeji.com/lego/context-go"
)

type MeetingCarEstimateController struct{}

func (c *MeetingCarEstimateController) MeetingCarEstimate(ctx context.Context, req *proto.MeetingCarEstimateReq) *proto.MeetingCarEstimateRsp {
	traceId := lego.GetTrace(ctx).GetTraceId()
	rsp := &proto.MeetingCarEstimateRsp{
		Errno:   0,
		Errmsg:  "success",
		TraceId: traceId,
	}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, "meeting_car", "stack: %s \n %s", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoSystemError
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
	}()
	meetingCar := meetingcarestimate.NewMeetingCar(ctx, req)
	if meetingCar == nil {
		return rsp
	}
	meetingCar.Estimate(ctx)
	rsp.Data = meetingCar.ConvertToRsp()
	return rsp
}
