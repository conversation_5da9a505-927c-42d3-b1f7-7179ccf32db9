package composite_travel

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3"
	trace "git.xiaojukeji.com/lego/context-go"
	"runtime/debug"
)

type PCompositeTravelV3Controller struct{}

// PCompositeTravel 综合推荐表单
func (c *PCompositeTravelV3Controller) PCompositeTravelV3(ctx context.Context, req *proto.CompositeTravelV3Req) (resp *proto.CompositeTravelV3Res) {
	var (
		bizError = BizError.Success
	)
	resp = new(proto.CompositeTravelV3Res)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	// 1,初始化参数
	service, err := composite_travel_v3.NewService(ctx, req)
	if err != nil {
		resp.Errno = consts.ErrnoParams
		resp.Errmsg = err.Error()
		return resp
	}

	// 2.先从缓存中获取
	dataCache := service.GetRespFromCache(ctx)
	if dataCache != nil {
		resp.Data = dataCache
		return resp
	}

	// 3.获取下游各方数据
	service.GetOriginPlanList(ctx)

	// 4. 推荐前校验
	err = service.CheckBeforeRecommend(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "before_recommend_check err||err=%v", err)
		return resp
	}

	// 5.获取推荐结果
	err = service.Recommend(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "recommend err||err=%v", err)
		return resp
	}

	// 6.渲染
	renderData, err := service.Render(ctx)
	if err != nil {
		bizError = BizError.ErrRender
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "render err||err=%v", err)
		return resp
	}
	resp.Data = renderData

	// 日志
	go service.WritePublicLog(ctx)
	return resp
}

func (c *PCompositeTravelV3Controller) PCompositeTravelV3Post(ctx context.Context, req *proto.CompositeTravelV3Req) (resp *proto.CompositeTravelV3Res) {
	return c.PCompositeTravelV3(ctx, req)
}
