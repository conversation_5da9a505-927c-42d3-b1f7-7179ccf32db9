package composite_travel

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2"
	trace "git.xiaojukeji.com/lego/context-go"
)

type PCompositeTravelV2Controller struct{}

// PCompositeTravelV2 新8.5综合推荐表单
func (c *PCompositeTravelV2Controller) PCompositeTravelV2(ctx context.Context, req *proto.CompositeTravelV2Req) (resp *proto.CompositeTravelV2Res) {
	var (
		bizError = BizError.Success
	)
	resp = new(proto.CompositeTravelV2Res)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	// 1,初始化参数
	service, err := composite_travel_v2.NewService(ctx, req)
	if err != nil {
		bizError = BizError.ErrReqNotAllow
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "service init err||err=%v", err)
		return resp
	}

	// 2. 获取下游各方案数据
	service.GetOriginPlanList(ctx)

	// 3. 获取推荐结果
	err = service.Recommend(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "recommend err||err=%v", err)
		return resp
	}

	// 4. 渲染对端数据
	planList, err := service.Render(ctx)
	if err != nil {
		bizError = BizError.ErrRender
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "render err||err=%v", err)
		return resp
	}

	resp.Data = planList

	// 日志
	go service.WritePublicLog(ctx)
	return resp
}
