package composite_travel

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel"
	trace "git.xiaojukeji.com/lego/context-go"
)

type PCompositeTravelController struct{}

// PCompositeTravel 综合推荐表单
func (c *PCompositeTravelController) PCompositeTravel(ctx context.Context, req *proto.CompositeTravelReq) (resp *proto.CompositeTravelRes) {
	resp = new(proto.CompositeTravelRes)
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoSystemError
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	}()

	// 1,初始化参数
	service, err := composite_travel.NewService(ctx, req)
	if err != nil {
		resp.Errno = consts.ErrnoParams
		resp.Errmsg = err.Error()
		return resp
	}

	// 2. 获取方案
	planMap, evaluation := service.GetOriginPlanList(ctx)

	// 3. 方案准入
	service.Decision(ctx, planMap)

	// 4. 渲染
	service.Render(ctx)

	// 5. 推荐
	planList, isRecommend := service.Recommend(ctx)

	if len(planList) < 1 {
		resp.Errno = consts.ErrnoGetProducts
		resp.Errmsg = service.DCMPConf.NoPlanTips
	}
	resp.Data = &proto.CompositeTravelData{
		PlanList:                 planList,
		IsRecommend:              isRecommend,
		CompositeTravelAgreement: &service.DCMPConf.CompositeTravelAgreement,
	}
	if evaluation != nil {
		resp.Data.Evaluation = evaluation
	}

	// 日志
	go service.WritePublicLog(ctx)
	return resp
}
