package composite_travel

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_poi"
	"git.xiaojukeji.com/nuwa/trace"
)

type PCompositeTravelPoiController struct{}

// 综合推荐表单V3
func (c *PCompositeTravelPoiController) PCompositeTravelPoi(ctx context.Context, req *proto.CompositeTravelPoiReq) (resp *proto.CompositeTravelPoiRes) {
	resp = new(proto.CompositeTravelPoiRes)
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoSystemError
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	}()

	// 1,初始化参数
	service, err := composite_travel_poi.NewService(ctx, req)
	if err != nil {
		resp.Errno = consts.ErrnoParams
		resp.Errmsg = err.Error()
		return resp
	}

	// 2. 获取方案
	planMap, _ := service.Service.GetOriginPlanList(ctx)

	// 4. 方案准入
	service.Service.Decision(ctx, planMap)

	// 5. 渲染+
	service.Service.Render(ctx)

	// 6. 推荐
	planList := service.Recommend(ctx, req)

	if len(planList) <= 0 {
		return resp
	}
	// 7. 二次渲染
	data, err := service.Render(ctx)
	if err != nil {
		resp.Errno = consts.ErrnoSystemError
		resp.Errmsg = err.Error()
		return resp
	}
	resp.Data = data
	return resp
}
