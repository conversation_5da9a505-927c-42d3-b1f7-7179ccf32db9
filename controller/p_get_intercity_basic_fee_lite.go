package controller

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_intercity_basic_fee_lite"
	context2 "git.xiaojukeji.com/lego/context-go"
)

// GetIntercityBasicFeeLiteController 获取站点巴士费用描述
type GetIntercityBasicFeeLiteController struct {
}

func (g *GetIntercityBasicFeeLiteController) PGetIntercityBasicFeeLite(ctx context.Context, req *proto.IntercityBasicFeeLiteRequest) *proto.IntercityBasicFeeLiteResponse {
	var (
		errno    int
		respData = &proto.IntercityBasicFeeLiteData{}
		resp     = &proto.IntercityBasicFeeLiteResponse{
			TraceId: context2.GetTrace(ctx).GetTraceId(),
		}
	)

	respData, errno = get_intercity_basic_fee_lite.GetIntercityBasicFeeLite(ctx, req)
	if errno != consts.NoErr {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = respData
	}

	return resp
}
