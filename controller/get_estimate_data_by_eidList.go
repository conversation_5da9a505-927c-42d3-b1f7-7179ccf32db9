package controller

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_estimate_data"
	trace "git.xiaojukeji.com/lego/context-go"
)

type PGetEstimateDataByEidListController struct {
}

// PGetEstimateDataByEidList  通过eidlist获得预估数据
func (pe PGetEstimateDataByEidListController) PGetEstimateDataByEidList(ctx context.Context, req *proto.PGetEstimateDataByEidListReq) (resp *proto.PGetEstimateDataByEidListResp) {
	resp = new(proto.PGetEstimateDataByEidListResp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError != nil {
			if bizError.Errno() != BizError.ErrnoSuccess {
				resp.Errno = int32(bizError.Errno())
				resp.Errmsg = bizError.Error()
			}
		}
	}()

	bizError = get_estimate_data.CheckReq(ctx, req)
	if bizError != nil {
		return
	}

	// service
	service, err := get_estimate_data.NewService(ctx, req)
	if err != nil {
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		resp.Data = &proto.PGetEstimateDataByEidListResponse{}
		return
	}

	resp.Data = service.GetData(ctx)

	return
}
