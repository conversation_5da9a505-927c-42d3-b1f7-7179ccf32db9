package controller

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info"
	context2 "git.xiaojukeji.com/lego/context-go"
)

type PGetGuideInfoController struct {
}

func (c *PGetGuideInfoController) PGetGuideInfo(ctx context.Context, req *proto.PGetGuideInfoReq) *proto.PGetGuideInfoRsp {
	var (
		resp = &proto.PGetGuideInfoRsp{
			Errno:   consts.NoErr,
			TraceId: context2.GetTrace(ctx).GetTraceId(),
		}
	)

	validator := req.Validate()
	if validator != nil {
		resp.Errno = int32(consts.ErrnoParams)
		resp.Errmsg = consts.GetErrMessage(consts.ErrnoParams)
	}

	respData, errno := guide_info.Handler(ctx, req)
	if errno != consts.NoErr {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Data = respData
	}

	return resp
}

func (c *PGetGuideInfoController) PGetGuideInfoPost(ctx context.Context, req *proto.PGetGuideInfoReq) *proto.PGetGuideInfoRsp {
	return c.PGetGuideInfo(ctx, req)
}
