package order_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order"
)

// EstimateByOrder 行中预估
type EstimateByOrder struct {
	base_controller.BaseController
}

func (r *EstimateByOrder) PEstimateByOrder(ctx context.Context, req *proto.PEstimateByOrderRequest) *proto.PEstimateByOrderResponse {
	var (
		err     NewErrors.BizError
		resp    = &proto.PEstimateByOrderResponse{}
		process public_log.WritePublicFunc
	)

	defer r.RecoverPanic(ctx, "p_multi_estimate_by_order", resp)

	resp.Data, err, process = multi_estimate_by_order.EstimateByOrder(ctx, req)
	r.<PERSON>p(ctx, resp, err)
	r.SetIsAsync(true).WritePublicLog(ctx, process)

	return resp
}
