package order_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/route_estimate"
)

// RouteEstimateController 路线预估
type RouteEstimateController struct {
}

func (r *RouteEstimateController) PRouteEstimate(ctx context.Context, req *proto.RouteEstimateRequest) *proto.RouteEstimateResponse {
	var (
		err      error
		respData *proto.RouteEstimateData
		resp     = &proto.RouteEstimateResponse{}
	)

	respData, err = route_estimate.RouteEstimate(ctx, req)
	resp.Data = respData

	r.handleResp(resp, err)

	return resp
}

func (r *RouteEstimateController) handleResp(resp *proto.RouteEstimateResponse, err error) {
	var bizErr BizError.BizError

	if resp == nil {
		resp = &proto.RouteEstimateResponse{}
	}

	if err != nil {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSystemError)
	} else {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSuccess)
	}

	resp.Errno = int32(bizErr.Errno())
	resp.Errmsg = bizErr.Error()
}
