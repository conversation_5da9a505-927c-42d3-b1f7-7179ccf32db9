package order_estimate

import (
	"context"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_carpool_order"
)

// EstimateCarpoolOrder 拼车行中预估
type EstimateCarpoolOrder struct {
	base_controller.BaseController
}

func (r *EstimateCarpoolOrder) PEstimateCarpoolMultiOrder(ctx context.Context, req *proto.PEstimateCarpoolOrderRequest) *proto.PEstimateCarpoolOrderResponse {
	var (
		err     NewErrors.BizError
		resp    = &proto.PEstimateCarpoolOrderResponse{}
		process public_log.WritePublicFunc
	)

	defer r.RecoverPanic(ctx, "p_estimate_carpool_multi_order", resp)

	resp, err, process = multi_estimate_carpool_order.EstimateCarpoolOrder(ctx, req)
	if resp == nil {
		resp = &proto.PEstimateCarpoolOrderResponse{}
	}

	r.HandleResp(ctx, resp, err)
	r.SetIsAsync(true).WritePublicLog(ctx, process)

	return resp
}
