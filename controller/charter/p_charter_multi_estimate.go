package charter

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/charter_multi_estimate"
	context2 "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/trace/v2"
	"runtime/debug"
)

type PCharterMultiEstimateController struct{}

func (c *PCharterMultiEstimateController) PCharterMultiEstimate(ctx context.Context, req *proto.CharterMultiEstimateReq) (resp *proto.CharterMultiEstimateRsp) {
	var (
		errno   int
		rspData *proto.CharterMultiEstimateData
	)

	resp = &proto.CharterMultiEstimateRsp{
		TraceId: context2.GetTrace(ctx).GetTraceId(),
	}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoSystemError
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
	}()

	rspData, errno = charter_multi_estimate.CharterMultiEstimate(ctx, req)
	resp.Errno = int32(errno)

	if charter_multi_estimate.ErrnoSuccess != errno {
		resp.Errmsg = charter_multi_estimate.GetErrMsg(ctx, errno)
	} else {
		resp.Data = rspData
	}

	return resp
}
