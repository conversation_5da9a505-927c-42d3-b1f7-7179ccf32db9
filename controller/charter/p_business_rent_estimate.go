package charter

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_rent_estimate"
	"git.xiaojukeji.com/nuwa/trace/v2"
	"runtime/debug"
)

type PBusinessRentEstimateController struct{}

func (c *PBusinessRentEstimateController) PBusinessRentEstimate(ctx context.Context, req *proto.BusinessRentEstimateReq) (resp *proto.B2BEstimateRsp) {
	var (
		errno   int
		rspData *proto.B2BEstimateData
	)

	resp = &proto.B2BEstimateRsp{}

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoSystemError
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
	}()

	rspData, errno = business_rent_estimate.BusinessRentEstimate(ctx, req)
	if consts.NoErr != errno {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = rspData
	}

	return resp
}
