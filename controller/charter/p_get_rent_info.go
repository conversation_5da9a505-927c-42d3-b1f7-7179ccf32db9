package charter

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/rent_info"
	context2 "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type PGetRentInfoController struct{}

func (c *PGetRentInfoController) PGetRentInfo(ctx context.Context, req *proto.GetRentInfoReq) *proto.GetRentInfoRsp {
	var (
		errno   int
		rspData *proto.RentInfo
		resp    = &proto.GetRentInfoRsp{
			TraceId: context2.GetTrace(ctx).GetTraceId(),
		}
	)

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoSystemError
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoSystemError)
		}
	}()

	rspData, errno = rent_info.GetRentInfo(ctx, req)

	if consts.NoErr != errno {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = rspData
	}

	return resp
}
