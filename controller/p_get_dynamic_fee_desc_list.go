package controller

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/dynamic_fee_desc_list"
	context2 "git.xiaojukeji.com/lego/context-go"
)

// GetDynamicFeeDescListController 动态获取费用描述
type GetDynamicFeeDescListController struct {
}

func (g *GetDynamicFeeDescListController) PGetDynamicFeeDescList(ctx context.Context, req *proto.DynamicFeeDescListRequest) *proto.DynamicFeeDescListResponse {
	var (
		errno    int
		respData = &proto.DynamicFeeDescListData{}
		resp     = &proto.DynamicFeeDescListResponse{
			TraceId: context2.GetTrace(ctx).GetTraceId(),
		}
	)

	respData, errno = dynamic_fee_desc_list.GetDynamicFeeDescList(ctx, req)
	if errno != consts.NoErr {
		resp.Errno = int32(errno)
		resp.Errmsg = consts.GetErrMessage(errno)
	} else {
		resp.Errno = consts.NoErr
		resp.Data = respData
	}

	return resp
}

func (g *GetDynamicFeeDescListController) PGetDynamicFeeDescListPost(ctx context.Context, request *proto.DynamicFeeDescListRequest) *proto.DynamicFeeDescListResponse {
	return g.PGetDynamicFeeDescList(ctx, request)
}
