package controller

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_landing"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type PRecommendLandingController struct{}

// PRecommendLanding maas 推荐着陆
func (c *PRecommendLandingController) PRecommendLanding(ctx context.Context, req *proto.CompositeTravelV2Req) (resp *proto.PRecommendLandingRes) {
	var (
		bizError = BizError.Success
	)
	resp = new(proto.PRecommendLandingRes)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	resp.Data, bizError = composite_landing.RecommendLanding(ctx, req)

	return resp
}
