package side

import (
	"context"
	"fmt"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data"
	trace "git.xiaojukeji.com/lego/context-go"
)

type GetFormRealDataController struct {
}

func (p GetFormRealDataController) PGetFormRealData(ctx context.Context, req *proto.PGetFormRealDataReq) (res *proto.PGetFormRealDataResp) {
	res = &proto.PGetFormRealDataResp{Errno: 0}
	var (
		bizError = BizError.Success
	)

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		res.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			res.Errno = int32(bizError.Errno())
			res.Errmsg = bizError.Error()
		}
	}()

	service, err := get_form_real_data.NewService(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return res
	}

	res.Data, err = get_form_real_data.NewRender(service).Render(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return res
	}

	//log public
	service.LogPublic(ctx, res.Data)

	return res
}

func (p GetFormRealDataController) PGetFormRealDataPost(ctx context.Context, req *proto.PGetFormRealDataReq) *proto.PGetFormRealDataResp {
	return p.PGetFormRealData(ctx, req)
}
