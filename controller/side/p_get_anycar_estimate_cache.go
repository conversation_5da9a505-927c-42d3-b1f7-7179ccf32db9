package side

import (
	"context"
	"fmt"
	"runtime/debug"

	trace "git.xiaojukeji.com/lego/context-go"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_estimate_cache"
)

type GetAnycarEstimateCacheController struct {
	base_controller.BaseController
}

func (g *GetAnycarEstimateCacheController) GetAnycarEstimateCache(ctx context.Context, request *proto.AnycarEstimateCacheReq) *proto.AnycarEstimateCacheResp {
	var (
		bizError = NewErrors.Success
		resp     = &proto.AnycarEstimateCacheResp{}
	)

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			bizError = NewErrors.ErrSystem
		}

		if bizError.Errno() != NewErrors.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	estimateInfo, err := anycar_estimate_cache.GetAnycarEstimateCache(ctx, request)
	if err != nil {
		bizError = NewErrors.ErrSystem
		if _err, ok := err.(NewErrors.BizError); ok {
			bizError = _err
		}
		return resp
	}

	resp.EstimateInfo = estimateInfo

	return resp
}
