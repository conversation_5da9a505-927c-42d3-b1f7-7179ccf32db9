// Code generated by http-gen compiler. DO NOT EDIT.
// source: mamba.idl
// argument: --client_style=protoc

// Package proto is the logic of http server.
package proto

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"

	legoTrace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/binding"
	"git.xiaojukeji.com/nuwa/golibs/rpcserver/v2/rpcserver"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/utilities"
)

var _ io.Reader
var _ fmt.State
var _ legoTrace.Trace
var _ strconv.NumError

const ErrNo = "errno"
const Errmsg = "errmsg"

type reqKeyStruct struct{}
type respHeaderKeyStruct struct{}
type respHttpCodeKeyStruct struct{}

func init() {
	rpcserver.EnableNuwaJson()
}

var (
	reqKey          reqKeyStruct
	respHeaderKey   respHeaderKeyStruct
	respHttpCodeKey respHttpCodeKeyStruct
)

func toString(i interface{}) string {
	switch v := i.(type) {
	case nil:
		return ""
	case *int:
		return strconv.FormatInt(int64(*v), 10)
	case *int32:
		return strconv.FormatInt(int64(*v), 10)
	case *int64:
		return strconv.FormatInt(int64(*v), 10)
	case int:
		return strconv.FormatInt(int64(v), 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int64:
		return strconv.FormatInt(int64(v), 10)
	case *string:
		return *v
	case string:
		return v
	default:
		return fmt.Sprint(i)
	}
}

func setTraceCustomKV(ctx context.Context, key, val string) {
	r, _ := legoTrace.GetCtxTrace(ctx)
	if r != nil {
		r.SetCustomKV(key, val)
	}
}

// GetHeader used for get requestHeader
func GetHeader(ctx context.Context, key string) string {
	req := GetReq(ctx)
	if req != nil {
		return req.Header.Get(key)
	}
	return ""
}

// GetReq used for get request from ctx
func GetReq(ctx context.Context) *http.Request {
	req, ok := ctx.Value(reqKey).(*http.Request)
	if !ok {
		return nil
	}
	return req
}

// SetHeader used for set respHeader
func SetHeader(ctx context.Context, key, value string) {
	headers, ok := ctx.Value(respHeaderKey).(http.Header)
	if ok {
		headers.Add(key, value)
	}
}

// SetCookie used for set cookie in respHeader
func SetCookie(ctx context.Context, cookie *http.Cookie) {
	if v := cookie.String(); v != "" {
		headers, ok := ctx.Value(respHeaderKey).(http.Header)
		if ok {
			headers.Add("Set-Cookie", v)
		}
	}
}

// SetHttpCode used for set httpCode
func SetHttpCode(ctx context.Context, httpCode int) {
	if code, ok := ctx.Value(respHttpCodeKey).(*int); ok {
		*code = httpCode
		_ = code
	}
	return
}

func handleForwardHeader(ctx context.Context, w http.ResponseWriter) {
	headers, ok := ctx.Value(respHeaderKey).(http.Header)
	if ok {
		for k, vs := range headers {
			for _, v := range vs {
				w.Header().Add(k, v)
			}
		}
	}
}

type MambaServer interface {
	Ping(context.Context, *PingReq) (*PingRsp, error)
	PCarpoolEstimatePrice(context.Context, *CarpoolEstimateRequest) (*CarpoolEstimateResponse, error)
	IntercityEstimatePrice(context.Context, *CarpoolEstimateRequest) (*IntercityEstimateResp, error)
	MiniBusEstimatePrice(context.Context, *MiniBusEstimateRequest) (*MiniBusEstimateResp, error)
	AnyCarEstimate(context.Context, *AnyCarEstimateReq) (*AnyCarEstimateRsp, error)
	AnyCarEstimateV3(context.Context, *AnyCarEstimateReq) (*AnyCarEstimateV3Rsp, error)
	AnyCarEstimateV4(context.Context, *AnyCarEstimateV4Req) (*AnyCarEstimateV4Resp, error)
	GetAnycarEstimateCache(context.Context, *AnycarEstimateCacheReq) (*AnycarEstimateCacheResp, error)
	DelAnycarEstimateCache(context.Context, *AnycarEstimateCacheReq) (*AnycarEstimateDelCacheResp, error)
	CombinedTravelEstimate(context.Context, *MultiEstimatePriceRequest) (*CombinedTravelEstimateRes, error)
	PCancelEstimate(context.Context, *CancelEstimateReq) (*CancelEstimateRsp, error)
	PBusinessEstimate(context.Context, *B2BEstimateReq) (*B2BEstimateRsp, error)
	PBusinessAnyCarEstimate(context.Context, *B2BAnyCarEstimateReq) (*B2BAnyCarEstimateRsp, error)
	PPbdEstimate(context.Context, *PBDEstimateReq) (*B2BEstimateRsp, error)
	PPBDAnyCarEstimate(context.Context, *PBDAnyCarEstimateReq) (*B2BAnyCarEstimateRsp, error)
	PAssistantEstimate(context.Context, *PBDEstimateReq) (*B2BEstimateRsp, error)
	PHelperEstimate(context.Context, *MultiEstimatePriceRequest) (*HelperEstimateRes, error)
	PShuttleBusPageEstimate(context.Context, *ShuttleBusPageEstimateReq) (*ShuttleBusPageEstimateRsp, error)
	PShuttleBusGuideBarEstimate(context.Context, *ShuttleBusGuideBarEstimateReq) (*ShuttleBusGuideBarEstimateRsp, error)
	PBargainEstimate(context.Context, *BargainEstimateReq) (*BargainEstimateRsp, error)
	PHomePageCallCarEstimate(context.Context, *HomePageCallCarEstimateReq) (*HomePageCallCarEstimateRsp, error)
	PHomePageCallCarEstimateV2(context.Context, *HomePageCallCarEstimateReq) (*HomePageCallCarEstimateRspV2, error)
	PCarpoolPrice(context.Context, *CarpoolEstimateRequest) (*CarpoolPriceResponse, error)
	PRecCarpoolEstimate(context.Context, *RecCarpoolEstimateReq) (*RecCarpoolEstimateResp, error)
	PCarpoolInvitationEstimate(context.Context, *CarpoolInvitationEstimateRequest) (*CarpoolInvitationEstimateResp, error)
	PGetCouponPrice(context.Context, *CouponPriceRequest) (*CouponPriceResponse, error)
	PServiceEstimate(context.Context, *ServiceEstimateRequest) (*ServiceMultiEstimateResponse, error)
	PSFCEstimate(context.Context, *SFCEstimateRequest) (*SFCMultiEstimateResponse, error)
	PSFCModifyEstimate(context.Context, *SFCModifyEstimateRequest) (*SFCModifyEstimateResponse, error)
	PSFCSimpleEstimate(context.Context, *SFCSimpleEstimateReq) (*SFCSimpleEstimateResponse, error)
	PCompositeTravel(context.Context, *CompositeTravelReq) (*CompositeTravelRes, error)
	PCompositeTravelV2(context.Context, *CompositeTravelV2Req) (*CompositeTravelV2Res, error)
	PCompositeTravelPoi(context.Context, *CompositeTravelPoiReq) (*CompositeTravelPoiRes, error)
	PCompositeTravelV3(context.Context, *CompositeTravelV3Req) (*CompositeTravelV3Res, error)
	PCompositeTravelV3Post(context.Context, *CompositeTravelV3Req) (*CompositeTravelV3Res, error)
	PCompositeTravelOptions(context.Context, *CompositeTravelOptionsReq) (*CompositeTravelOptionsRes, error)
	PRecommendLanding(context.Context, *CompositeTravelV2Req) (*PRecommendLandingRes, error)
	PGetDynamicFeeDescList(context.Context, *DynamicFeeDescListRequest) (*DynamicFeeDescListResponse, error)
	PGetDynamicFeeDescListPost(context.Context, *DynamicFeeDescListRequest) (*DynamicFeeDescListResponse, error)
	PPickOnTimeEstimate(context.Context, *PickOnTimeEstimateReq) (*PickOnTimeEstimateRsp, error)
	PGetGuideInfo(context.Context, *PGetGuideInfoReq) (*PGetGuideInfoRsp, error)
	PGetGuideInfoPost(context.Context, *PGetGuideInfoReq) (*PGetGuideInfoRsp, error)
	PGetFormRealData(context.Context, *PGetFormRealDataReq) (*PGetFormRealDataResp, error)
	PGetFormRealDataPost(context.Context, *PGetFormRealDataReq) (*PGetFormRealDataResp, error)
	PGetEstimateDataByEidList(context.Context, *PGetEstimateDataByEidListReq) (*PGetEstimateDataByEidListResp, error)
	PRouteEstimate(context.Context, *RouteEstimateRequest) (*RouteEstimateResponse, error)
	PIntercityMultiEstimateEstimate(context.Context, *IntercityMultiEstimateRequest) (*IntercityMultiEstimateResponse, error)
	PIntercityEstimateDetail(context.Context, *IntercityEstimateDetailRequest) (*IntercityEstimateDetailResponse, error)
	POrderBookingPreCancelEstimate(context.Context, *AnyCarEstimateReq) (*AnyCarEstimateV3Rsp, error)
	PInvitedEstimate(context.Context, *InvitedEstimateReq) (*InvitedEstimateRsp, error)
	PMultiPointEstimate(context.Context, *MultiPointEstimateRequest) (*MultiPointEstimateResponse, error)
	PGetEstimateDataWithoutRender(context.Context, *MultiEstimatePriceRequest) (*PGetEstimateDataWithoutRenderResp, error)
	PGetEstimateDataWithoutRenderV2(context.Context, *PGetEstimateDataWithoutRenderReq) (*PGetEstimateDataWithoutRenderResp, error)
	PGetBusinessFormRealData(context.Context, *GetBusinessFormRealDataRequest) (*GetBusinessFormRealDataResponse, error)
	PBargainRangeEstimate(context.Context, *BargainRangeEstimateReq) (*BargainRangeEstimateRsp, error)
	PStationBusInsteadOrderEstimate(context.Context, *StationBusInsteadOrderEstimateReq) (*StationBusInsteadOrderEstimateRsp, error)
	PGetIntercityBasicFeeLite(context.Context, *IntercityBasicFeeLiteRequest) (*IntercityBasicFeeLiteResponse, error)
	PDidiMiniEstimate(context.Context, *PDidiMiniEstimateReq) (*PDidiMiniEstimateResp, error)
	PEngageCarEstimate(context.Context, *EngageCarEstimateReq) (*EngageCarEstimateResponse, error)
	PPetsTravelEstimate(context.Context, *PetsTravelEstimateReq) (*PetsTravelEstimateResponse, error)
	POverseaEstimate(context.Context, *OverseaEstimateReq) (*OverseaEstimateResponse, error)
	PEstimateByOrder(context.Context, *PEstimateByOrderRequest) (*PEstimateByOrderResponse, error)
	EstimateOrderWithoutRender(context.Context, *EstimateOrderWithoutRenderReq) (*EstimateOrderWithoutRenderRsp, error)
	PPbdStationBusMultiStationPrice(context.Context, *PbdStationBusMultiStationPriceReq) (*PbdStationBusMultiStationPriceRsp, error)
	PPbdStationBusOrderEstimate(context.Context, *PbdStationBusOrderEstimateReq) (*PbdStationBusOrderEstimateRsp, error)
	PPbdStationBusMultiEstimate(context.Context, *PbdStationBusMultiEstimateReq) (*PbdStationBusMultiEstimateRsp, error)
	PPbdStationBusDetailEstimate(context.Context, *PbdStationBusDetailEstimateReq) (*PbdStationBusDetailEstimateRsp, error)
	PCharterHomePage(context.Context, *CharterHomePageReq) (*CharterHomePageRsp, error)
	PCharterMultiEstimate(context.Context, *CharterMultiEstimateReq) (*CharterMultiEstimateRsp, error)
	PPassengerBeginCharge(context.Context, *PPassengerBeginChargeRequest) (*PPassengerBeginChargeResponse, error)
	PGetRentInfo(context.Context, *GetRentInfoReq) (*GetRentInfoRsp, error)
	PBusinessRentEstimate(context.Context, *BusinessRentEstimateReq) (*B2BEstimateRsp, error)
	PEstimateCarpoolMultiOrder(context.Context, *PEstimateCarpoolOrderRequest) (*PEstimateCarpoolOrderResponse, error)
	PEstimateV3(context.Context, *PEstimateV3DataReq) (*PEstimateV3DataResponse, error)
	PGetBusinessTailorService(context.Context, *LuxMultiEstimatePriceRequest) (*BusinessTailorServiceResponse, error)
	PetAnyCarEstimate(context.Context, *AnyCarEstimateV4Req) (*AnyCarEstimateV4Resp, error)
	MeetingCarEstimate(context.Context, *MeetingCarEstimateReq) (*MeetingCarEstimateRsp, error)
}

type MambaServerNoError interface {
	Ping(context.Context, *PingReq) *PingRsp
	PCarpoolEstimatePrice(context.Context, *CarpoolEstimateRequest) *CarpoolEstimateResponse
	IntercityEstimatePrice(context.Context, *CarpoolEstimateRequest) *IntercityEstimateResp
	MiniBusEstimatePrice(context.Context, *MiniBusEstimateRequest) *MiniBusEstimateResp
	AnyCarEstimate(context.Context, *AnyCarEstimateReq) *AnyCarEstimateRsp
	AnyCarEstimateV3(context.Context, *AnyCarEstimateReq) *AnyCarEstimateV3Rsp
	AnyCarEstimateV4(context.Context, *AnyCarEstimateV4Req) *AnyCarEstimateV4Resp
	GetAnycarEstimateCache(context.Context, *AnycarEstimateCacheReq) *AnycarEstimateCacheResp
	DelAnycarEstimateCache(context.Context, *AnycarEstimateCacheReq) *AnycarEstimateDelCacheResp
	CombinedTravelEstimate(context.Context, *MultiEstimatePriceRequest) *CombinedTravelEstimateRes
	PCancelEstimate(context.Context, *CancelEstimateReq) *CancelEstimateRsp
	PBusinessEstimate(context.Context, *B2BEstimateReq) *B2BEstimateRsp
	PBusinessAnyCarEstimate(context.Context, *B2BAnyCarEstimateReq) *B2BAnyCarEstimateRsp
	PPbdEstimate(context.Context, *PBDEstimateReq) *B2BEstimateRsp
	PPBDAnyCarEstimate(context.Context, *PBDAnyCarEstimateReq) *B2BAnyCarEstimateRsp
	PAssistantEstimate(context.Context, *PBDEstimateReq) *B2BEstimateRsp
	PHelperEstimate(context.Context, *MultiEstimatePriceRequest) *HelperEstimateRes
	PShuttleBusPageEstimate(context.Context, *ShuttleBusPageEstimateReq) *ShuttleBusPageEstimateRsp
	PShuttleBusGuideBarEstimate(context.Context, *ShuttleBusGuideBarEstimateReq) *ShuttleBusGuideBarEstimateRsp
	PBargainEstimate(context.Context, *BargainEstimateReq) *BargainEstimateRsp
	PHomePageCallCarEstimate(context.Context, *HomePageCallCarEstimateReq) *HomePageCallCarEstimateRsp
	PHomePageCallCarEstimateV2(context.Context, *HomePageCallCarEstimateReq) *HomePageCallCarEstimateRspV2
	PCarpoolPrice(context.Context, *CarpoolEstimateRequest) *CarpoolPriceResponse
	PRecCarpoolEstimate(context.Context, *RecCarpoolEstimateReq) *RecCarpoolEstimateResp
	PCarpoolInvitationEstimate(context.Context, *CarpoolInvitationEstimateRequest) *CarpoolInvitationEstimateResp
	PGetCouponPrice(context.Context, *CouponPriceRequest) *CouponPriceResponse
	PServiceEstimate(context.Context, *ServiceEstimateRequest) *ServiceMultiEstimateResponse
	PSFCEstimate(context.Context, *SFCEstimateRequest) *SFCMultiEstimateResponse
	PSFCModifyEstimate(context.Context, *SFCModifyEstimateRequest) *SFCModifyEstimateResponse
	PSFCSimpleEstimate(context.Context, *SFCSimpleEstimateReq) *SFCSimpleEstimateResponse
	PCompositeTravel(context.Context, *CompositeTravelReq) *CompositeTravelRes
	PCompositeTravelV2(context.Context, *CompositeTravelV2Req) *CompositeTravelV2Res
	PCompositeTravelPoi(context.Context, *CompositeTravelPoiReq) *CompositeTravelPoiRes
	PCompositeTravelV3(context.Context, *CompositeTravelV3Req) *CompositeTravelV3Res
	PCompositeTravelV3Post(context.Context, *CompositeTravelV3Req) *CompositeTravelV3Res
	PCompositeTravelOptions(context.Context, *CompositeTravelOptionsReq) *CompositeTravelOptionsRes
	PRecommendLanding(context.Context, *CompositeTravelV2Req) *PRecommendLandingRes
	PGetDynamicFeeDescList(context.Context, *DynamicFeeDescListRequest) *DynamicFeeDescListResponse
	PGetDynamicFeeDescListPost(context.Context, *DynamicFeeDescListRequest) *DynamicFeeDescListResponse
	PPickOnTimeEstimate(context.Context, *PickOnTimeEstimateReq) *PickOnTimeEstimateRsp
	PGetGuideInfo(context.Context, *PGetGuideInfoReq) *PGetGuideInfoRsp
	PGetGuideInfoPost(context.Context, *PGetGuideInfoReq) *PGetGuideInfoRsp
	PGetFormRealData(context.Context, *PGetFormRealDataReq) *PGetFormRealDataResp
	PGetFormRealDataPost(context.Context, *PGetFormRealDataReq) *PGetFormRealDataResp
	PGetEstimateDataByEidList(context.Context, *PGetEstimateDataByEidListReq) *PGetEstimateDataByEidListResp
	PRouteEstimate(context.Context, *RouteEstimateRequest) *RouteEstimateResponse
	PIntercityMultiEstimateEstimate(context.Context, *IntercityMultiEstimateRequest) *IntercityMultiEstimateResponse
	PIntercityEstimateDetail(context.Context, *IntercityEstimateDetailRequest) *IntercityEstimateDetailResponse
	POrderBookingPreCancelEstimate(context.Context, *AnyCarEstimateReq) *AnyCarEstimateV3Rsp
	PInvitedEstimate(context.Context, *InvitedEstimateReq) *InvitedEstimateRsp
	PMultiPointEstimate(context.Context, *MultiPointEstimateRequest) *MultiPointEstimateResponse
	PGetEstimateDataWithoutRender(context.Context, *MultiEstimatePriceRequest) *PGetEstimateDataWithoutRenderResp
	PGetEstimateDataWithoutRenderV2(context.Context, *PGetEstimateDataWithoutRenderReq) *PGetEstimateDataWithoutRenderResp
	PGetBusinessFormRealData(context.Context, *GetBusinessFormRealDataRequest) *GetBusinessFormRealDataResponse
	PBargainRangeEstimate(context.Context, *BargainRangeEstimateReq) *BargainRangeEstimateRsp
	PStationBusInsteadOrderEstimate(context.Context, *StationBusInsteadOrderEstimateReq) *StationBusInsteadOrderEstimateRsp
	PGetIntercityBasicFeeLite(context.Context, *IntercityBasicFeeLiteRequest) *IntercityBasicFeeLiteResponse
	PDidiMiniEstimate(context.Context, *PDidiMiniEstimateReq) *PDidiMiniEstimateResp
	PEngageCarEstimate(context.Context, *EngageCarEstimateReq) *EngageCarEstimateResponse
	PPetsTravelEstimate(context.Context, *PetsTravelEstimateReq) *PetsTravelEstimateResponse
	POverseaEstimate(context.Context, *OverseaEstimateReq) *OverseaEstimateResponse
	PEstimateByOrder(context.Context, *PEstimateByOrderRequest) *PEstimateByOrderResponse
	EstimateOrderWithoutRender(context.Context, *EstimateOrderWithoutRenderReq) *EstimateOrderWithoutRenderRsp
	PPbdStationBusMultiStationPrice(context.Context, *PbdStationBusMultiStationPriceReq) *PbdStationBusMultiStationPriceRsp
	PPbdStationBusOrderEstimate(context.Context, *PbdStationBusOrderEstimateReq) *PbdStationBusOrderEstimateRsp
	PPbdStationBusMultiEstimate(context.Context, *PbdStationBusMultiEstimateReq) *PbdStationBusMultiEstimateRsp
	PPbdStationBusDetailEstimate(context.Context, *PbdStationBusDetailEstimateReq) *PbdStationBusDetailEstimateRsp
	PCharterHomePage(context.Context, *CharterHomePageReq) *CharterHomePageRsp
	PCharterMultiEstimate(context.Context, *CharterMultiEstimateReq) *CharterMultiEstimateRsp
	PPassengerBeginCharge(context.Context, *PPassengerBeginChargeRequest) *PPassengerBeginChargeResponse
	PGetRentInfo(context.Context, *GetRentInfoReq) *GetRentInfoRsp
	PBusinessRentEstimate(context.Context, *BusinessRentEstimateReq) *B2BEstimateRsp
	PEstimateCarpoolMultiOrder(context.Context, *PEstimateCarpoolOrderRequest) *PEstimateCarpoolOrderResponse
	PEstimateV3(context.Context, *PEstimateV3DataReq) *PEstimateV3DataResponse
	PGetBusinessTailorService(context.Context, *LuxMultiEstimatePriceRequest) *BusinessTailorServiceResponse
	PetAnyCarEstimate(context.Context, *AnyCarEstimateV4Req) *AnyCarEstimateV4Resp
	MeetingCarEstimate(context.Context, *MeetingCarEstimateReq) *MeetingCarEstimateRsp
}

type MambaServerWrapper struct {
	Server MambaServerNoError
}

func (s *MambaServerWrapper) Ping(ctx context.Context, req *PingReq) (*PingRsp, error) {
	return s.Server.Ping(ctx, req), nil
}

func (s *MambaServerWrapper) PCarpoolEstimatePrice(ctx context.Context, req *CarpoolEstimateRequest) (*CarpoolEstimateResponse, error) {
	return s.Server.PCarpoolEstimatePrice(ctx, req), nil
}

func (s *MambaServerWrapper) IntercityEstimatePrice(ctx context.Context, req *CarpoolEstimateRequest) (*IntercityEstimateResp, error) {
	return s.Server.IntercityEstimatePrice(ctx, req), nil
}

func (s *MambaServerWrapper) MiniBusEstimatePrice(ctx context.Context, req *MiniBusEstimateRequest) (*MiniBusEstimateResp, error) {
	return s.Server.MiniBusEstimatePrice(ctx, req), nil
}

func (s *MambaServerWrapper) AnyCarEstimate(ctx context.Context, req *AnyCarEstimateReq) (*AnyCarEstimateRsp, error) {
	return s.Server.AnyCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) AnyCarEstimateV3(ctx context.Context, req *AnyCarEstimateReq) (*AnyCarEstimateV3Rsp, error) {
	return s.Server.AnyCarEstimateV3(ctx, req), nil
}

func (s *MambaServerWrapper) AnyCarEstimateV4(ctx context.Context, req *AnyCarEstimateV4Req) (*AnyCarEstimateV4Resp, error) {
	return s.Server.AnyCarEstimateV4(ctx, req), nil
}

func (s *MambaServerWrapper) GetAnycarEstimateCache(ctx context.Context, req *AnycarEstimateCacheReq) (*AnycarEstimateCacheResp, error) {
	return s.Server.GetAnycarEstimateCache(ctx, req), nil
}

func (s *MambaServerWrapper) DelAnycarEstimateCache(ctx context.Context, req *AnycarEstimateCacheReq) (*AnycarEstimateDelCacheResp, error) {
	return s.Server.DelAnycarEstimateCache(ctx, req), nil
}

func (s *MambaServerWrapper) CombinedTravelEstimate(ctx context.Context, req *MultiEstimatePriceRequest) (*CombinedTravelEstimateRes, error) {
	return s.Server.CombinedTravelEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PCancelEstimate(ctx context.Context, req *CancelEstimateReq) (*CancelEstimateRsp, error) {
	return s.Server.PCancelEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PBusinessEstimate(ctx context.Context, req *B2BEstimateReq) (*B2BEstimateRsp, error) {
	return s.Server.PBusinessEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PBusinessAnyCarEstimate(ctx context.Context, req *B2BAnyCarEstimateReq) (*B2BAnyCarEstimateRsp, error) {
	return s.Server.PBusinessAnyCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPbdEstimate(ctx context.Context, req *PBDEstimateReq) (*B2BEstimateRsp, error) {
	return s.Server.PPbdEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPBDAnyCarEstimate(ctx context.Context, req *PBDAnyCarEstimateReq) (*B2BAnyCarEstimateRsp, error) {
	return s.Server.PPBDAnyCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PAssistantEstimate(ctx context.Context, req *PBDEstimateReq) (*B2BEstimateRsp, error) {
	return s.Server.PAssistantEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PHelperEstimate(ctx context.Context, req *MultiEstimatePriceRequest) (*HelperEstimateRes, error) {
	return s.Server.PHelperEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PShuttleBusPageEstimate(ctx context.Context, req *ShuttleBusPageEstimateReq) (*ShuttleBusPageEstimateRsp, error) {
	return s.Server.PShuttleBusPageEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PShuttleBusGuideBarEstimate(ctx context.Context, req *ShuttleBusGuideBarEstimateReq) (*ShuttleBusGuideBarEstimateRsp, error) {
	return s.Server.PShuttleBusGuideBarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PBargainEstimate(ctx context.Context, req *BargainEstimateReq) (*BargainEstimateRsp, error) {
	return s.Server.PBargainEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PHomePageCallCarEstimate(ctx context.Context, req *HomePageCallCarEstimateReq) (*HomePageCallCarEstimateRsp, error) {
	return s.Server.PHomePageCallCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PHomePageCallCarEstimateV2(ctx context.Context, req *HomePageCallCarEstimateReq) (*HomePageCallCarEstimateRspV2, error) {
	return s.Server.PHomePageCallCarEstimateV2(ctx, req), nil
}

func (s *MambaServerWrapper) PCarpoolPrice(ctx context.Context, req *CarpoolEstimateRequest) (*CarpoolPriceResponse, error) {
	return s.Server.PCarpoolPrice(ctx, req), nil
}

func (s *MambaServerWrapper) PRecCarpoolEstimate(ctx context.Context, req *RecCarpoolEstimateReq) (*RecCarpoolEstimateResp, error) {
	return s.Server.PRecCarpoolEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PCarpoolInvitationEstimate(ctx context.Context, req *CarpoolInvitationEstimateRequest) (*CarpoolInvitationEstimateResp, error) {
	return s.Server.PCarpoolInvitationEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PGetCouponPrice(ctx context.Context, req *CouponPriceRequest) (*CouponPriceResponse, error) {
	return s.Server.PGetCouponPrice(ctx, req), nil
}

func (s *MambaServerWrapper) PServiceEstimate(ctx context.Context, req *ServiceEstimateRequest) (*ServiceMultiEstimateResponse, error) {
	return s.Server.PServiceEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PSFCEstimate(ctx context.Context, req *SFCEstimateRequest) (*SFCMultiEstimateResponse, error) {
	return s.Server.PSFCEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PSFCModifyEstimate(ctx context.Context, req *SFCModifyEstimateRequest) (*SFCModifyEstimateResponse, error) {
	return s.Server.PSFCModifyEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PSFCSimpleEstimate(ctx context.Context, req *SFCSimpleEstimateReq) (*SFCSimpleEstimateResponse, error) {
	return s.Server.PSFCSimpleEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravel(ctx context.Context, req *CompositeTravelReq) (*CompositeTravelRes, error) {
	return s.Server.PCompositeTravel(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravelV2(ctx context.Context, req *CompositeTravelV2Req) (*CompositeTravelV2Res, error) {
	return s.Server.PCompositeTravelV2(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravelPoi(ctx context.Context, req *CompositeTravelPoiReq) (*CompositeTravelPoiRes, error) {
	return s.Server.PCompositeTravelPoi(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravelV3(ctx context.Context, req *CompositeTravelV3Req) (*CompositeTravelV3Res, error) {
	return s.Server.PCompositeTravelV3(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravelV3Post(ctx context.Context, req *CompositeTravelV3Req) (*CompositeTravelV3Res, error) {
	return s.Server.PCompositeTravelV3Post(ctx, req), nil
}

func (s *MambaServerWrapper) PCompositeTravelOptions(ctx context.Context, req *CompositeTravelOptionsReq) (*CompositeTravelOptionsRes, error) {
	return s.Server.PCompositeTravelOptions(ctx, req), nil
}

func (s *MambaServerWrapper) PRecommendLanding(ctx context.Context, req *CompositeTravelV2Req) (*PRecommendLandingRes, error) {
	return s.Server.PRecommendLanding(ctx, req), nil
}

func (s *MambaServerWrapper) PGetDynamicFeeDescList(ctx context.Context, req *DynamicFeeDescListRequest) (*DynamicFeeDescListResponse, error) {
	return s.Server.PGetDynamicFeeDescList(ctx, req), nil
}

func (s *MambaServerWrapper) PGetDynamicFeeDescListPost(ctx context.Context, req *DynamicFeeDescListRequest) (*DynamicFeeDescListResponse, error) {
	return s.Server.PGetDynamicFeeDescListPost(ctx, req), nil
}

func (s *MambaServerWrapper) PPickOnTimeEstimate(ctx context.Context, req *PickOnTimeEstimateReq) (*PickOnTimeEstimateRsp, error) {
	return s.Server.PPickOnTimeEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PGetGuideInfo(ctx context.Context, req *PGetGuideInfoReq) (*PGetGuideInfoRsp, error) {
	return s.Server.PGetGuideInfo(ctx, req), nil
}

func (s *MambaServerWrapper) PGetGuideInfoPost(ctx context.Context, req *PGetGuideInfoReq) (*PGetGuideInfoRsp, error) {
	return s.Server.PGetGuideInfoPost(ctx, req), nil
}

func (s *MambaServerWrapper) PGetFormRealData(ctx context.Context, req *PGetFormRealDataReq) (*PGetFormRealDataResp, error) {
	return s.Server.PGetFormRealData(ctx, req), nil
}

func (s *MambaServerWrapper) PGetFormRealDataPost(ctx context.Context, req *PGetFormRealDataReq) (*PGetFormRealDataResp, error) {
	return s.Server.PGetFormRealDataPost(ctx, req), nil
}

func (s *MambaServerWrapper) PGetEstimateDataByEidList(ctx context.Context, req *PGetEstimateDataByEidListReq) (*PGetEstimateDataByEidListResp, error) {
	return s.Server.PGetEstimateDataByEidList(ctx, req), nil
}

func (s *MambaServerWrapper) PRouteEstimate(ctx context.Context, req *RouteEstimateRequest) (*RouteEstimateResponse, error) {
	return s.Server.PRouteEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PIntercityMultiEstimateEstimate(ctx context.Context, req *IntercityMultiEstimateRequest) (*IntercityMultiEstimateResponse, error) {
	return s.Server.PIntercityMultiEstimateEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PIntercityEstimateDetail(ctx context.Context, req *IntercityEstimateDetailRequest) (*IntercityEstimateDetailResponse, error) {
	return s.Server.PIntercityEstimateDetail(ctx, req), nil
}

func (s *MambaServerWrapper) POrderBookingPreCancelEstimate(ctx context.Context, req *AnyCarEstimateReq) (*AnyCarEstimateV3Rsp, error) {
	return s.Server.POrderBookingPreCancelEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PInvitedEstimate(ctx context.Context, req *InvitedEstimateReq) (*InvitedEstimateRsp, error) {
	return s.Server.PInvitedEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PMultiPointEstimate(ctx context.Context, req *MultiPointEstimateRequest) (*MultiPointEstimateResponse, error) {
	return s.Server.PMultiPointEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PGetEstimateDataWithoutRender(ctx context.Context, req *MultiEstimatePriceRequest) (*PGetEstimateDataWithoutRenderResp, error) {
	return s.Server.PGetEstimateDataWithoutRender(ctx, req), nil
}

func (s *MambaServerWrapper) PGetEstimateDataWithoutRenderV2(ctx context.Context, req *PGetEstimateDataWithoutRenderReq) (*PGetEstimateDataWithoutRenderResp, error) {
	return s.Server.PGetEstimateDataWithoutRenderV2(ctx, req), nil
}

func (s *MambaServerWrapper) PGetBusinessFormRealData(ctx context.Context, req *GetBusinessFormRealDataRequest) (*GetBusinessFormRealDataResponse, error) {
	return s.Server.PGetBusinessFormRealData(ctx, req), nil
}

func (s *MambaServerWrapper) PBargainRangeEstimate(ctx context.Context, req *BargainRangeEstimateReq) (*BargainRangeEstimateRsp, error) {
	return s.Server.PBargainRangeEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PStationBusInsteadOrderEstimate(ctx context.Context, req *StationBusInsteadOrderEstimateReq) (*StationBusInsteadOrderEstimateRsp, error) {
	return s.Server.PStationBusInsteadOrderEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PGetIntercityBasicFeeLite(ctx context.Context, req *IntercityBasicFeeLiteRequest) (*IntercityBasicFeeLiteResponse, error) {
	return s.Server.PGetIntercityBasicFeeLite(ctx, req), nil
}

func (s *MambaServerWrapper) PDidiMiniEstimate(ctx context.Context, req *PDidiMiniEstimateReq) (*PDidiMiniEstimateResp, error) {
	return s.Server.PDidiMiniEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PEngageCarEstimate(ctx context.Context, req *EngageCarEstimateReq) (*EngageCarEstimateResponse, error) {
	return s.Server.PEngageCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPetsTravelEstimate(ctx context.Context, req *PetsTravelEstimateReq) (*PetsTravelEstimateResponse, error) {
	return s.Server.PPetsTravelEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) POverseaEstimate(ctx context.Context, req *OverseaEstimateReq) (*OverseaEstimateResponse, error) {
	return s.Server.POverseaEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PEstimateByOrder(ctx context.Context, req *PEstimateByOrderRequest) (*PEstimateByOrderResponse, error) {
	return s.Server.PEstimateByOrder(ctx, req), nil
}

func (s *MambaServerWrapper) EstimateOrderWithoutRender(ctx context.Context, req *EstimateOrderWithoutRenderReq) (*EstimateOrderWithoutRenderRsp, error) {
	return s.Server.EstimateOrderWithoutRender(ctx, req), nil
}

func (s *MambaServerWrapper) PPbdStationBusMultiStationPrice(ctx context.Context, req *PbdStationBusMultiStationPriceReq) (*PbdStationBusMultiStationPriceRsp, error) {
	return s.Server.PPbdStationBusMultiStationPrice(ctx, req), nil
}

func (s *MambaServerWrapper) PPbdStationBusOrderEstimate(ctx context.Context, req *PbdStationBusOrderEstimateReq) (*PbdStationBusOrderEstimateRsp, error) {
	return s.Server.PPbdStationBusOrderEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPbdStationBusMultiEstimate(ctx context.Context, req *PbdStationBusMultiEstimateReq) (*PbdStationBusMultiEstimateRsp, error) {
	return s.Server.PPbdStationBusMultiEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPbdStationBusDetailEstimate(ctx context.Context, req *PbdStationBusDetailEstimateReq) (*PbdStationBusDetailEstimateRsp, error) {
	return s.Server.PPbdStationBusDetailEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PCharterHomePage(ctx context.Context, req *CharterHomePageReq) (*CharterHomePageRsp, error) {
	return s.Server.PCharterHomePage(ctx, req), nil
}

func (s *MambaServerWrapper) PCharterMultiEstimate(ctx context.Context, req *CharterMultiEstimateReq) (*CharterMultiEstimateRsp, error) {
	return s.Server.PCharterMultiEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PPassengerBeginCharge(ctx context.Context, req *PPassengerBeginChargeRequest) (*PPassengerBeginChargeResponse, error) {
	return s.Server.PPassengerBeginCharge(ctx, req), nil
}

func (s *MambaServerWrapper) PGetRentInfo(ctx context.Context, req *GetRentInfoReq) (*GetRentInfoRsp, error) {
	return s.Server.PGetRentInfo(ctx, req), nil
}

func (s *MambaServerWrapper) PBusinessRentEstimate(ctx context.Context, req *BusinessRentEstimateReq) (*B2BEstimateRsp, error) {
	return s.Server.PBusinessRentEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) PEstimateCarpoolMultiOrder(ctx context.Context, req *PEstimateCarpoolOrderRequest) (*PEstimateCarpoolOrderResponse, error) {
	return s.Server.PEstimateCarpoolMultiOrder(ctx, req), nil
}

func (s *MambaServerWrapper) PEstimateV3(ctx context.Context, req *PEstimateV3DataReq) (*PEstimateV3DataResponse, error) {
	return s.Server.PEstimateV3(ctx, req), nil
}

func (s *MambaServerWrapper) PGetBusinessTailorService(ctx context.Context, req *LuxMultiEstimatePriceRequest) (*BusinessTailorServiceResponse, error) {
	return s.Server.PGetBusinessTailorService(ctx, req), nil
}

func (s *MambaServerWrapper) PetAnyCarEstimate(ctx context.Context, req *AnyCarEstimateV4Req) (*AnyCarEstimateV4Resp, error) {
	return s.Server.PetAnyCarEstimate(ctx, req), nil
}

func (s *MambaServerWrapper) MeetingCarEstimate(ctx context.Context, req *MeetingCarEstimateReq) (*MeetingCarEstimateRsp, error) {
	return s.Server.MeetingCarEstimate(ctx, req), nil
}

var methodOptions struct {
	requestParserForPing                            func(req *http.Request, pathParams map[string]string) (PingReq, error)
	requestParserForPCarpoolEstimatePrice           func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)
	requestParserForIntercityEstimatePrice          func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)
	requestParserForMiniBusEstimatePrice            func(req *http.Request, pathParams map[string]string) (MiniBusEstimateRequest, error)
	requestParserForAnyCarEstimate                  func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)
	requestParserForAnyCarEstimateV3                func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)
	requestParserForAnyCarEstimateV4                func(req *http.Request, pathParams map[string]string) (AnyCarEstimateV4Req, error)
	requestParserForGetAnycarEstimateCache          func(req *http.Request, pathParams map[string]string) (AnycarEstimateCacheReq, error)
	requestParserForDelAnycarEstimateCache          func(req *http.Request, pathParams map[string]string) (AnycarEstimateCacheReq, error)
	requestParserForCombinedTravelEstimate          func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)
	requestParserForPCancelEstimate                 func(req *http.Request, pathParams map[string]string) (CancelEstimateReq, error)
	requestParserForPBusinessEstimate               func(req *http.Request, pathParams map[string]string) (B2BEstimateReq, error)
	requestParserForPBusinessAnyCarEstimate         func(req *http.Request, pathParams map[string]string) (B2BAnyCarEstimateReq, error)
	requestParserForPPbdEstimate                    func(req *http.Request, pathParams map[string]string) (PBDEstimateReq, error)
	requestParserForPPBDAnyCarEstimate              func(req *http.Request, pathParams map[string]string) (PBDAnyCarEstimateReq, error)
	requestParserForPAssistantEstimate              func(req *http.Request, pathParams map[string]string) (PBDEstimateReq, error)
	requestParserForPHelperEstimate                 func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)
	requestParserForPShuttleBusPageEstimate         func(req *http.Request, pathParams map[string]string) (ShuttleBusPageEstimateReq, error)
	requestParserForPShuttleBusGuideBarEstimate     func(req *http.Request, pathParams map[string]string) (ShuttleBusGuideBarEstimateReq, error)
	requestParserForPBargainEstimate                func(req *http.Request, pathParams map[string]string) (BargainEstimateReq, error)
	requestParserForPHomePageCallCarEstimate        func(req *http.Request, pathParams map[string]string) (HomePageCallCarEstimateReq, error)
	requestParserForPHomePageCallCarEstimateV2      func(req *http.Request, pathParams map[string]string) (HomePageCallCarEstimateReq, error)
	requestParserForPCarpoolPrice                   func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)
	requestParserForPRecCarpoolEstimate             func(req *http.Request, pathParams map[string]string) (RecCarpoolEstimateReq, error)
	requestParserForPCarpoolInvitationEstimate      func(req *http.Request, pathParams map[string]string) (CarpoolInvitationEstimateRequest, error)
	requestParserForPGetCouponPrice                 func(req *http.Request, pathParams map[string]string) (CouponPriceRequest, error)
	requestParserForPServiceEstimate                func(req *http.Request, pathParams map[string]string) (ServiceEstimateRequest, error)
	requestParserForPSFCEstimate                    func(req *http.Request, pathParams map[string]string) (SFCEstimateRequest, error)
	requestParserForPSFCModifyEstimate              func(req *http.Request, pathParams map[string]string) (SFCModifyEstimateRequest, error)
	requestParserForPSFCSimpleEstimate              func(req *http.Request, pathParams map[string]string) (SFCSimpleEstimateReq, error)
	requestParserForPCompositeTravel                func(req *http.Request, pathParams map[string]string) (CompositeTravelReq, error)
	requestParserForPCompositeTravelV2              func(req *http.Request, pathParams map[string]string) (CompositeTravelV2Req, error)
	requestParserForPCompositeTravelPoi             func(req *http.Request, pathParams map[string]string) (CompositeTravelPoiReq, error)
	requestParserForPCompositeTravelV3              func(req *http.Request, pathParams map[string]string) (CompositeTravelV3Req, error)
	requestParserForPCompositeTravelV3Post          func(req *http.Request, pathParams map[string]string) (CompositeTravelV3Req, error)
	requestParserForPCompositeTravelOptions         func(req *http.Request, pathParams map[string]string) (CompositeTravelOptionsReq, error)
	requestParserForPRecommendLanding               func(req *http.Request, pathParams map[string]string) (CompositeTravelV2Req, error)
	requestParserForPGetDynamicFeeDescList          func(req *http.Request, pathParams map[string]string) (DynamicFeeDescListRequest, error)
	requestParserForPGetDynamicFeeDescListPost      func(req *http.Request, pathParams map[string]string) (DynamicFeeDescListRequest, error)
	requestParserForPPickOnTimeEstimate             func(req *http.Request, pathParams map[string]string) (PickOnTimeEstimateReq, error)
	requestParserForPGetGuideInfo                   func(req *http.Request, pathParams map[string]string) (PGetGuideInfoReq, error)
	requestParserForPGetGuideInfoPost               func(req *http.Request, pathParams map[string]string) (PGetGuideInfoReq, error)
	requestParserForPGetFormRealData                func(req *http.Request, pathParams map[string]string) (PGetFormRealDataReq, error)
	requestParserForPGetFormRealDataPost            func(req *http.Request, pathParams map[string]string) (PGetFormRealDataReq, error)
	requestParserForPGetEstimateDataByEidList       func(req *http.Request, pathParams map[string]string) (PGetEstimateDataByEidListReq, error)
	requestParserForPRouteEstimate                  func(req *http.Request, pathParams map[string]string) (RouteEstimateRequest, error)
	requestParserForPIntercityMultiEstimateEstimate func(req *http.Request, pathParams map[string]string) (IntercityMultiEstimateRequest, error)
	requestParserForPIntercityEstimateDetail        func(req *http.Request, pathParams map[string]string) (IntercityEstimateDetailRequest, error)
	requestParserForPOrderBookingPreCancelEstimate  func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)
	requestParserForPInvitedEstimate                func(req *http.Request, pathParams map[string]string) (InvitedEstimateReq, error)
	requestParserForPMultiPointEstimate             func(req *http.Request, pathParams map[string]string) (MultiPointEstimateRequest, error)
	requestParserForPGetEstimateDataWithoutRender   func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)
	requestParserForPGetEstimateDataWithoutRenderV2 func(req *http.Request, pathParams map[string]string) (PGetEstimateDataWithoutRenderReq, error)
	requestParserForPGetBusinessFormRealData        func(req *http.Request, pathParams map[string]string) (GetBusinessFormRealDataRequest, error)
	requestParserForPBargainRangeEstimate           func(req *http.Request, pathParams map[string]string) (BargainRangeEstimateReq, error)
	requestParserForPStationBusInsteadOrderEstimate func(req *http.Request, pathParams map[string]string) (StationBusInsteadOrderEstimateReq, error)
	requestParserForPGetIntercityBasicFeeLite       func(req *http.Request, pathParams map[string]string) (IntercityBasicFeeLiteRequest, error)
	requestParserForPDidiMiniEstimate               func(req *http.Request, pathParams map[string]string) (PDidiMiniEstimateReq, error)
	requestParserForPEngageCarEstimate              func(req *http.Request, pathParams map[string]string) (EngageCarEstimateReq, error)
	requestParserForPPetsTravelEstimate             func(req *http.Request, pathParams map[string]string) (PetsTravelEstimateReq, error)
	requestParserForPOverseaEstimate                func(req *http.Request, pathParams map[string]string) (OverseaEstimateReq, error)
	requestParserForPEstimateByOrder                func(req *http.Request, pathParams map[string]string) (PEstimateByOrderRequest, error)
	requestParserForEstimateOrderWithoutRender      func(req *http.Request, pathParams map[string]string) (EstimateOrderWithoutRenderReq, error)
	requestParserForPPbdStationBusMultiStationPrice func(req *http.Request, pathParams map[string]string) (PbdStationBusMultiStationPriceReq, error)
	requestParserForPPbdStationBusOrderEstimate     func(req *http.Request, pathParams map[string]string) (PbdStationBusOrderEstimateReq, error)
	requestParserForPPbdStationBusMultiEstimate     func(req *http.Request, pathParams map[string]string) (PbdStationBusMultiEstimateReq, error)
	requestParserForPPbdStationBusDetailEstimate    func(req *http.Request, pathParams map[string]string) (PbdStationBusDetailEstimateReq, error)
	requestParserForPCharterHomePage                func(req *http.Request, pathParams map[string]string) (CharterHomePageReq, error)
	requestParserForPCharterMultiEstimate           func(req *http.Request, pathParams map[string]string) (CharterMultiEstimateReq, error)
	requestParserForPPassengerBeginCharge           func(req *http.Request, pathParams map[string]string) (PPassengerBeginChargeRequest, error)
	requestParserForPGetRentInfo                    func(req *http.Request, pathParams map[string]string) (GetRentInfoReq, error)
	requestParserForPBusinessRentEstimate           func(req *http.Request, pathParams map[string]string) (BusinessRentEstimateReq, error)
	requestParserForPEstimateCarpoolMultiOrder      func(req *http.Request, pathParams map[string]string) (PEstimateCarpoolOrderRequest, error)
	requestParserForPEstimateV3                     func(req *http.Request, pathParams map[string]string) (PEstimateV3DataReq, error)
	requestParserForPGetBusinessTailorService       func(req *http.Request, pathParams map[string]string) (LuxMultiEstimatePriceRequest, error)
	requestParserForPetAnyCarEstimate               func(req *http.Request, pathParams map[string]string) (AnyCarEstimateV4Req, error)
	requestParserForMeetingCarEstimate              func(req *http.Request, pathParams map[string]string) (MeetingCarEstimateReq, error)

	parseBodyAndQueryStrForPing                            bool
	parseBodyAndQueryStrForPCarpoolEstimatePrice           bool
	parseBodyAndQueryStrForIntercityEstimatePrice          bool
	parseBodyAndQueryStrForMiniBusEstimatePrice            bool
	parseBodyAndQueryStrForAnyCarEstimate                  bool
	parseBodyAndQueryStrForAnyCarEstimateV3                bool
	parseBodyAndQueryStrForAnyCarEstimateV4                bool
	parseBodyAndQueryStrForGetAnycarEstimateCache          bool
	parseBodyAndQueryStrForDelAnycarEstimateCache          bool
	parseBodyAndQueryStrForCombinedTravelEstimate          bool
	parseBodyAndQueryStrForPCancelEstimate                 bool
	parseBodyAndQueryStrForPBusinessEstimate               bool
	parseBodyAndQueryStrForPBusinessAnyCarEstimate         bool
	parseBodyAndQueryStrForPPbdEstimate                    bool
	parseBodyAndQueryStrForPPBDAnyCarEstimate              bool
	parseBodyAndQueryStrForPAssistantEstimate              bool
	parseBodyAndQueryStrForPHelperEstimate                 bool
	parseBodyAndQueryStrForPShuttleBusPageEstimate         bool
	parseBodyAndQueryStrForPShuttleBusGuideBarEstimate     bool
	parseBodyAndQueryStrForPBargainEstimate                bool
	parseBodyAndQueryStrForPHomePageCallCarEstimate        bool
	parseBodyAndQueryStrForPHomePageCallCarEstimateV2      bool
	parseBodyAndQueryStrForPCarpoolPrice                   bool
	parseBodyAndQueryStrForPRecCarpoolEstimate             bool
	parseBodyAndQueryStrForPCarpoolInvitationEstimate      bool
	parseBodyAndQueryStrForPGetCouponPrice                 bool
	parseBodyAndQueryStrForPServiceEstimate                bool
	parseBodyAndQueryStrForPSFCEstimate                    bool
	parseBodyAndQueryStrForPSFCModifyEstimate              bool
	parseBodyAndQueryStrForPSFCSimpleEstimate              bool
	parseBodyAndQueryStrForPCompositeTravel                bool
	parseBodyAndQueryStrForPCompositeTravelV2              bool
	parseBodyAndQueryStrForPCompositeTravelPoi             bool
	parseBodyAndQueryStrForPCompositeTravelV3              bool
	parseBodyAndQueryStrForPCompositeTravelV3Post          bool
	parseBodyAndQueryStrForPCompositeTravelOptions         bool
	parseBodyAndQueryStrForPRecommendLanding               bool
	parseBodyAndQueryStrForPGetDynamicFeeDescList          bool
	parseBodyAndQueryStrForPGetDynamicFeeDescListPost      bool
	parseBodyAndQueryStrForPPickOnTimeEstimate             bool
	parseBodyAndQueryStrForPGetGuideInfo                   bool
	parseBodyAndQueryStrForPGetGuideInfoPost               bool
	parseBodyAndQueryStrForPGetFormRealData                bool
	parseBodyAndQueryStrForPGetFormRealDataPost            bool
	parseBodyAndQueryStrForPGetEstimateDataByEidList       bool
	parseBodyAndQueryStrForPRouteEstimate                  bool
	parseBodyAndQueryStrForPIntercityMultiEstimateEstimate bool
	parseBodyAndQueryStrForPIntercityEstimateDetail        bool
	parseBodyAndQueryStrForPOrderBookingPreCancelEstimate  bool
	parseBodyAndQueryStrForPInvitedEstimate                bool
	parseBodyAndQueryStrForPMultiPointEstimate             bool
	parseBodyAndQueryStrForPGetEstimateDataWithoutRender   bool
	parseBodyAndQueryStrForPGetEstimateDataWithoutRenderV2 bool
	parseBodyAndQueryStrForPGetBusinessFormRealData        bool
	parseBodyAndQueryStrForPBargainRangeEstimate           bool
	parseBodyAndQueryStrForPStationBusInsteadOrderEstimate bool
	parseBodyAndQueryStrForPGetIntercityBasicFeeLite       bool
	parseBodyAndQueryStrForPDidiMiniEstimate               bool
	parseBodyAndQueryStrForPEngageCarEstimate              bool
	parseBodyAndQueryStrForPPetsTravelEstimate             bool
	parseBodyAndQueryStrForPOverseaEstimate                bool
	parseBodyAndQueryStrForPEstimateByOrder                bool
	parseBodyAndQueryStrForEstimateOrderWithoutRender      bool
	parseBodyAndQueryStrForPPbdStationBusMultiStationPrice bool
	parseBodyAndQueryStrForPPbdStationBusOrderEstimate     bool
	parseBodyAndQueryStrForPPbdStationBusMultiEstimate     bool
	parseBodyAndQueryStrForPPbdStationBusDetailEstimate    bool
	parseBodyAndQueryStrForPCharterHomePage                bool
	parseBodyAndQueryStrForPCharterMultiEstimate           bool
	parseBodyAndQueryStrForPPassengerBeginCharge           bool
	parseBodyAndQueryStrForPGetRentInfo                    bool
	parseBodyAndQueryStrForPBusinessRentEstimate           bool
	parseBodyAndQueryStrForPEstimateCarpoolMultiOrder      bool
	parseBodyAndQueryStrForPEstimateV3                     bool
	parseBodyAndQueryStrForPGetBusinessTailorService       bool
	parseBodyAndQueryStrForPetAnyCarEstimate               bool
	parseBodyAndQueryStrForMeetingCarEstimate              bool
}

func SetRequestParserForPing(p func(req *http.Request, pathParams map[string]string) (PingReq, error)) {
	methodOptions.requestParserForPing = p
}

func EnableBothParseBodyAndQueryStrForPing(enable bool) {
	methodOptions.parseBodyAndQueryStrForPing = enable
}

func SetRequestParserForPCarpoolEstimatePrice(p func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)) {
	methodOptions.requestParserForPCarpoolEstimatePrice = p
}

func EnableBothParseBodyAndQueryStrForPCarpoolEstimatePrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCarpoolEstimatePrice = enable
}

func SetRequestParserForIntercityEstimatePrice(p func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)) {
	methodOptions.requestParserForIntercityEstimatePrice = p
}

func EnableBothParseBodyAndQueryStrForIntercityEstimatePrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForIntercityEstimatePrice = enable
}

func SetRequestParserForMiniBusEstimatePrice(p func(req *http.Request, pathParams map[string]string) (MiniBusEstimateRequest, error)) {
	methodOptions.requestParserForMiniBusEstimatePrice = p
}

func EnableBothParseBodyAndQueryStrForMiniBusEstimatePrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForMiniBusEstimatePrice = enable
}

func SetRequestParserForAnyCarEstimate(p func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)) {
	methodOptions.requestParserForAnyCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForAnyCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForAnyCarEstimate = enable
}

func SetRequestParserForAnyCarEstimateV3(p func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)) {
	methodOptions.requestParserForAnyCarEstimateV3 = p
}

func EnableBothParseBodyAndQueryStrForAnyCarEstimateV3(enable bool) {
	methodOptions.parseBodyAndQueryStrForAnyCarEstimateV3 = enable
}

func SetRequestParserForAnyCarEstimateV4(p func(req *http.Request, pathParams map[string]string) (AnyCarEstimateV4Req, error)) {
	methodOptions.requestParserForAnyCarEstimateV4 = p
}

func EnableBothParseBodyAndQueryStrForAnyCarEstimateV4(enable bool) {
	methodOptions.parseBodyAndQueryStrForAnyCarEstimateV4 = enable
}

func SetRequestParserForGetAnycarEstimateCache(p func(req *http.Request, pathParams map[string]string) (AnycarEstimateCacheReq, error)) {
	methodOptions.requestParserForGetAnycarEstimateCache = p
}

func EnableBothParseBodyAndQueryStrForGetAnycarEstimateCache(enable bool) {
	methodOptions.parseBodyAndQueryStrForGetAnycarEstimateCache = enable
}

func SetRequestParserForDelAnycarEstimateCache(p func(req *http.Request, pathParams map[string]string) (AnycarEstimateCacheReq, error)) {
	methodOptions.requestParserForDelAnycarEstimateCache = p
}

func EnableBothParseBodyAndQueryStrForDelAnycarEstimateCache(enable bool) {
	methodOptions.parseBodyAndQueryStrForDelAnycarEstimateCache = enable
}

func SetRequestParserForCombinedTravelEstimate(p func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)) {
	methodOptions.requestParserForCombinedTravelEstimate = p
}

func EnableBothParseBodyAndQueryStrForCombinedTravelEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForCombinedTravelEstimate = enable
}

func SetRequestParserForPCancelEstimate(p func(req *http.Request, pathParams map[string]string) (CancelEstimateReq, error)) {
	methodOptions.requestParserForPCancelEstimate = p
}

func EnableBothParseBodyAndQueryStrForPCancelEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCancelEstimate = enable
}

func SetRequestParserForPBusinessEstimate(p func(req *http.Request, pathParams map[string]string) (B2BEstimateReq, error)) {
	methodOptions.requestParserForPBusinessEstimate = p
}

func EnableBothParseBodyAndQueryStrForPBusinessEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPBusinessEstimate = enable
}

func SetRequestParserForPBusinessAnyCarEstimate(p func(req *http.Request, pathParams map[string]string) (B2BAnyCarEstimateReq, error)) {
	methodOptions.requestParserForPBusinessAnyCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPBusinessAnyCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPBusinessAnyCarEstimate = enable
}

func SetRequestParserForPPbdEstimate(p func(req *http.Request, pathParams map[string]string) (PBDEstimateReq, error)) {
	methodOptions.requestParserForPPbdEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPbdEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPbdEstimate = enable
}

func SetRequestParserForPPBDAnyCarEstimate(p func(req *http.Request, pathParams map[string]string) (PBDAnyCarEstimateReq, error)) {
	methodOptions.requestParserForPPBDAnyCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPBDAnyCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPBDAnyCarEstimate = enable
}

func SetRequestParserForPAssistantEstimate(p func(req *http.Request, pathParams map[string]string) (PBDEstimateReq, error)) {
	methodOptions.requestParserForPAssistantEstimate = p
}

func EnableBothParseBodyAndQueryStrForPAssistantEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPAssistantEstimate = enable
}

func SetRequestParserForPHelperEstimate(p func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)) {
	methodOptions.requestParserForPHelperEstimate = p
}

func EnableBothParseBodyAndQueryStrForPHelperEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPHelperEstimate = enable
}

func SetRequestParserForPShuttleBusPageEstimate(p func(req *http.Request, pathParams map[string]string) (ShuttleBusPageEstimateReq, error)) {
	methodOptions.requestParserForPShuttleBusPageEstimate = p
}

func EnableBothParseBodyAndQueryStrForPShuttleBusPageEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPShuttleBusPageEstimate = enable
}

func SetRequestParserForPShuttleBusGuideBarEstimate(p func(req *http.Request, pathParams map[string]string) (ShuttleBusGuideBarEstimateReq, error)) {
	methodOptions.requestParserForPShuttleBusGuideBarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPShuttleBusGuideBarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPShuttleBusGuideBarEstimate = enable
}

func SetRequestParserForPBargainEstimate(p func(req *http.Request, pathParams map[string]string) (BargainEstimateReq, error)) {
	methodOptions.requestParserForPBargainEstimate = p
}

func EnableBothParseBodyAndQueryStrForPBargainEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPBargainEstimate = enable
}

func SetRequestParserForPHomePageCallCarEstimate(p func(req *http.Request, pathParams map[string]string) (HomePageCallCarEstimateReq, error)) {
	methodOptions.requestParserForPHomePageCallCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPHomePageCallCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPHomePageCallCarEstimate = enable
}

func SetRequestParserForPHomePageCallCarEstimateV2(p func(req *http.Request, pathParams map[string]string) (HomePageCallCarEstimateReq, error)) {
	methodOptions.requestParserForPHomePageCallCarEstimateV2 = p
}

func EnableBothParseBodyAndQueryStrForPHomePageCallCarEstimateV2(enable bool) {
	methodOptions.parseBodyAndQueryStrForPHomePageCallCarEstimateV2 = enable
}

func SetRequestParserForPCarpoolPrice(p func(req *http.Request, pathParams map[string]string) (CarpoolEstimateRequest, error)) {
	methodOptions.requestParserForPCarpoolPrice = p
}

func EnableBothParseBodyAndQueryStrForPCarpoolPrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCarpoolPrice = enable
}

func SetRequestParserForPRecCarpoolEstimate(p func(req *http.Request, pathParams map[string]string) (RecCarpoolEstimateReq, error)) {
	methodOptions.requestParserForPRecCarpoolEstimate = p
}

func EnableBothParseBodyAndQueryStrForPRecCarpoolEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPRecCarpoolEstimate = enable
}

func SetRequestParserForPCarpoolInvitationEstimate(p func(req *http.Request, pathParams map[string]string) (CarpoolInvitationEstimateRequest, error)) {
	methodOptions.requestParserForPCarpoolInvitationEstimate = p
}

func EnableBothParseBodyAndQueryStrForPCarpoolInvitationEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCarpoolInvitationEstimate = enable
}

func SetRequestParserForPGetCouponPrice(p func(req *http.Request, pathParams map[string]string) (CouponPriceRequest, error)) {
	methodOptions.requestParserForPGetCouponPrice = p
}

func EnableBothParseBodyAndQueryStrForPGetCouponPrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetCouponPrice = enable
}

func SetRequestParserForPServiceEstimate(p func(req *http.Request, pathParams map[string]string) (ServiceEstimateRequest, error)) {
	methodOptions.requestParserForPServiceEstimate = p
}

func EnableBothParseBodyAndQueryStrForPServiceEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPServiceEstimate = enable
}

func SetRequestParserForPSFCEstimate(p func(req *http.Request, pathParams map[string]string) (SFCEstimateRequest, error)) {
	methodOptions.requestParserForPSFCEstimate = p
}

func EnableBothParseBodyAndQueryStrForPSFCEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPSFCEstimate = enable
}

func SetRequestParserForPSFCModifyEstimate(p func(req *http.Request, pathParams map[string]string) (SFCModifyEstimateRequest, error)) {
	methodOptions.requestParserForPSFCModifyEstimate = p
}

func EnableBothParseBodyAndQueryStrForPSFCModifyEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPSFCModifyEstimate = enable
}

func SetRequestParserForPSFCSimpleEstimate(p func(req *http.Request, pathParams map[string]string) (SFCSimpleEstimateReq, error)) {
	methodOptions.requestParserForPSFCSimpleEstimate = p
}

func EnableBothParseBodyAndQueryStrForPSFCSimpleEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPSFCSimpleEstimate = enable
}

func SetRequestParserForPCompositeTravel(p func(req *http.Request, pathParams map[string]string) (CompositeTravelReq, error)) {
	methodOptions.requestParserForPCompositeTravel = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravel(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravel = enable
}

func SetRequestParserForPCompositeTravelV2(p func(req *http.Request, pathParams map[string]string) (CompositeTravelV2Req, error)) {
	methodOptions.requestParserForPCompositeTravelV2 = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravelV2(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravelV2 = enable
}

func SetRequestParserForPCompositeTravelPoi(p func(req *http.Request, pathParams map[string]string) (CompositeTravelPoiReq, error)) {
	methodOptions.requestParserForPCompositeTravelPoi = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravelPoi(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravelPoi = enable
}

func SetRequestParserForPCompositeTravelV3(p func(req *http.Request, pathParams map[string]string) (CompositeTravelV3Req, error)) {
	methodOptions.requestParserForPCompositeTravelV3 = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravelV3(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravelV3 = enable
}

func SetRequestParserForPCompositeTravelV3Post(p func(req *http.Request, pathParams map[string]string) (CompositeTravelV3Req, error)) {
	methodOptions.requestParserForPCompositeTravelV3Post = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravelV3Post(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravelV3Post = enable
}

func SetRequestParserForPCompositeTravelOptions(p func(req *http.Request, pathParams map[string]string) (CompositeTravelOptionsReq, error)) {
	methodOptions.requestParserForPCompositeTravelOptions = p
}

func EnableBothParseBodyAndQueryStrForPCompositeTravelOptions(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCompositeTravelOptions = enable
}

func SetRequestParserForPRecommendLanding(p func(req *http.Request, pathParams map[string]string) (CompositeTravelV2Req, error)) {
	methodOptions.requestParserForPRecommendLanding = p
}

func EnableBothParseBodyAndQueryStrForPRecommendLanding(enable bool) {
	methodOptions.parseBodyAndQueryStrForPRecommendLanding = enable
}

func SetRequestParserForPGetDynamicFeeDescList(p func(req *http.Request, pathParams map[string]string) (DynamicFeeDescListRequest, error)) {
	methodOptions.requestParserForPGetDynamicFeeDescList = p
}

func EnableBothParseBodyAndQueryStrForPGetDynamicFeeDescList(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetDynamicFeeDescList = enable
}

func SetRequestParserForPGetDynamicFeeDescListPost(p func(req *http.Request, pathParams map[string]string) (DynamicFeeDescListRequest, error)) {
	methodOptions.requestParserForPGetDynamicFeeDescListPost = p
}

func EnableBothParseBodyAndQueryStrForPGetDynamicFeeDescListPost(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetDynamicFeeDescListPost = enable
}

func SetRequestParserForPPickOnTimeEstimate(p func(req *http.Request, pathParams map[string]string) (PickOnTimeEstimateReq, error)) {
	methodOptions.requestParserForPPickOnTimeEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPickOnTimeEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPickOnTimeEstimate = enable
}

func SetRequestParserForPGetGuideInfo(p func(req *http.Request, pathParams map[string]string) (PGetGuideInfoReq, error)) {
	methodOptions.requestParserForPGetGuideInfo = p
}

func EnableBothParseBodyAndQueryStrForPGetGuideInfo(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetGuideInfo = enable
}

func SetRequestParserForPGetGuideInfoPost(p func(req *http.Request, pathParams map[string]string) (PGetGuideInfoReq, error)) {
	methodOptions.requestParserForPGetGuideInfoPost = p
}

func EnableBothParseBodyAndQueryStrForPGetGuideInfoPost(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetGuideInfoPost = enable
}

func SetRequestParserForPGetFormRealData(p func(req *http.Request, pathParams map[string]string) (PGetFormRealDataReq, error)) {
	methodOptions.requestParserForPGetFormRealData = p
}

func EnableBothParseBodyAndQueryStrForPGetFormRealData(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetFormRealData = enable
}

func SetRequestParserForPGetFormRealDataPost(p func(req *http.Request, pathParams map[string]string) (PGetFormRealDataReq, error)) {
	methodOptions.requestParserForPGetFormRealDataPost = p
}

func EnableBothParseBodyAndQueryStrForPGetFormRealDataPost(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetFormRealDataPost = enable
}

func SetRequestParserForPGetEstimateDataByEidList(p func(req *http.Request, pathParams map[string]string) (PGetEstimateDataByEidListReq, error)) {
	methodOptions.requestParserForPGetEstimateDataByEidList = p
}

func EnableBothParseBodyAndQueryStrForPGetEstimateDataByEidList(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetEstimateDataByEidList = enable
}

func SetRequestParserForPRouteEstimate(p func(req *http.Request, pathParams map[string]string) (RouteEstimateRequest, error)) {
	methodOptions.requestParserForPRouteEstimate = p
}

func EnableBothParseBodyAndQueryStrForPRouteEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPRouteEstimate = enable
}

func SetRequestParserForPIntercityMultiEstimateEstimate(p func(req *http.Request, pathParams map[string]string) (IntercityMultiEstimateRequest, error)) {
	methodOptions.requestParserForPIntercityMultiEstimateEstimate = p
}

func EnableBothParseBodyAndQueryStrForPIntercityMultiEstimateEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPIntercityMultiEstimateEstimate = enable
}

func SetRequestParserForPIntercityEstimateDetail(p func(req *http.Request, pathParams map[string]string) (IntercityEstimateDetailRequest, error)) {
	methodOptions.requestParserForPIntercityEstimateDetail = p
}

func EnableBothParseBodyAndQueryStrForPIntercityEstimateDetail(enable bool) {
	methodOptions.parseBodyAndQueryStrForPIntercityEstimateDetail = enable
}

func SetRequestParserForPOrderBookingPreCancelEstimate(p func(req *http.Request, pathParams map[string]string) (AnyCarEstimateReq, error)) {
	methodOptions.requestParserForPOrderBookingPreCancelEstimate = p
}

func EnableBothParseBodyAndQueryStrForPOrderBookingPreCancelEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPOrderBookingPreCancelEstimate = enable
}

func SetRequestParserForPInvitedEstimate(p func(req *http.Request, pathParams map[string]string) (InvitedEstimateReq, error)) {
	methodOptions.requestParserForPInvitedEstimate = p
}

func EnableBothParseBodyAndQueryStrForPInvitedEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPInvitedEstimate = enable
}

func SetRequestParserForPMultiPointEstimate(p func(req *http.Request, pathParams map[string]string) (MultiPointEstimateRequest, error)) {
	methodOptions.requestParserForPMultiPointEstimate = p
}

func EnableBothParseBodyAndQueryStrForPMultiPointEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPMultiPointEstimate = enable
}

func SetRequestParserForPGetEstimateDataWithoutRender(p func(req *http.Request, pathParams map[string]string) (MultiEstimatePriceRequest, error)) {
	methodOptions.requestParserForPGetEstimateDataWithoutRender = p
}

func EnableBothParseBodyAndQueryStrForPGetEstimateDataWithoutRender(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetEstimateDataWithoutRender = enable
}

func SetRequestParserForPGetEstimateDataWithoutRenderV2(p func(req *http.Request, pathParams map[string]string) (PGetEstimateDataWithoutRenderReq, error)) {
	methodOptions.requestParserForPGetEstimateDataWithoutRenderV2 = p
}

func EnableBothParseBodyAndQueryStrForPGetEstimateDataWithoutRenderV2(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetEstimateDataWithoutRenderV2 = enable
}

func SetRequestParserForPGetBusinessFormRealData(p func(req *http.Request, pathParams map[string]string) (GetBusinessFormRealDataRequest, error)) {
	methodOptions.requestParserForPGetBusinessFormRealData = p
}

func EnableBothParseBodyAndQueryStrForPGetBusinessFormRealData(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetBusinessFormRealData = enable
}

func SetRequestParserForPBargainRangeEstimate(p func(req *http.Request, pathParams map[string]string) (BargainRangeEstimateReq, error)) {
	methodOptions.requestParserForPBargainRangeEstimate = p
}

func EnableBothParseBodyAndQueryStrForPBargainRangeEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPBargainRangeEstimate = enable
}

func SetRequestParserForPStationBusInsteadOrderEstimate(p func(req *http.Request, pathParams map[string]string) (StationBusInsteadOrderEstimateReq, error)) {
	methodOptions.requestParserForPStationBusInsteadOrderEstimate = p
}

func EnableBothParseBodyAndQueryStrForPStationBusInsteadOrderEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPStationBusInsteadOrderEstimate = enable
}

func SetRequestParserForPGetIntercityBasicFeeLite(p func(req *http.Request, pathParams map[string]string) (IntercityBasicFeeLiteRequest, error)) {
	methodOptions.requestParserForPGetIntercityBasicFeeLite = p
}

func EnableBothParseBodyAndQueryStrForPGetIntercityBasicFeeLite(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetIntercityBasicFeeLite = enable
}

func SetRequestParserForPDidiMiniEstimate(p func(req *http.Request, pathParams map[string]string) (PDidiMiniEstimateReq, error)) {
	methodOptions.requestParserForPDidiMiniEstimate = p
}

func EnableBothParseBodyAndQueryStrForPDidiMiniEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPDidiMiniEstimate = enable
}

func SetRequestParserForPEngageCarEstimate(p func(req *http.Request, pathParams map[string]string) (EngageCarEstimateReq, error)) {
	methodOptions.requestParserForPEngageCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPEngageCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPEngageCarEstimate = enable
}

func SetRequestParserForPPetsTravelEstimate(p func(req *http.Request, pathParams map[string]string) (PetsTravelEstimateReq, error)) {
	methodOptions.requestParserForPPetsTravelEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPetsTravelEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPetsTravelEstimate = enable
}

func SetRequestParserForPOverseaEstimate(p func(req *http.Request, pathParams map[string]string) (OverseaEstimateReq, error)) {
	methodOptions.requestParserForPOverseaEstimate = p
}

func EnableBothParseBodyAndQueryStrForPOverseaEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPOverseaEstimate = enable
}

func SetRequestParserForPEstimateByOrder(p func(req *http.Request, pathParams map[string]string) (PEstimateByOrderRequest, error)) {
	methodOptions.requestParserForPEstimateByOrder = p
}

func EnableBothParseBodyAndQueryStrForPEstimateByOrder(enable bool) {
	methodOptions.parseBodyAndQueryStrForPEstimateByOrder = enable
}

func SetRequestParserForEstimateOrderWithoutRender(p func(req *http.Request, pathParams map[string]string) (EstimateOrderWithoutRenderReq, error)) {
	methodOptions.requestParserForEstimateOrderWithoutRender = p
}

func EnableBothParseBodyAndQueryStrForEstimateOrderWithoutRender(enable bool) {
	methodOptions.parseBodyAndQueryStrForEstimateOrderWithoutRender = enable
}

func SetRequestParserForPPbdStationBusMultiStationPrice(p func(req *http.Request, pathParams map[string]string) (PbdStationBusMultiStationPriceReq, error)) {
	methodOptions.requestParserForPPbdStationBusMultiStationPrice = p
}

func EnableBothParseBodyAndQueryStrForPPbdStationBusMultiStationPrice(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPbdStationBusMultiStationPrice = enable
}

func SetRequestParserForPPbdStationBusOrderEstimate(p func(req *http.Request, pathParams map[string]string) (PbdStationBusOrderEstimateReq, error)) {
	methodOptions.requestParserForPPbdStationBusOrderEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPbdStationBusOrderEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPbdStationBusOrderEstimate = enable
}

func SetRequestParserForPPbdStationBusMultiEstimate(p func(req *http.Request, pathParams map[string]string) (PbdStationBusMultiEstimateReq, error)) {
	methodOptions.requestParserForPPbdStationBusMultiEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPbdStationBusMultiEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPbdStationBusMultiEstimate = enable
}

func SetRequestParserForPPbdStationBusDetailEstimate(p func(req *http.Request, pathParams map[string]string) (PbdStationBusDetailEstimateReq, error)) {
	methodOptions.requestParserForPPbdStationBusDetailEstimate = p
}

func EnableBothParseBodyAndQueryStrForPPbdStationBusDetailEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPbdStationBusDetailEstimate = enable
}

func SetRequestParserForPCharterHomePage(p func(req *http.Request, pathParams map[string]string) (CharterHomePageReq, error)) {
	methodOptions.requestParserForPCharterHomePage = p
}

func EnableBothParseBodyAndQueryStrForPCharterHomePage(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCharterHomePage = enable
}

func SetRequestParserForPCharterMultiEstimate(p func(req *http.Request, pathParams map[string]string) (CharterMultiEstimateReq, error)) {
	methodOptions.requestParserForPCharterMultiEstimate = p
}

func EnableBothParseBodyAndQueryStrForPCharterMultiEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPCharterMultiEstimate = enable
}

func SetRequestParserForPPassengerBeginCharge(p func(req *http.Request, pathParams map[string]string) (PPassengerBeginChargeRequest, error)) {
	methodOptions.requestParserForPPassengerBeginCharge = p
}

func EnableBothParseBodyAndQueryStrForPPassengerBeginCharge(enable bool) {
	methodOptions.parseBodyAndQueryStrForPPassengerBeginCharge = enable
}

func SetRequestParserForPGetRentInfo(p func(req *http.Request, pathParams map[string]string) (GetRentInfoReq, error)) {
	methodOptions.requestParserForPGetRentInfo = p
}

func EnableBothParseBodyAndQueryStrForPGetRentInfo(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetRentInfo = enable
}

func SetRequestParserForPBusinessRentEstimate(p func(req *http.Request, pathParams map[string]string) (BusinessRentEstimateReq, error)) {
	methodOptions.requestParserForPBusinessRentEstimate = p
}

func EnableBothParseBodyAndQueryStrForPBusinessRentEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPBusinessRentEstimate = enable
}

func SetRequestParserForPEstimateCarpoolMultiOrder(p func(req *http.Request, pathParams map[string]string) (PEstimateCarpoolOrderRequest, error)) {
	methodOptions.requestParserForPEstimateCarpoolMultiOrder = p
}

func EnableBothParseBodyAndQueryStrForPEstimateCarpoolMultiOrder(enable bool) {
	methodOptions.parseBodyAndQueryStrForPEstimateCarpoolMultiOrder = enable
}

func SetRequestParserForPEstimateV3(p func(req *http.Request, pathParams map[string]string) (PEstimateV3DataReq, error)) {
	methodOptions.requestParserForPEstimateV3 = p
}

func EnableBothParseBodyAndQueryStrForPEstimateV3(enable bool) {
	methodOptions.parseBodyAndQueryStrForPEstimateV3 = enable
}

func SetRequestParserForPGetBusinessTailorService(p func(req *http.Request, pathParams map[string]string) (LuxMultiEstimatePriceRequest, error)) {
	methodOptions.requestParserForPGetBusinessTailorService = p
}

func EnableBothParseBodyAndQueryStrForPGetBusinessTailorService(enable bool) {
	methodOptions.parseBodyAndQueryStrForPGetBusinessTailorService = enable
}

func SetRequestParserForPetAnyCarEstimate(p func(req *http.Request, pathParams map[string]string) (AnyCarEstimateV4Req, error)) {
	methodOptions.requestParserForPetAnyCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForPetAnyCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForPetAnyCarEstimate = enable
}

func SetRequestParserForMeetingCarEstimate(p func(req *http.Request, pathParams map[string]string) (MeetingCarEstimateReq, error)) {
	methodOptions.requestParserForMeetingCarEstimate = p
}

func EnableBothParseBodyAndQueryStrForMeetingCarEstimate(enable bool) {
	methodOptions.parseBodyAndQueryStrForMeetingCarEstimate = enable
}

var marshalOptions struct {
	responseMarshalerForPing                            func(ctx context.Context, resp *PingRsp) (ret interface{})
	responseMarshalerForPCarpoolEstimatePrice           func(ctx context.Context, resp *CarpoolEstimateResponse) (ret interface{})
	responseMarshalerForIntercityEstimatePrice          func(ctx context.Context, resp *IntercityEstimateResp) (ret interface{})
	responseMarshalerForMiniBusEstimatePrice            func(ctx context.Context, resp *MiniBusEstimateResp) (ret interface{})
	responseMarshalerForAnyCarEstimate                  func(ctx context.Context, resp *AnyCarEstimateRsp) (ret interface{})
	responseMarshalerForAnyCarEstimateV3                func(ctx context.Context, resp *AnyCarEstimateV3Rsp) (ret interface{})
	responseMarshalerForAnyCarEstimateV4                func(ctx context.Context, resp *AnyCarEstimateV4Resp) (ret interface{})
	responseMarshalerForGetAnycarEstimateCache          func(ctx context.Context, resp *AnycarEstimateCacheResp) (ret interface{})
	responseMarshalerForDelAnycarEstimateCache          func(ctx context.Context, resp *AnycarEstimateDelCacheResp) (ret interface{})
	responseMarshalerForCombinedTravelEstimate          func(ctx context.Context, resp *CombinedTravelEstimateRes) (ret interface{})
	responseMarshalerForPCancelEstimate                 func(ctx context.Context, resp *CancelEstimateRsp) (ret interface{})
	responseMarshalerForPBusinessEstimate               func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})
	responseMarshalerForPBusinessAnyCarEstimate         func(ctx context.Context, resp *B2BAnyCarEstimateRsp) (ret interface{})
	responseMarshalerForPPbdEstimate                    func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})
	responseMarshalerForPPBDAnyCarEstimate              func(ctx context.Context, resp *B2BAnyCarEstimateRsp) (ret interface{})
	responseMarshalerForPAssistantEstimate              func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})
	responseMarshalerForPHelperEstimate                 func(ctx context.Context, resp *HelperEstimateRes) (ret interface{})
	responseMarshalerForPShuttleBusPageEstimate         func(ctx context.Context, resp *ShuttleBusPageEstimateRsp) (ret interface{})
	responseMarshalerForPShuttleBusGuideBarEstimate     func(ctx context.Context, resp *ShuttleBusGuideBarEstimateRsp) (ret interface{})
	responseMarshalerForPBargainEstimate                func(ctx context.Context, resp *BargainEstimateRsp) (ret interface{})
	responseMarshalerForPHomePageCallCarEstimate        func(ctx context.Context, resp *HomePageCallCarEstimateRsp) (ret interface{})
	responseMarshalerForPHomePageCallCarEstimateV2      func(ctx context.Context, resp *HomePageCallCarEstimateRspV2) (ret interface{})
	responseMarshalerForPCarpoolPrice                   func(ctx context.Context, resp *CarpoolPriceResponse) (ret interface{})
	responseMarshalerForPRecCarpoolEstimate             func(ctx context.Context, resp *RecCarpoolEstimateResp) (ret interface{})
	responseMarshalerForPCarpoolInvitationEstimate      func(ctx context.Context, resp *CarpoolInvitationEstimateResp) (ret interface{})
	responseMarshalerForPGetCouponPrice                 func(ctx context.Context, resp *CouponPriceResponse) (ret interface{})
	responseMarshalerForPServiceEstimate                func(ctx context.Context, resp *ServiceMultiEstimateResponse) (ret interface{})
	responseMarshalerForPSFCEstimate                    func(ctx context.Context, resp *SFCMultiEstimateResponse) (ret interface{})
	responseMarshalerForPSFCModifyEstimate              func(ctx context.Context, resp *SFCModifyEstimateResponse) (ret interface{})
	responseMarshalerForPSFCSimpleEstimate              func(ctx context.Context, resp *SFCSimpleEstimateResponse) (ret interface{})
	responseMarshalerForPCompositeTravel                func(ctx context.Context, resp *CompositeTravelRes) (ret interface{})
	responseMarshalerForPCompositeTravelV2              func(ctx context.Context, resp *CompositeTravelV2Res) (ret interface{})
	responseMarshalerForPCompositeTravelPoi             func(ctx context.Context, resp *CompositeTravelPoiRes) (ret interface{})
	responseMarshalerForPCompositeTravelV3              func(ctx context.Context, resp *CompositeTravelV3Res) (ret interface{})
	responseMarshalerForPCompositeTravelV3Post          func(ctx context.Context, resp *CompositeTravelV3Res) (ret interface{})
	responseMarshalerForPCompositeTravelOptions         func(ctx context.Context, resp *CompositeTravelOptionsRes) (ret interface{})
	responseMarshalerForPRecommendLanding               func(ctx context.Context, resp *PRecommendLandingRes) (ret interface{})
	responseMarshalerForPGetDynamicFeeDescList          func(ctx context.Context, resp *DynamicFeeDescListResponse) (ret interface{})
	responseMarshalerForPGetDynamicFeeDescListPost      func(ctx context.Context, resp *DynamicFeeDescListResponse) (ret interface{})
	responseMarshalerForPPickOnTimeEstimate             func(ctx context.Context, resp *PickOnTimeEstimateRsp) (ret interface{})
	responseMarshalerForPGetGuideInfo                   func(ctx context.Context, resp *PGetGuideInfoRsp) (ret interface{})
	responseMarshalerForPGetGuideInfoPost               func(ctx context.Context, resp *PGetGuideInfoRsp) (ret interface{})
	responseMarshalerForPGetFormRealData                func(ctx context.Context, resp *PGetFormRealDataResp) (ret interface{})
	responseMarshalerForPGetFormRealDataPost            func(ctx context.Context, resp *PGetFormRealDataResp) (ret interface{})
	responseMarshalerForPGetEstimateDataByEidList       func(ctx context.Context, resp *PGetEstimateDataByEidListResp) (ret interface{})
	responseMarshalerForPRouteEstimate                  func(ctx context.Context, resp *RouteEstimateResponse) (ret interface{})
	responseMarshalerForPIntercityMultiEstimateEstimate func(ctx context.Context, resp *IntercityMultiEstimateResponse) (ret interface{})
	responseMarshalerForPIntercityEstimateDetail        func(ctx context.Context, resp *IntercityEstimateDetailResponse) (ret interface{})
	responseMarshalerForPOrderBookingPreCancelEstimate  func(ctx context.Context, resp *AnyCarEstimateV3Rsp) (ret interface{})
	responseMarshalerForPInvitedEstimate                func(ctx context.Context, resp *InvitedEstimateRsp) (ret interface{})
	responseMarshalerForPMultiPointEstimate             func(ctx context.Context, resp *MultiPointEstimateResponse) (ret interface{})
	responseMarshalerForPGetEstimateDataWithoutRender   func(ctx context.Context, resp *PGetEstimateDataWithoutRenderResp) (ret interface{})
	responseMarshalerForPGetEstimateDataWithoutRenderV2 func(ctx context.Context, resp *PGetEstimateDataWithoutRenderResp) (ret interface{})
	responseMarshalerForPGetBusinessFormRealData        func(ctx context.Context, resp *GetBusinessFormRealDataResponse) (ret interface{})
	responseMarshalerForPBargainRangeEstimate           func(ctx context.Context, resp *BargainRangeEstimateRsp) (ret interface{})
	responseMarshalerForPStationBusInsteadOrderEstimate func(ctx context.Context, resp *StationBusInsteadOrderEstimateRsp) (ret interface{})
	responseMarshalerForPGetIntercityBasicFeeLite       func(ctx context.Context, resp *IntercityBasicFeeLiteResponse) (ret interface{})
	responseMarshalerForPDidiMiniEstimate               func(ctx context.Context, resp *PDidiMiniEstimateResp) (ret interface{})
	responseMarshalerForPEngageCarEstimate              func(ctx context.Context, resp *EngageCarEstimateResponse) (ret interface{})
	responseMarshalerForPPetsTravelEstimate             func(ctx context.Context, resp *PetsTravelEstimateResponse) (ret interface{})
	responseMarshalerForPOverseaEstimate                func(ctx context.Context, resp *OverseaEstimateResponse) (ret interface{})
	responseMarshalerForPEstimateByOrder                func(ctx context.Context, resp *PEstimateByOrderResponse) (ret interface{})
	responseMarshalerForEstimateOrderWithoutRender      func(ctx context.Context, resp *EstimateOrderWithoutRenderRsp) (ret interface{})
	responseMarshalerForPPbdStationBusMultiStationPrice func(ctx context.Context, resp *PbdStationBusMultiStationPriceRsp) (ret interface{})
	responseMarshalerForPPbdStationBusOrderEstimate     func(ctx context.Context, resp *PbdStationBusOrderEstimateRsp) (ret interface{})
	responseMarshalerForPPbdStationBusMultiEstimate     func(ctx context.Context, resp *PbdStationBusMultiEstimateRsp) (ret interface{})
	responseMarshalerForPPbdStationBusDetailEstimate    func(ctx context.Context, resp *PbdStationBusDetailEstimateRsp) (ret interface{})
	responseMarshalerForPCharterHomePage                func(ctx context.Context, resp *CharterHomePageRsp) (ret interface{})
	responseMarshalerForPCharterMultiEstimate           func(ctx context.Context, resp *CharterMultiEstimateRsp) (ret interface{})
	responseMarshalerForPPassengerBeginCharge           func(ctx context.Context, resp *PPassengerBeginChargeResponse) (ret interface{})
	responseMarshalerForPGetRentInfo                    func(ctx context.Context, resp *GetRentInfoRsp) (ret interface{})
	responseMarshalerForPBusinessRentEstimate           func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})
	responseMarshalerForPEstimateCarpoolMultiOrder      func(ctx context.Context, resp *PEstimateCarpoolOrderResponse) (ret interface{})
	responseMarshalerForPEstimateV3                     func(ctx context.Context, resp *PEstimateV3DataResponse) (ret interface{})
	responseMarshalerForPGetBusinessTailorService       func(ctx context.Context, resp *BusinessTailorServiceResponse) (ret interface{})
	responseMarshalerForPetAnyCarEstimate               func(ctx context.Context, resp *AnyCarEstimateV4Resp) (ret interface{})
	responseMarshalerForMeetingCarEstimate              func(ctx context.Context, resp *MeetingCarEstimateRsp) (ret interface{})
}

func SetResponseMarshalerForPing(p func(ctx context.Context, resp *PingRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPing = p
}

func SetResponseMarshalerForPCarpoolEstimatePrice(p func(ctx context.Context, resp *CarpoolEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPCarpoolEstimatePrice = p
}

func SetResponseMarshalerForIntercityEstimatePrice(p func(ctx context.Context, resp *IntercityEstimateResp) (ret interface{})) {
	marshalOptions.responseMarshalerForIntercityEstimatePrice = p
}

func SetResponseMarshalerForMiniBusEstimatePrice(p func(ctx context.Context, resp *MiniBusEstimateResp) (ret interface{})) {
	marshalOptions.responseMarshalerForMiniBusEstimatePrice = p
}

func SetResponseMarshalerForAnyCarEstimate(p func(ctx context.Context, resp *AnyCarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForAnyCarEstimate = p
}

func SetResponseMarshalerForAnyCarEstimateV3(p func(ctx context.Context, resp *AnyCarEstimateV3Rsp) (ret interface{})) {
	marshalOptions.responseMarshalerForAnyCarEstimateV3 = p
}

func SetResponseMarshalerForAnyCarEstimateV4(p func(ctx context.Context, resp *AnyCarEstimateV4Resp) (ret interface{})) {
	marshalOptions.responseMarshalerForAnyCarEstimateV4 = p
}

func SetResponseMarshalerForGetAnycarEstimateCache(p func(ctx context.Context, resp *AnycarEstimateCacheResp) (ret interface{})) {
	marshalOptions.responseMarshalerForGetAnycarEstimateCache = p
}

func SetResponseMarshalerForDelAnycarEstimateCache(p func(ctx context.Context, resp *AnycarEstimateDelCacheResp) (ret interface{})) {
	marshalOptions.responseMarshalerForDelAnycarEstimateCache = p
}

func SetResponseMarshalerForCombinedTravelEstimate(p func(ctx context.Context, resp *CombinedTravelEstimateRes) (ret interface{})) {
	marshalOptions.responseMarshalerForCombinedTravelEstimate = p
}

func SetResponseMarshalerForPCancelEstimate(p func(ctx context.Context, resp *CancelEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPCancelEstimate = p
}

func SetResponseMarshalerForPBusinessEstimate(p func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPBusinessEstimate = p
}

func SetResponseMarshalerForPBusinessAnyCarEstimate(p func(ctx context.Context, resp *B2BAnyCarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPBusinessAnyCarEstimate = p
}

func SetResponseMarshalerForPPbdEstimate(p func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPbdEstimate = p
}

func SetResponseMarshalerForPPBDAnyCarEstimate(p func(ctx context.Context, resp *B2BAnyCarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPBDAnyCarEstimate = p
}

func SetResponseMarshalerForPAssistantEstimate(p func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPAssistantEstimate = p
}

func SetResponseMarshalerForPHelperEstimate(p func(ctx context.Context, resp *HelperEstimateRes) (ret interface{})) {
	marshalOptions.responseMarshalerForPHelperEstimate = p
}

func SetResponseMarshalerForPShuttleBusPageEstimate(p func(ctx context.Context, resp *ShuttleBusPageEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPShuttleBusPageEstimate = p
}

func SetResponseMarshalerForPShuttleBusGuideBarEstimate(p func(ctx context.Context, resp *ShuttleBusGuideBarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPShuttleBusGuideBarEstimate = p
}

func SetResponseMarshalerForPBargainEstimate(p func(ctx context.Context, resp *BargainEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPBargainEstimate = p
}

func SetResponseMarshalerForPHomePageCallCarEstimate(p func(ctx context.Context, resp *HomePageCallCarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPHomePageCallCarEstimate = p
}

func SetResponseMarshalerForPHomePageCallCarEstimateV2(p func(ctx context.Context, resp *HomePageCallCarEstimateRspV2) (ret interface{})) {
	marshalOptions.responseMarshalerForPHomePageCallCarEstimateV2 = p
}

func SetResponseMarshalerForPCarpoolPrice(p func(ctx context.Context, resp *CarpoolPriceResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPCarpoolPrice = p
}

func SetResponseMarshalerForPRecCarpoolEstimate(p func(ctx context.Context, resp *RecCarpoolEstimateResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPRecCarpoolEstimate = p
}

func SetResponseMarshalerForPCarpoolInvitationEstimate(p func(ctx context.Context, resp *CarpoolInvitationEstimateResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPCarpoolInvitationEstimate = p
}

func SetResponseMarshalerForPGetCouponPrice(p func(ctx context.Context, resp *CouponPriceResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetCouponPrice = p
}

func SetResponseMarshalerForPServiceEstimate(p func(ctx context.Context, resp *ServiceMultiEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPServiceEstimate = p
}

func SetResponseMarshalerForPSFCEstimate(p func(ctx context.Context, resp *SFCMultiEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPSFCEstimate = p
}

func SetResponseMarshalerForPSFCModifyEstimate(p func(ctx context.Context, resp *SFCModifyEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPSFCModifyEstimate = p
}

func SetResponseMarshalerForPSFCSimpleEstimate(p func(ctx context.Context, resp *SFCSimpleEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPSFCSimpleEstimate = p
}

func SetResponseMarshalerForPCompositeTravel(p func(ctx context.Context, resp *CompositeTravelRes) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravel = p
}

func SetResponseMarshalerForPCompositeTravelV2(p func(ctx context.Context, resp *CompositeTravelV2Res) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravelV2 = p
}

func SetResponseMarshalerForPCompositeTravelPoi(p func(ctx context.Context, resp *CompositeTravelPoiRes) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravelPoi = p
}

func SetResponseMarshalerForPCompositeTravelV3(p func(ctx context.Context, resp *CompositeTravelV3Res) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravelV3 = p
}

func SetResponseMarshalerForPCompositeTravelV3Post(p func(ctx context.Context, resp *CompositeTravelV3Res) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravelV3Post = p
}

func SetResponseMarshalerForPCompositeTravelOptions(p func(ctx context.Context, resp *CompositeTravelOptionsRes) (ret interface{})) {
	marshalOptions.responseMarshalerForPCompositeTravelOptions = p
}

func SetResponseMarshalerForPRecommendLanding(p func(ctx context.Context, resp *PRecommendLandingRes) (ret interface{})) {
	marshalOptions.responseMarshalerForPRecommendLanding = p
}

func SetResponseMarshalerForPGetDynamicFeeDescList(p func(ctx context.Context, resp *DynamicFeeDescListResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetDynamicFeeDescList = p
}

func SetResponseMarshalerForPGetDynamicFeeDescListPost(p func(ctx context.Context, resp *DynamicFeeDescListResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetDynamicFeeDescListPost = p
}

func SetResponseMarshalerForPPickOnTimeEstimate(p func(ctx context.Context, resp *PickOnTimeEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPickOnTimeEstimate = p
}

func SetResponseMarshalerForPGetGuideInfo(p func(ctx context.Context, resp *PGetGuideInfoRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetGuideInfo = p
}

func SetResponseMarshalerForPGetGuideInfoPost(p func(ctx context.Context, resp *PGetGuideInfoRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetGuideInfoPost = p
}

func SetResponseMarshalerForPGetFormRealData(p func(ctx context.Context, resp *PGetFormRealDataResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetFormRealData = p
}

func SetResponseMarshalerForPGetFormRealDataPost(p func(ctx context.Context, resp *PGetFormRealDataResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetFormRealDataPost = p
}

func SetResponseMarshalerForPGetEstimateDataByEidList(p func(ctx context.Context, resp *PGetEstimateDataByEidListResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetEstimateDataByEidList = p
}

func SetResponseMarshalerForPRouteEstimate(p func(ctx context.Context, resp *RouteEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPRouteEstimate = p
}

func SetResponseMarshalerForPIntercityMultiEstimateEstimate(p func(ctx context.Context, resp *IntercityMultiEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPIntercityMultiEstimateEstimate = p
}

func SetResponseMarshalerForPIntercityEstimateDetail(p func(ctx context.Context, resp *IntercityEstimateDetailResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPIntercityEstimateDetail = p
}

func SetResponseMarshalerForPOrderBookingPreCancelEstimate(p func(ctx context.Context, resp *AnyCarEstimateV3Rsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPOrderBookingPreCancelEstimate = p
}

func SetResponseMarshalerForPInvitedEstimate(p func(ctx context.Context, resp *InvitedEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPInvitedEstimate = p
}

func SetResponseMarshalerForPMultiPointEstimate(p func(ctx context.Context, resp *MultiPointEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPMultiPointEstimate = p
}

func SetResponseMarshalerForPGetEstimateDataWithoutRender(p func(ctx context.Context, resp *PGetEstimateDataWithoutRenderResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetEstimateDataWithoutRender = p
}

func SetResponseMarshalerForPGetEstimateDataWithoutRenderV2(p func(ctx context.Context, resp *PGetEstimateDataWithoutRenderResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetEstimateDataWithoutRenderV2 = p
}

func SetResponseMarshalerForPGetBusinessFormRealData(p func(ctx context.Context, resp *GetBusinessFormRealDataResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetBusinessFormRealData = p
}

func SetResponseMarshalerForPBargainRangeEstimate(p func(ctx context.Context, resp *BargainRangeEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPBargainRangeEstimate = p
}

func SetResponseMarshalerForPStationBusInsteadOrderEstimate(p func(ctx context.Context, resp *StationBusInsteadOrderEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPStationBusInsteadOrderEstimate = p
}

func SetResponseMarshalerForPGetIntercityBasicFeeLite(p func(ctx context.Context, resp *IntercityBasicFeeLiteResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetIntercityBasicFeeLite = p
}

func SetResponseMarshalerForPDidiMiniEstimate(p func(ctx context.Context, resp *PDidiMiniEstimateResp) (ret interface{})) {
	marshalOptions.responseMarshalerForPDidiMiniEstimate = p
}

func SetResponseMarshalerForPEngageCarEstimate(p func(ctx context.Context, resp *EngageCarEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPEngageCarEstimate = p
}

func SetResponseMarshalerForPPetsTravelEstimate(p func(ctx context.Context, resp *PetsTravelEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPPetsTravelEstimate = p
}

func SetResponseMarshalerForPOverseaEstimate(p func(ctx context.Context, resp *OverseaEstimateResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPOverseaEstimate = p
}

func SetResponseMarshalerForPEstimateByOrder(p func(ctx context.Context, resp *PEstimateByOrderResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPEstimateByOrder = p
}

func SetResponseMarshalerForEstimateOrderWithoutRender(p func(ctx context.Context, resp *EstimateOrderWithoutRenderRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForEstimateOrderWithoutRender = p
}

func SetResponseMarshalerForPPbdStationBusMultiStationPrice(p func(ctx context.Context, resp *PbdStationBusMultiStationPriceRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPbdStationBusMultiStationPrice = p
}

func SetResponseMarshalerForPPbdStationBusOrderEstimate(p func(ctx context.Context, resp *PbdStationBusOrderEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPbdStationBusOrderEstimate = p
}

func SetResponseMarshalerForPPbdStationBusMultiEstimate(p func(ctx context.Context, resp *PbdStationBusMultiEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPbdStationBusMultiEstimate = p
}

func SetResponseMarshalerForPPbdStationBusDetailEstimate(p func(ctx context.Context, resp *PbdStationBusDetailEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPPbdStationBusDetailEstimate = p
}

func SetResponseMarshalerForPCharterHomePage(p func(ctx context.Context, resp *CharterHomePageRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPCharterHomePage = p
}

func SetResponseMarshalerForPCharterMultiEstimate(p func(ctx context.Context, resp *CharterMultiEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPCharterMultiEstimate = p
}

func SetResponseMarshalerForPPassengerBeginCharge(p func(ctx context.Context, resp *PPassengerBeginChargeResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPPassengerBeginCharge = p
}

func SetResponseMarshalerForPGetRentInfo(p func(ctx context.Context, resp *GetRentInfoRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetRentInfo = p
}

func SetResponseMarshalerForPBusinessRentEstimate(p func(ctx context.Context, resp *B2BEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForPBusinessRentEstimate = p
}

func SetResponseMarshalerForPEstimateCarpoolMultiOrder(p func(ctx context.Context, resp *PEstimateCarpoolOrderResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPEstimateCarpoolMultiOrder = p
}

func SetResponseMarshalerForPEstimateV3(p func(ctx context.Context, resp *PEstimateV3DataResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPEstimateV3 = p
}

func SetResponseMarshalerForPGetBusinessTailorService(p func(ctx context.Context, resp *BusinessTailorServiceResponse) (ret interface{})) {
	marshalOptions.responseMarshalerForPGetBusinessTailorService = p
}

func SetResponseMarshalerForPetAnyCarEstimate(p func(ctx context.Context, resp *AnyCarEstimateV4Resp) (ret interface{})) {
	marshalOptions.responseMarshalerForPetAnyCarEstimate = p
}

func SetResponseMarshalerForMeetingCarEstimate(p func(ctx context.Context, resp *MeetingCarEstimateRsp) (ret interface{})) {
	marshalOptions.responseMarshalerForMeetingCarEstimate = p
}

var (
	filter_Mamba_Ping_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_Ping_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PingRsp, error) {
	var protoReq PingReq

	if methodOptions.requestParserForPing != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPing(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPing err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.Ping(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCarpoolEstimatePrice_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PCarpoolEstimatePrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CarpoolEstimateResponse, error) {
	var protoReq CarpoolEstimateRequest

	if methodOptions.requestParserForPCarpoolEstimatePrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCarpoolEstimatePrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCarpoolEstimatePrice err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPCarpoolEstimatePrice {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCarpoolEstimatePrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_IntercityEstimatePrice_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_IntercityEstimatePrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*IntercityEstimateResp, error) {
	var protoReq CarpoolEstimateRequest

	if methodOptions.requestParserForIntercityEstimatePrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForIntercityEstimatePrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForIntercityEstimatePrice err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForIntercityEstimatePrice {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.IntercityEstimatePrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_MiniBusEstimatePrice_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_MiniBusEstimatePrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*MiniBusEstimateResp, error) {
	var protoReq MiniBusEstimateRequest

	if methodOptions.requestParserForMiniBusEstimatePrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForMiniBusEstimatePrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForMiniBusEstimatePrice err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForMiniBusEstimatePrice {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.MiniBusEstimatePrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_AnyCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_AnyCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnyCarEstimateRsp, error) {
	var protoReq AnyCarEstimateReq

	if methodOptions.requestParserForAnyCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForAnyCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForAnyCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.AnyCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_AnyCarEstimateV3_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_AnyCarEstimateV3_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnyCarEstimateV3Rsp, error) {
	var protoReq AnyCarEstimateReq

	if methodOptions.requestParserForAnyCarEstimateV3 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForAnyCarEstimateV3(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForAnyCarEstimateV3 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.AnyCarEstimateV3(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_AnyCarEstimateV4_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_AnyCarEstimateV4_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnyCarEstimateV4Resp, error) {
	var protoReq AnyCarEstimateV4Req

	if methodOptions.requestParserForAnyCarEstimateV4 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForAnyCarEstimateV4(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForAnyCarEstimateV4 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.AnyCarEstimateV4(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_GetAnycarEstimateCache_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_GetAnycarEstimateCache_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnycarEstimateCacheResp, error) {
	var protoReq AnycarEstimateCacheReq

	if methodOptions.requestParserForGetAnycarEstimateCache != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForGetAnycarEstimateCache(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForGetAnycarEstimateCache err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.GetAnycarEstimateCache(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_DelAnycarEstimateCache_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_DelAnycarEstimateCache_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnycarEstimateDelCacheResp, error) {
	var protoReq AnycarEstimateCacheReq

	if methodOptions.requestParserForDelAnycarEstimateCache != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForDelAnycarEstimateCache(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForDelAnycarEstimateCache err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.DelAnycarEstimateCache(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_CombinedTravelEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_CombinedTravelEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CombinedTravelEstimateRes, error) {
	var protoReq MultiEstimatePriceRequest

	if methodOptions.requestParserForCombinedTravelEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForCombinedTravelEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForCombinedTravelEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForCombinedTravelEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.CombinedTravelEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCancelEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCancelEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CancelEstimateRsp, error) {
	var protoReq CancelEstimateReq

	if methodOptions.requestParserForPCancelEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCancelEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCancelEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCancelEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PBusinessEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PBusinessEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BEstimateRsp, error) {
	var protoReq B2BEstimateReq

	if methodOptions.requestParserForPBusinessEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPBusinessEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPBusinessEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PBusinessEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PBusinessAnyCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PBusinessAnyCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BAnyCarEstimateRsp, error) {
	var protoReq B2BAnyCarEstimateReq

	if methodOptions.requestParserForPBusinessAnyCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPBusinessAnyCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPBusinessAnyCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PBusinessAnyCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPbdEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPbdEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BEstimateRsp, error) {
	var protoReq PBDEstimateReq

	if methodOptions.requestParserForPPbdEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPbdEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPbdEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPbdEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPBDAnyCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPBDAnyCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BAnyCarEstimateRsp, error) {
	var protoReq PBDAnyCarEstimateReq

	if methodOptions.requestParserForPPBDAnyCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPBDAnyCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPBDAnyCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPBDAnyCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PAssistantEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PAssistantEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BEstimateRsp, error) {
	var protoReq PBDEstimateReq

	if methodOptions.requestParserForPAssistantEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPAssistantEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPAssistantEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPAssistantEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PAssistantEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PHelperEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PHelperEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*HelperEstimateRes, error) {
	var protoReq MultiEstimatePriceRequest

	if methodOptions.requestParserForPHelperEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPHelperEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPHelperEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PHelperEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PShuttleBusPageEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PShuttleBusPageEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*ShuttleBusPageEstimateRsp, error) {
	var protoReq ShuttleBusPageEstimateReq

	if methodOptions.requestParserForPShuttleBusPageEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPShuttleBusPageEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPShuttleBusPageEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PShuttleBusPageEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PShuttleBusGuideBarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PShuttleBusGuideBarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*ShuttleBusGuideBarEstimateRsp, error) {
	var protoReq ShuttleBusGuideBarEstimateReq

	if methodOptions.requestParserForPShuttleBusGuideBarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPShuttleBusGuideBarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPShuttleBusGuideBarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PShuttleBusGuideBarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PBargainEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PBargainEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*BargainEstimateRsp, error) {
	var protoReq BargainEstimateReq

	if methodOptions.requestParserForPBargainEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPBargainEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPBargainEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PBargainEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PHomePageCallCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PHomePageCallCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*HomePageCallCarEstimateRsp, error) {
	var protoReq HomePageCallCarEstimateReq

	if methodOptions.requestParserForPHomePageCallCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPHomePageCallCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPHomePageCallCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PHomePageCallCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PHomePageCallCarEstimateV2_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PHomePageCallCarEstimateV2_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*HomePageCallCarEstimateRspV2, error) {
	var protoReq HomePageCallCarEstimateReq

	if methodOptions.requestParserForPHomePageCallCarEstimateV2 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPHomePageCallCarEstimateV2(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPHomePageCallCarEstimateV2 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PHomePageCallCarEstimateV2(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCarpoolPrice_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCarpoolPrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CarpoolPriceResponse, error) {
	var protoReq CarpoolEstimateRequest

	if methodOptions.requestParserForPCarpoolPrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCarpoolPrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCarpoolPrice err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCarpoolPrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PRecCarpoolEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PRecCarpoolEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*RecCarpoolEstimateResp, error) {
	var protoReq RecCarpoolEstimateReq

	if methodOptions.requestParserForPRecCarpoolEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPRecCarpoolEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPRecCarpoolEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PRecCarpoolEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCarpoolInvitationEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCarpoolInvitationEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CarpoolInvitationEstimateResp, error) {
	var protoReq CarpoolInvitationEstimateRequest

	if methodOptions.requestParserForPCarpoolInvitationEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCarpoolInvitationEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCarpoolInvitationEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCarpoolInvitationEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetCouponPrice_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetCouponPrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CouponPriceResponse, error) {
	var protoReq CouponPriceRequest

	if methodOptions.requestParserForPGetCouponPrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetCouponPrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetCouponPrice err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetCouponPrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PServiceEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PServiceEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*ServiceMultiEstimateResponse, error) {
	var protoReq ServiceEstimateRequest

	if methodOptions.requestParserForPServiceEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPServiceEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPServiceEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PServiceEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PSFCEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PSFCEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*SFCMultiEstimateResponse, error) {
	var protoReq SFCEstimateRequest

	if methodOptions.requestParserForPSFCEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPSFCEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPSFCEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PSFCEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PSFCModifyEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PSFCModifyEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*SFCModifyEstimateResponse, error) {
	var protoReq SFCModifyEstimateRequest

	if methodOptions.requestParserForPSFCModifyEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPSFCModifyEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPSFCModifyEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PSFCModifyEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PSFCSimpleEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PSFCSimpleEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*SFCSimpleEstimateResponse, error) {
	var protoReq SFCSimpleEstimateReq

	if methodOptions.requestParserForPSFCSimpleEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPSFCSimpleEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPSFCSimpleEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPSFCSimpleEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PSFCSimpleEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravel_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCompositeTravel_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelRes, error) {
	var protoReq CompositeTravelReq

	if methodOptions.requestParserForPCompositeTravel != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravel(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravel err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravel(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravelV2_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCompositeTravelV2_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelV2Res, error) {
	var protoReq CompositeTravelV2Req

	if methodOptions.requestParserForPCompositeTravelV2 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravelV2(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravelV2 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravelV2(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravelPoi_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCompositeTravelPoi_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelPoiRes, error) {
	var protoReq CompositeTravelPoiReq

	if methodOptions.requestParserForPCompositeTravelPoi != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravelPoi(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravelPoi err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravelPoi(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravelV3_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCompositeTravelV3_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelV3Res, error) {
	var protoReq CompositeTravelV3Req

	if methodOptions.requestParserForPCompositeTravelV3 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravelV3(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravelV3 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravelV3(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravelV3Post_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PCompositeTravelV3Post_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelV3Res, error) {
	var protoReq CompositeTravelV3Req

	if methodOptions.requestParserForPCompositeTravelV3Post != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravelV3Post(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravelV3Post err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPCompositeTravelV3Post {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravelV3Post(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCompositeTravelOptions_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCompositeTravelOptions_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CompositeTravelOptionsRes, error) {
	var protoReq CompositeTravelOptionsReq

	if methodOptions.requestParserForPCompositeTravelOptions != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCompositeTravelOptions(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCompositeTravelOptions err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCompositeTravelOptions(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PRecommendLanding_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PRecommendLanding_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PRecommendLandingRes, error) {
	var protoReq CompositeTravelV2Req

	if methodOptions.requestParserForPRecommendLanding != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPRecommendLanding(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPRecommendLanding err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PRecommendLanding(ctx, &protoReq)
	if msg != nil {
	}

	return msg, err
}

var (
	filter_Mamba_PGetDynamicFeeDescList_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetDynamicFeeDescList_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*DynamicFeeDescListResponse, error) {
	var protoReq DynamicFeeDescListRequest

	if methodOptions.requestParserForPGetDynamicFeeDescList != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetDynamicFeeDescList(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetDynamicFeeDescList err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetDynamicFeeDescList(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetDynamicFeeDescListPost_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PGetDynamicFeeDescListPost_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*DynamicFeeDescListResponse, error) {
	var protoReq DynamicFeeDescListRequest

	if methodOptions.requestParserForPGetDynamicFeeDescListPost != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetDynamicFeeDescListPost(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetDynamicFeeDescListPost err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPGetDynamicFeeDescListPost {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetDynamicFeeDescListPost(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPickOnTimeEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPickOnTimeEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PickOnTimeEstimateRsp, error) {
	var protoReq PickOnTimeEstimateReq

	if methodOptions.requestParserForPPickOnTimeEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPickOnTimeEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPickOnTimeEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPickOnTimeEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetGuideInfo_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetGuideInfo_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetGuideInfoRsp, error) {
	var protoReq PGetGuideInfoReq

	if methodOptions.requestParserForPGetGuideInfo != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetGuideInfo(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetGuideInfo err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetGuideInfo(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetGuideInfoPost_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PGetGuideInfoPost_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetGuideInfoRsp, error) {
	var protoReq PGetGuideInfoReq

	if methodOptions.requestParserForPGetGuideInfoPost != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetGuideInfoPost(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetGuideInfoPost err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPGetGuideInfoPost {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetGuideInfoPost(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetFormRealData_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetFormRealData_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetFormRealDataResp, error) {
	var protoReq PGetFormRealDataReq

	if methodOptions.requestParserForPGetFormRealData != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetFormRealData(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetFormRealData err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetFormRealData(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetFormRealDataPost_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PGetFormRealDataPost_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetFormRealDataResp, error) {
	var protoReq PGetFormRealDataReq

	if methodOptions.requestParserForPGetFormRealDataPost != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetFormRealDataPost(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetFormRealDataPost err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPGetFormRealDataPost {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetFormRealDataPost(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetEstimateDataByEidList_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetEstimateDataByEidList_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetEstimateDataByEidListResp, error) {
	var protoReq PGetEstimateDataByEidListReq

	if methodOptions.requestParserForPGetEstimateDataByEidList != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetEstimateDataByEidList(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetEstimateDataByEidList err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetEstimateDataByEidList(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PRouteEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PRouteEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*RouteEstimateResponse, error) {
	var protoReq RouteEstimateRequest

	if methodOptions.requestParserForPRouteEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPRouteEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPRouteEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PRouteEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PIntercityMultiEstimateEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PIntercityMultiEstimateEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*IntercityMultiEstimateResponse, error) {
	var protoReq IntercityMultiEstimateRequest

	if methodOptions.requestParserForPIntercityMultiEstimateEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPIntercityMultiEstimateEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPIntercityMultiEstimateEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPIntercityMultiEstimateEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PIntercityMultiEstimateEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PIntercityEstimateDetail_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PIntercityEstimateDetail_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*IntercityEstimateDetailResponse, error) {
	var protoReq IntercityEstimateDetailRequest

	if methodOptions.requestParserForPIntercityEstimateDetail != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPIntercityEstimateDetail(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPIntercityEstimateDetail err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PIntercityEstimateDetail(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_POrderBookingPreCancelEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_POrderBookingPreCancelEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnyCarEstimateV3Rsp, error) {
	var protoReq AnyCarEstimateReq

	if methodOptions.requestParserForPOrderBookingPreCancelEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPOrderBookingPreCancelEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPOrderBookingPreCancelEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.POrderBookingPreCancelEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PInvitedEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PInvitedEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*InvitedEstimateRsp, error) {
	var protoReq InvitedEstimateReq

	if methodOptions.requestParserForPInvitedEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPInvitedEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPInvitedEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PInvitedEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PMultiPointEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PMultiPointEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*MultiPointEstimateResponse, error) {
	var protoReq MultiPointEstimateRequest

	if methodOptions.requestParserForPMultiPointEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPMultiPointEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPMultiPointEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PMultiPointEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetEstimateDataWithoutRender_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetEstimateDataWithoutRender_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetEstimateDataWithoutRenderResp, error) {
	var protoReq MultiEstimatePriceRequest

	if methodOptions.requestParserForPGetEstimateDataWithoutRender != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetEstimateDataWithoutRender(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetEstimateDataWithoutRender err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetEstimateDataWithoutRender(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetEstimateDataWithoutRenderV2_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetEstimateDataWithoutRenderV2_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PGetEstimateDataWithoutRenderResp, error) {
	var protoReq PGetEstimateDataWithoutRenderReq

	if methodOptions.requestParserForPGetEstimateDataWithoutRenderV2 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetEstimateDataWithoutRenderV2(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetEstimateDataWithoutRenderV2 err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetEstimateDataWithoutRenderV2(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetBusinessFormRealData_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetBusinessFormRealData_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*GetBusinessFormRealDataResponse, error) {
	var protoReq GetBusinessFormRealDataRequest

	if methodOptions.requestParserForPGetBusinessFormRealData != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetBusinessFormRealData(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetBusinessFormRealData err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetBusinessFormRealData(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PBargainRangeEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PBargainRangeEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*BargainRangeEstimateRsp, error) {
	var protoReq BargainRangeEstimateReq

	if methodOptions.requestParserForPBargainRangeEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPBargainRangeEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPBargainRangeEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PBargainRangeEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PStationBusInsteadOrderEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PStationBusInsteadOrderEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*StationBusInsteadOrderEstimateRsp, error) {
	var protoReq StationBusInsteadOrderEstimateReq

	if methodOptions.requestParserForPStationBusInsteadOrderEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPStationBusInsteadOrderEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPStationBusInsteadOrderEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PStationBusInsteadOrderEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetIntercityBasicFeeLite_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetIntercityBasicFeeLite_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*IntercityBasicFeeLiteResponse, error) {
	var protoReq IntercityBasicFeeLiteRequest

	if methodOptions.requestParserForPGetIntercityBasicFeeLite != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetIntercityBasicFeeLite(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetIntercityBasicFeeLite err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetIntercityBasicFeeLite(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PDidiMiniEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PDidiMiniEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PDidiMiniEstimateResp, error) {
	var protoReq PDidiMiniEstimateReq

	if methodOptions.requestParserForPDidiMiniEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPDidiMiniEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPDidiMiniEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PDidiMiniEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PEngageCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PEngageCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*EngageCarEstimateResponse, error) {
	var protoReq EngageCarEstimateReq

	if methodOptions.requestParserForPEngageCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPEngageCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPEngageCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PEngageCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPetsTravelEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PPetsTravelEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PetsTravelEstimateResponse, error) {
	var protoReq PetsTravelEstimateReq

	if methodOptions.requestParserForPPetsTravelEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPetsTravelEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPetsTravelEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPPetsTravelEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPetsTravelEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_POverseaEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_POverseaEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*OverseaEstimateResponse, error) {
	var protoReq OverseaEstimateReq

	if methodOptions.requestParserForPOverseaEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPOverseaEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPOverseaEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPOverseaEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.POverseaEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PEstimateByOrder_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PEstimateByOrder_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PEstimateByOrderResponse, error) {
	var protoReq PEstimateByOrderRequest

	if methodOptions.requestParserForPEstimateByOrder != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPEstimateByOrder(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPEstimateByOrder err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PEstimateByOrder(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_EstimateOrderWithoutRender_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_EstimateOrderWithoutRender_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*EstimateOrderWithoutRenderRsp, error) {
	var protoReq EstimateOrderWithoutRenderReq

	if methodOptions.requestParserForEstimateOrderWithoutRender != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForEstimateOrderWithoutRender(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForEstimateOrderWithoutRender err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.EstimateOrderWithoutRender(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPbdStationBusMultiStationPrice_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPbdStationBusMultiStationPrice_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PbdStationBusMultiStationPriceRsp, error) {
	var protoReq PbdStationBusMultiStationPriceReq

	if methodOptions.requestParserForPPbdStationBusMultiStationPrice != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPbdStationBusMultiStationPrice(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPbdStationBusMultiStationPrice err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPbdStationBusMultiStationPrice(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPbdStationBusOrderEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPbdStationBusOrderEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PbdStationBusOrderEstimateRsp, error) {
	var protoReq PbdStationBusOrderEstimateReq

	if methodOptions.requestParserForPPbdStationBusOrderEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPbdStationBusOrderEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPbdStationBusOrderEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPbdStationBusOrderEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPbdStationBusMultiEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPbdStationBusMultiEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PbdStationBusMultiEstimateRsp, error) {
	var protoReq PbdStationBusMultiEstimateReq

	if methodOptions.requestParserForPPbdStationBusMultiEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPbdStationBusMultiEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPbdStationBusMultiEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPbdStationBusMultiEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPbdStationBusDetailEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPbdStationBusDetailEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PbdStationBusDetailEstimateRsp, error) {
	var protoReq PbdStationBusDetailEstimateReq

	if methodOptions.requestParserForPPbdStationBusDetailEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPbdStationBusDetailEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPbdStationBusDetailEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPbdStationBusDetailEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCharterHomePage_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCharterHomePage_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CharterHomePageRsp, error) {
	var protoReq CharterHomePageReq

	if methodOptions.requestParserForPCharterHomePage != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCharterHomePage(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCharterHomePage err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCharterHomePage(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PCharterMultiEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PCharterMultiEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*CharterMultiEstimateRsp, error) {
	var protoReq CharterMultiEstimateReq

	if methodOptions.requestParserForPCharterMultiEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPCharterMultiEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPCharterMultiEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PCharterMultiEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PPassengerBeginCharge_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PPassengerBeginCharge_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PPassengerBeginChargeResponse, error) {
	var protoReq PPassengerBeginChargeRequest

	if methodOptions.requestParserForPPassengerBeginCharge != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPPassengerBeginCharge(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPPassengerBeginCharge err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PPassengerBeginCharge(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetRentInfo_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetRentInfo_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*GetRentInfoRsp, error) {
	var protoReq GetRentInfoReq

	if methodOptions.requestParserForPGetRentInfo != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetRentInfo(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetRentInfo err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetRentInfo(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PBusinessRentEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PBusinessRentEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*B2BEstimateRsp, error) {
	var protoReq BusinessRentEstimateReq

	if methodOptions.requestParserForPBusinessRentEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPBusinessRentEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPBusinessRentEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PBusinessRentEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PEstimateCarpoolMultiOrder_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PEstimateCarpoolMultiOrder_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PEstimateCarpoolOrderResponse, error) {
	var protoReq PEstimateCarpoolOrderRequest

	if methodOptions.requestParserForPEstimateCarpoolMultiOrder != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPEstimateCarpoolMultiOrder(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPEstimateCarpoolMultiOrder err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PEstimateCarpoolMultiOrder(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PEstimateV3_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_PEstimateV3_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*PEstimateV3DataResponse, error) {
	var protoReq PEstimateV3DataReq

	if methodOptions.requestParserForPEstimateV3 != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPEstimateV3(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPEstimateV3 err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForPEstimateV3 {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PEstimateV3(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PGetBusinessTailorService_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PGetBusinessTailorService_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*BusinessTailorServiceResponse, error) {
	var protoReq LuxMultiEstimatePriceRequest

	if methodOptions.requestParserForPGetBusinessTailorService != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPGetBusinessTailorService(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPGetBusinessTailorService err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PGetBusinessTailorService(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_PetAnyCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func local_request_Mamba_PetAnyCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*AnyCarEstimateV4Resp, error) {
	var protoReq AnyCarEstimateV4Req

	if methodOptions.requestParserForPetAnyCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForPetAnyCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForPetAnyCarEstimate err, %v", err)
		}
	} else {
		if err := req.ParseForm(); err != nil {
			return nil, fmt.Errorf("ParseForm err, err %v", err)
		}

		if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
			return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.PetAnyCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

var (
	filter_Mamba_MeetingCarEstimate_0 = &utilities.DoubleArray{Encoding: map[string]int{"": 0}, Base: []int{1, 1, 0}, Check: []int{0, 1, 2}}
)

func local_request_Mamba_MeetingCarEstimate_0(ctx context.Context, marshaler runtime.Marshaler, server MambaServer, req *http.Request, pathParams map[string]string) (*MeetingCarEstimateRsp, error) {
	var protoReq MeetingCarEstimateReq

	if methodOptions.requestParserForMeetingCarEstimate != nil {
		var err error
		if protoReq, err = methodOptions.requestParserForMeetingCarEstimate(req, pathParams); err != nil {
			return nil, fmt.Errorf("requestParserForMeetingCarEstimate err, %v", err)
		}
	} else {
		if methodOptions.parseBodyAndQueryStrForMeetingCarEstimate {
			if err := req.ParseForm(); err != nil {
				return nil, fmt.Errorf("ParseForm err, err %v", err)
			}

			if err := binding.ShouldBindQuery(req, &protoReq); err != nil {
				return nil, fmt.Errorf("PopulateQueryParameters err, err %v", err)
			}
		}

		newReader, berr := utilities.IOReaderFactory(req.Body)
		if berr != nil {
			return nil, berr
		}
		if err := marshaler.NewDecoder(newReader()).Decode(&protoReq); err != nil && err != io.EOF {
			return nil, err
		}
	}

	msg, err := server.MeetingCarEstimate(ctx, &protoReq)
	if msg != nil {
		setTraceCustomKV(req.Context(), ErrNo, toString(msg.Errno))
		setTraceCustomKV(req.Context(), Errmsg, toString(msg.Errmsg))
	}

	return msg, err
}

// RegisterMambaServer registers the http handlers for service Mamba to "mux".
func RegisterMambaHandlerServer(ctx context.Context, mux *runtime.ServeMux, server MambaServer) error {

	mux.Handle("GET", pattern_Mamba_Ping_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_Ping_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPing != nil {
			ri = marshalOptions.responseMarshalerForPing(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PCarpoolEstimatePrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCarpoolEstimatePrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCarpoolEstimatePrice != nil {
			ri = marshalOptions.responseMarshalerForPCarpoolEstimatePrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_IntercityEstimatePrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_IntercityEstimatePrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForIntercityEstimatePrice != nil {
			ri = marshalOptions.responseMarshalerForIntercityEstimatePrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_MiniBusEstimatePrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_MiniBusEstimatePrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForMiniBusEstimatePrice != nil {
			ri = marshalOptions.responseMarshalerForMiniBusEstimatePrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_AnyCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_AnyCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForAnyCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForAnyCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_AnyCarEstimateV3_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_AnyCarEstimateV3_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForAnyCarEstimateV3 != nil {
			ri = marshalOptions.responseMarshalerForAnyCarEstimateV3(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_AnyCarEstimateV4_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_AnyCarEstimateV4_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForAnyCarEstimateV4 != nil {
			ri = marshalOptions.responseMarshalerForAnyCarEstimateV4(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_GetAnycarEstimateCache_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_GetAnycarEstimateCache_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForGetAnycarEstimateCache != nil {
			ri = marshalOptions.responseMarshalerForGetAnycarEstimateCache(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_DelAnycarEstimateCache_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_DelAnycarEstimateCache_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForDelAnycarEstimateCache != nil {
			ri = marshalOptions.responseMarshalerForDelAnycarEstimateCache(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_CombinedTravelEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_CombinedTravelEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForCombinedTravelEstimate != nil {
			ri = marshalOptions.responseMarshalerForCombinedTravelEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCancelEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCancelEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCancelEstimate != nil {
			ri = marshalOptions.responseMarshalerForPCancelEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PBusinessEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PBusinessEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPBusinessEstimate != nil {
			ri = marshalOptions.responseMarshalerForPBusinessEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PBusinessAnyCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PBusinessAnyCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPBusinessAnyCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPBusinessAnyCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPbdEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPbdEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPbdEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPbdEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPBDAnyCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPBDAnyCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPBDAnyCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPBDAnyCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PAssistantEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PAssistantEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPAssistantEstimate != nil {
			ri = marshalOptions.responseMarshalerForPAssistantEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PHelperEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PHelperEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPHelperEstimate != nil {
			ri = marshalOptions.responseMarshalerForPHelperEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PShuttleBusPageEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PShuttleBusPageEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPShuttleBusPageEstimate != nil {
			ri = marshalOptions.responseMarshalerForPShuttleBusPageEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PShuttleBusGuideBarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PShuttleBusGuideBarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPShuttleBusGuideBarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPShuttleBusGuideBarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PBargainEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PBargainEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPBargainEstimate != nil {
			ri = marshalOptions.responseMarshalerForPBargainEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PHomePageCallCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PHomePageCallCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPHomePageCallCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPHomePageCallCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PHomePageCallCarEstimateV2_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PHomePageCallCarEstimateV2_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPHomePageCallCarEstimateV2 != nil {
			ri = marshalOptions.responseMarshalerForPHomePageCallCarEstimateV2(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCarpoolPrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCarpoolPrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCarpoolPrice != nil {
			ri = marshalOptions.responseMarshalerForPCarpoolPrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PRecCarpoolEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PRecCarpoolEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPRecCarpoolEstimate != nil {
			ri = marshalOptions.responseMarshalerForPRecCarpoolEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCarpoolInvitationEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCarpoolInvitationEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCarpoolInvitationEstimate != nil {
			ri = marshalOptions.responseMarshalerForPCarpoolInvitationEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetCouponPrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetCouponPrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetCouponPrice != nil {
			ri = marshalOptions.responseMarshalerForPGetCouponPrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PServiceEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PServiceEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPServiceEstimate != nil {
			ri = marshalOptions.responseMarshalerForPServiceEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PSFCEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PSFCEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPSFCEstimate != nil {
			ri = marshalOptions.responseMarshalerForPSFCEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PSFCModifyEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PSFCModifyEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPSFCModifyEstimate != nil {
			ri = marshalOptions.responseMarshalerForPSFCModifyEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PSFCSimpleEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PSFCSimpleEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPSFCSimpleEstimate != nil {
			ri = marshalOptions.responseMarshalerForPSFCSimpleEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCompositeTravel_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravel_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravel != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravel(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCompositeTravelV2_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravelV2_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravelV2 != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravelV2(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCompositeTravelPoi_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravelPoi_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravelPoi != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravelPoi(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCompositeTravelV3_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravelV3_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravelV3 != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravelV3(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PCompositeTravelV3Post_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravelV3Post_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravelV3Post != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravelV3Post(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCompositeTravelOptions_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCompositeTravelOptions_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCompositeTravelOptions != nil {
			ri = marshalOptions.responseMarshalerForPCompositeTravelOptions(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PRecommendLanding_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PRecommendLanding_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPRecommendLanding != nil {
			ri = marshalOptions.responseMarshalerForPRecommendLanding(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetDynamicFeeDescList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetDynamicFeeDescList_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetDynamicFeeDescList != nil {
			ri = marshalOptions.responseMarshalerForPGetDynamicFeeDescList(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PGetDynamicFeeDescListPost_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetDynamicFeeDescListPost_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetDynamicFeeDescListPost != nil {
			ri = marshalOptions.responseMarshalerForPGetDynamicFeeDescListPost(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPickOnTimeEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPickOnTimeEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPickOnTimeEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPickOnTimeEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetGuideInfo_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetGuideInfo_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetGuideInfo != nil {
			ri = marshalOptions.responseMarshalerForPGetGuideInfo(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PGetGuideInfoPost_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetGuideInfoPost_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetGuideInfoPost != nil {
			ri = marshalOptions.responseMarshalerForPGetGuideInfoPost(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetFormRealData_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetFormRealData_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetFormRealData != nil {
			ri = marshalOptions.responseMarshalerForPGetFormRealData(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PGetFormRealDataPost_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetFormRealDataPost_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetFormRealDataPost != nil {
			ri = marshalOptions.responseMarshalerForPGetFormRealDataPost(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetEstimateDataByEidList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetEstimateDataByEidList_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetEstimateDataByEidList != nil {
			ri = marshalOptions.responseMarshalerForPGetEstimateDataByEidList(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PRouteEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PRouteEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPRouteEstimate != nil {
			ri = marshalOptions.responseMarshalerForPRouteEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PIntercityMultiEstimateEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PIntercityMultiEstimateEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPIntercityMultiEstimateEstimate != nil {
			ri = marshalOptions.responseMarshalerForPIntercityMultiEstimateEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PIntercityEstimateDetail_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PIntercityEstimateDetail_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPIntercityEstimateDetail != nil {
			ri = marshalOptions.responseMarshalerForPIntercityEstimateDetail(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_POrderBookingPreCancelEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_POrderBookingPreCancelEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPOrderBookingPreCancelEstimate != nil {
			ri = marshalOptions.responseMarshalerForPOrderBookingPreCancelEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PInvitedEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PInvitedEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPInvitedEstimate != nil {
			ri = marshalOptions.responseMarshalerForPInvitedEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PMultiPointEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PMultiPointEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPMultiPointEstimate != nil {
			ri = marshalOptions.responseMarshalerForPMultiPointEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetEstimateDataWithoutRender_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetEstimateDataWithoutRender_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetEstimateDataWithoutRender != nil {
			ri = marshalOptions.responseMarshalerForPGetEstimateDataWithoutRender(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetEstimateDataWithoutRenderV2_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetEstimateDataWithoutRenderV2_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetEstimateDataWithoutRenderV2 != nil {
			ri = marshalOptions.responseMarshalerForPGetEstimateDataWithoutRenderV2(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetBusinessFormRealData_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetBusinessFormRealData_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetBusinessFormRealData != nil {
			ri = marshalOptions.responseMarshalerForPGetBusinessFormRealData(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PBargainRangeEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PBargainRangeEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPBargainRangeEstimate != nil {
			ri = marshalOptions.responseMarshalerForPBargainRangeEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PStationBusInsteadOrderEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PStationBusInsteadOrderEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPStationBusInsteadOrderEstimate != nil {
			ri = marshalOptions.responseMarshalerForPStationBusInsteadOrderEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetIntercityBasicFeeLite_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetIntercityBasicFeeLite_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetIntercityBasicFeeLite != nil {
			ri = marshalOptions.responseMarshalerForPGetIntercityBasicFeeLite(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PDidiMiniEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PDidiMiniEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPDidiMiniEstimate != nil {
			ri = marshalOptions.responseMarshalerForPDidiMiniEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PEngageCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PEngageCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPEngageCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPEngageCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PPetsTravelEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPetsTravelEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPetsTravelEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPetsTravelEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_POverseaEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_POverseaEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPOverseaEstimate != nil {
			ri = marshalOptions.responseMarshalerForPOverseaEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PEstimateByOrder_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PEstimateByOrder_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPEstimateByOrder != nil {
			ri = marshalOptions.responseMarshalerForPEstimateByOrder(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_EstimateOrderWithoutRender_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_EstimateOrderWithoutRender_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForEstimateOrderWithoutRender != nil {
			ri = marshalOptions.responseMarshalerForEstimateOrderWithoutRender(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPbdStationBusMultiStationPrice_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPbdStationBusMultiStationPrice_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPbdStationBusMultiStationPrice != nil {
			ri = marshalOptions.responseMarshalerForPPbdStationBusMultiStationPrice(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPbdStationBusOrderEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPbdStationBusOrderEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPbdStationBusOrderEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPbdStationBusOrderEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPbdStationBusMultiEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPbdStationBusMultiEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPbdStationBusMultiEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPbdStationBusMultiEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPbdStationBusDetailEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPbdStationBusDetailEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPbdStationBusDetailEstimate != nil {
			ri = marshalOptions.responseMarshalerForPPbdStationBusDetailEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCharterHomePage_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCharterHomePage_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCharterHomePage != nil {
			ri = marshalOptions.responseMarshalerForPCharterHomePage(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PCharterMultiEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PCharterMultiEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPCharterMultiEstimate != nil {
			ri = marshalOptions.responseMarshalerForPCharterMultiEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PPassengerBeginCharge_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PPassengerBeginCharge_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPPassengerBeginCharge != nil {
			ri = marshalOptions.responseMarshalerForPPassengerBeginCharge(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetRentInfo_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetRentInfo_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetRentInfo != nil {
			ri = marshalOptions.responseMarshalerForPGetRentInfo(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PBusinessRentEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PBusinessRentEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPBusinessRentEstimate != nil {
			ri = marshalOptions.responseMarshalerForPBusinessRentEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PEstimateCarpoolMultiOrder_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PEstimateCarpoolMultiOrder_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPEstimateCarpoolMultiOrder != nil {
			ri = marshalOptions.responseMarshalerForPEstimateCarpoolMultiOrder(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_PEstimateV3_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PEstimateV3_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPEstimateV3 != nil {
			ri = marshalOptions.responseMarshalerForPEstimateV3(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PGetBusinessTailorService_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PGetBusinessTailorService_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPGetBusinessTailorService != nil {
			ri = marshalOptions.responseMarshalerForPGetBusinessTailorService(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("GET", pattern_Mamba_PetAnyCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_PetAnyCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForPetAnyCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForPetAnyCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	mux.Handle("POST", pattern_Mamba_MeetingCarEstimate_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()

		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)

		var defaultHttpCode = 200
		ctx = context.WithValue(ctx, reqKey, req)
		ctx = context.WithValue(ctx, respHeaderKey, http.Header{})
		ctx = context.WithValue(ctx, respHttpCodeKey, &defaultHttpCode)
		resp, err := local_request_Mamba_MeetingCarEstimate_0(ctx, inboundMarshaler, server, req, pathParams)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		contentType := outboundMarshaler.ContentType()
		w.Header().Set("Content-Type", contentType)
		handleForwardHeader(ctx, w)

		var ri interface{} = resp
		if marshalOptions.responseMarshalerForMeetingCarEstimate != nil {
			ri = marshalOptions.responseMarshalerForMeetingCarEstimate(ctx, resp)
		}

		buf, err := outboundMarshaler.Marshal(ri)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		code := ctx.Value(respHttpCodeKey)
		if httpCode, ok := code.(*int); ok {
			w.WriteHeader(*httpCode)
		}

		if _, err = w.Write(buf); err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
		}
	})

	return nil
}

var (
	pattern_Mamba_Ping_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0}, []string{"ping"}, ""))

	pattern_Mamba_PCarpoolEstimatePrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCarpoolEstimatePrice"}, ""))

	pattern_Mamba_IntercityEstimatePrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pIntercityEstimate"}, ""))

	pattern_Mamba_MiniBusEstimatePrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pMiniBusEstimate"}, ""))

	pattern_Mamba_AnyCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pAnycarEstimate"}, ""))

	pattern_Mamba_AnyCarEstimateV3_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pAnycarEstimateV3"}, ""))

	pattern_Mamba_AnyCarEstimateV4_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pAnycarEstimateV4"}, ""))

	pattern_Mamba_GetAnycarEstimateCache_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetAnycarEstimateCache"}, ""))

	pattern_Mamba_DelAnycarEstimateCache_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pDelAnycarEstimateCache"}, ""))

	pattern_Mamba_CombinedTravelEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCombinedTravelEstimate"}, ""))

	pattern_Mamba_PCancelEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCancelEstimate"}, ""))

	pattern_Mamba_PBusinessEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pBusinessEstimate"}, ""))

	pattern_Mamba_PBusinessAnyCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pBusinessAnyCarEstimate"}, ""))

	pattern_Mamba_PPbdEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pPbdEstimate"}, ""))

	pattern_Mamba_PPBDAnyCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pPbdAnyCarEstimate"}, ""))

	pattern_Mamba_PAssistantEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pAssistantEstimate"}, ""))

	pattern_Mamba_PHelperEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pHelperEstimate"}, ""))

	pattern_Mamba_PShuttleBusPageEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pShuttleBusPageEstimate"}, ""))

	pattern_Mamba_PShuttleBusGuideBarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pShuttleBusGuideBarEstimate"}, ""))

	pattern_Mamba_PBargainEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pBargainEstimate"}, ""))

	pattern_Mamba_PHomePageCallCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pHomePageCallCarEstimate"}, ""))

	pattern_Mamba_PHomePageCallCarEstimateV2_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pHomePageCallCarEstimateV2"}, ""))

	pattern_Mamba_PCarpoolPrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "v1", "internal", "pCarpoolPrice"}, ""))

	pattern_Mamba_PRecCarpoolEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pRecCarpoolEstimate"}, ""))

	pattern_Mamba_PCarpoolInvitationEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCarpoolInvitationEstimate"}, ""))

	pattern_Mamba_PGetCouponPrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetCouponPrice"}, ""))

	pattern_Mamba_PServiceEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pServiceEstimate"}, ""))

	pattern_Mamba_PSFCEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pSFCEstimate"}, ""))

	pattern_Mamba_PSFCModifyEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pSFCModifyEstimate"}, ""))

	pattern_Mamba_PSFCSimpleEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pSFCSimpleEstimate"}, ""))

	pattern_Mamba_PCompositeTravel_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravel"}, ""))

	pattern_Mamba_PCompositeTravelV2_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravelV2"}, ""))

	pattern_Mamba_PCompositeTravelPoi_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravelPoi"}, ""))

	pattern_Mamba_PCompositeTravelV3_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravelV3"}, ""))

	pattern_Mamba_PCompositeTravelV3Post_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravelV3"}, ""))

	pattern_Mamba_PCompositeTravelOptions_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCompositeTravelOptions"}, ""))

	pattern_Mamba_PRecommendLanding_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pRecommendLanding"}, ""))

	pattern_Mamba_PGetDynamicFeeDescList_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetDynamicFeeDescList"}, ""))

	pattern_Mamba_PGetDynamicFeeDescListPost_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetDynamicFeeDescList"}, ""))

	pattern_Mamba_PPickOnTimeEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pPickOnTimeEstimate"}, ""))

	pattern_Mamba_PGetGuideInfo_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "v1", "internal", "pGetGuideInfo"}, ""))

	pattern_Mamba_PGetGuideInfoPost_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "v1", "internal", "pGetGuideInfo"}, ""))

	pattern_Mamba_PGetFormRealData_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetFormRealData"}, ""))

	pattern_Mamba_PGetFormRealDataPost_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetFormRealData"}, ""))

	pattern_Mamba_PGetEstimateDataByEidList_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetEstimateDataByEidList"}, ""))

	pattern_Mamba_PRouteEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pRouteEstimate"}, ""))

	pattern_Mamba_PIntercityMultiEstimateEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pIntercityMultiEstimate"}, ""))

	pattern_Mamba_PIntercityEstimateDetail_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pIntercityEstimateDetail"}, ""))

	pattern_Mamba_POrderBookingPreCancelEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pOrderBookingPreCancelEstimate"}, ""))

	pattern_Mamba_PInvitedEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pInvitedEstimate"}, ""))

	pattern_Mamba_PMultiPointEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pMultiPointEstimate"}, ""))

	pattern_Mamba_PGetEstimateDataWithoutRender_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetEstimateDataWithoutRender"}, ""))

	pattern_Mamba_PGetEstimateDataWithoutRenderV2_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "v1", "internal", "pGetEstimateDataWithoutRender"}, ""))

	pattern_Mamba_PGetBusinessFormRealData_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "v1", "internal", "pGetBusinessFormRealData"}, ""))

	pattern_Mamba_PBargainRangeEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pBargainRangeEstimate"}, ""))

	pattern_Mamba_PStationBusInsteadOrderEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pStationBusInsteadOrderEstimate"}, ""))

	pattern_Mamba_PGetIntercityBasicFeeLite_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetIntercityBasicFeeLite"}, ""))

	pattern_Mamba_PDidiMiniEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pDidiMiniEstimate"}, ""))

	pattern_Mamba_PEngageCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pEngageCarEstimate"}, ""))

	pattern_Mamba_PPetsTravelEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pPetsTravelEstimate"}, ""))

	pattern_Mamba_POverseaEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pOverseaEstimate"}, ""))

	pattern_Mamba_PEstimateByOrder_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pEstimateByOrder"}, ""))

	pattern_Mamba_EstimateOrderWithoutRender_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "estimateOrderWithoutRender"}, ""))

	pattern_Mamba_PPbdStationBusMultiStationPrice_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "PPbdStationBusMultiStationPrice"}, ""))

	pattern_Mamba_PPbdStationBusOrderEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "PPbdStationBusOrderEstimate"}, ""))

	pattern_Mamba_PPbdStationBusMultiEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "PPbdStationBusMultiEstimate"}, ""))

	pattern_Mamba_PPbdStationBusDetailEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "PPbdStationBusDetailEstimate"}, ""))

	pattern_Mamba_PCharterHomePage_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCharterHomePage"}, ""))

	pattern_Mamba_PCharterMultiEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pCharterMultiEstimate"}, ""))

	pattern_Mamba_PPassengerBeginCharge_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pPassengerBeginCharge"}, ""))

	pattern_Mamba_PGetRentInfo_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetRentInfo"}, ""))

	pattern_Mamba_PBusinessRentEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pBusinessRentEstimate"}, ""))

	pattern_Mamba_PEstimateCarpoolMultiOrder_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pEstimateCarpoolMultiOrder"}, ""))

	pattern_Mamba_PEstimateV3_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pEstimateV3"}, ""))

	pattern_Mamba_PGetBusinessTailorService_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "pGetBusinessTailorService"}, ""))

	pattern_Mamba_PetAnyCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3}, []string{"gulfstream", "mamba", "v1", "petAnyCarEstimate"}, ""))

	pattern_Mamba_MeetingCarEstimate_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"gulfstream", "mamba", "inner", "v1", "meetingCarEstimate"}, ""))
)
