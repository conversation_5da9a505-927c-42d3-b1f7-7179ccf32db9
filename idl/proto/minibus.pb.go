// Code generated by http-gen compiler. DO NOT EDIT.
// source: minibus.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*MiniBusEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusEstimateResp)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*TokenInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*EstimateFromData)(nil)))
	json.Pretouch(reflect.TypeOf((*TP)(nil)))
	json.Pretouch(reflect.TypeOf((*FeeDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusExtraMap)(nil)))
	json.Pretouch(reflect.TypeOf((*SmartBusStationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusCarpoolSeatModule)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusPluginPageInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ConfirmButton)(nil)))
	json.Pretouch(reflect.TypeOf((*DynamicEffectParams)(nil)))
	json.Pretouch(reflect.TypeOf((*ShowOmega)(nil)))
	json.Pretouch(reflect.TypeOf((*MiniBusOmegaInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*TPShowOmega)(nil)))
	json.Pretouch(reflect.TypeOf((*SmartBusNewOrderParam)(nil)))
	json.Pretouch(reflect.TypeOf((*SideParam)(nil)))
}

type MiniBusEstimateRequest struct {
	Lang                   string   `json:"lang" form:"lang"`                   //端语言
	Token                  string   `json:"token" form:"token"`                 //用户认证token
	AccessKeyId            int32    `json:"access_key_id" form:"access_key_id"` //端来源
	AppVersion             string   `json:"app_version" form:"app_version"`     //端版本
	Channel                string   `json:"channel" form:"channel"`             //渠道号
	Xpsid                  *string  `json:"xpsid,omitempty" form:"xpsid"`       //渠道号
	XpsidRoot              *string  `json:"xpsid_root,omitempty" form:"xpsid_root"`
	CarpoolSeatNum         int32    `json:"carpool_seat_num" form:"carpool_seat_num"` //选择的座位数
	ClientType             int32    `json:"client_type" form:"client_type"`           //端类型
	FromLng                float64  `json:"from_lng" form:"from_lng"`                 //用户当前经度
	FromLat                float64  `json:"from_lat" form:"from_lat"`                 //用户当前纬度
	FromAddress            string   `json:"from_address" form:"from_address"`         //用户当前纬度
	FromName               string   `json:"from_name" form:"from_name"`
	FromPoiId              string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType            string   `json:"from_poi_type" form:"from_poi_type"`
	Lat                    float64  `json:"lat" form:"lat"`                     //定位点
	Lng                    float64  `json:"lng" form:"lng"`                     //定位点
	MapType                string   `json:"map_type" form:"map_type"`           //地图类型
	PlatformType           int32    `json:"platform_type" form:"platform_type"` //端(平台)类型-2
	PaymentsType           int32    `json:"payments_type" form:"payments_type"` //选择的支付方式
	ToAddress              string   `json:"to_address" form:"to_address"`       //选择的支付方式
	ToLat                  float64  `json:"to_lat" form:"to_lat"`               //终点
	ToLng                  float64  `json:"to_lng" form:"to_lng"`               //终点
	ToName                 string   `json:"to_name" form:"to_name"`
	ToPoiId                string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType              string   `json:"to_poi_type" form:"to_poi_type"`
	UserType               UserType `json:"user_type" form:"user_type"`                     //1普通用户；2企业用户
	ChooseFSearchid        string   `json:"choose_f_searchid" form:"choose_f_searchid"`     //用户选择起点请求ID
	ChooseTSearchid        string   `json:"choose_t_searchid" form:"choose_t_searchid"`     //用户选择终点请求ID
	MapinfoCacheToken      string   `json:"mapinfo_cache_token" form:"mapinfo_cache_token"` //用户选择终点请求ID
	FromType               int16    `json:"from_type" form:"from_type"`
	MultiRequireProduct    string   `json:"multi_require_product" form:"multi_require_product"`         //二次预估
	MapinfoStartCacheToken string   `json:"mapinfo_start_cache_token" form:"mapinfo_start_cache_token"` //二次预估
	MapinfoDestCacheToken  string   `json:"mapinfo_dest_cache_token" form:"mapinfo_dest_cache_token"`
	PageType               *int32   `json:"page_type,omitempty" form:"page_type"`         //页面类型，51: 智能小巴
	Dchn                   *string  `json:"dchn,omitempty" form:"dchn"`                   //终端来源
	TransData              *string  `json:"trans_data,omitempty" form:"trans_data"`       //透传数据，端根据link上 trans_data 透传
	StartStationInfo       string   `json:"start_station_info" form:"start_station_info"` //起点站点信息
	DestStationInfo        string   `json:"dest_station_info" form:"dest_station_info"`   //终点站点信息
	DriverId               int64    `json:"driver_id" form:"driver_id"`                   //司机id
	ExtMap                 string   `json:"ext_map" form:"ext_map"`                       //扩展字段  "{}"，不影响预估流程
}

func (x *MiniBusEstimateRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *MiniBusEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *MiniBusEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *MiniBusEstimateRequest) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *MiniBusEstimateRequest) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *MiniBusEstimateRequest) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *MiniBusEstimateRequest) GetCarpoolSeatNum() (r int32) {
	if x != nil {
		return x.CarpoolSeatNum
	}
	return r
}

func (x *MiniBusEstimateRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *MiniBusEstimateRequest) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *MiniBusEstimateRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *MiniBusEstimateRequest) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetUserType() (r UserType) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *MiniBusEstimateRequest) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *MiniBusEstimateRequest) GetMapinfoCacheToken() (r string) {
	if x != nil {
		return x.MapinfoCacheToken
	}
	return r
}

func (x *MiniBusEstimateRequest) GetFromType() (r int16) {
	if x != nil {
		return x.FromType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetMultiRequireProduct() (r string) {
	if x != nil {
		return x.MultiRequireProduct
	}
	return r
}

func (x *MiniBusEstimateRequest) GetMapinfoStartCacheToken() (r string) {
	if x != nil {
		return x.MapinfoStartCacheToken
	}
	return r
}

func (x *MiniBusEstimateRequest) GetMapinfoDestCacheToken() (r string) {
	if x != nil {
		return x.MapinfoDestCacheToken
	}
	return r
}

func (x *MiniBusEstimateRequest) GetPageType() (r int32) {
	if x != nil && x.PageType != nil {
		return *x.PageType
	}
	return r
}

func (x *MiniBusEstimateRequest) GetDchn() (r string) {
	if x != nil && x.Dchn != nil {
		return *x.Dchn
	}
	return r
}

func (x *MiniBusEstimateRequest) GetTransData() (r string) {
	if x != nil && x.TransData != nil {
		return *x.TransData
	}
	return r
}

func (x *MiniBusEstimateRequest) GetStartStationInfo() (r string) {
	if x != nil {
		return x.StartStationInfo
	}
	return r
}

func (x *MiniBusEstimateRequest) GetDestStationInfo() (r string) {
	if x != nil {
		return x.DestStationInfo
	}
	return r
}

func (x *MiniBusEstimateRequest) GetDriverId() (r int64) {
	if x != nil {
		return x.DriverId
	}
	return r
}

func (x *MiniBusEstimateRequest) GetExtMap() (r string) {
	if x != nil {
		return x.ExtMap
	}
	return r
}

func (x *MiniBusEstimateRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *MiniBusEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *MiniBusEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *MiniBusEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *MiniBusEstimateRequest) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *MiniBusEstimateRequest) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *MiniBusEstimateRequest) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (x *MiniBusEstimateRequest) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = v
	}
}

func (x *MiniBusEstimateRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *MiniBusEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *MiniBusEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *MiniBusEstimateRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *MiniBusEstimateRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *MiniBusEstimateRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *MiniBusEstimateRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *MiniBusEstimateRequest) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *MiniBusEstimateRequest) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *MiniBusEstimateRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *MiniBusEstimateRequest) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *MiniBusEstimateRequest) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *MiniBusEstimateRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *MiniBusEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *MiniBusEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *MiniBusEstimateRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *MiniBusEstimateRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *MiniBusEstimateRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *MiniBusEstimateRequest) SetUserType(v UserType) {
	if x != nil {
		x.UserType = v
	}
}

func (x *MiniBusEstimateRequest) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *MiniBusEstimateRequest) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *MiniBusEstimateRequest) SetMapinfoCacheToken(v string) {
	if x != nil {
		x.MapinfoCacheToken = v
	}
}

func (x *MiniBusEstimateRequest) SetFromType(v int16) {
	if x != nil {
		x.FromType = v
	}
}

func (x *MiniBusEstimateRequest) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = v
	}
}

func (x *MiniBusEstimateRequest) SetMapinfoStartCacheToken(v string) {
	if x != nil {
		x.MapinfoStartCacheToken = v
	}
}

func (x *MiniBusEstimateRequest) SetMapinfoDestCacheToken(v string) {
	if x != nil {
		x.MapinfoDestCacheToken = v
	}
}

func (x *MiniBusEstimateRequest) SetPageType(v int32) {
	if x != nil {
		x.PageType = &v
	}
}

func (x *MiniBusEstimateRequest) SetDchn(v string) {
	if x != nil {
		x.Dchn = &v
	}
}

func (x *MiniBusEstimateRequest) SetTransData(v string) {
	if x != nil {
		x.TransData = &v
	}
}

func (x *MiniBusEstimateRequest) SetStartStationInfo(v string) {
	if x != nil {
		x.StartStationInfo = v
	}
}

func (x *MiniBusEstimateRequest) SetDestStationInfo(v string) {
	if x != nil {
		x.DestStationInfo = v
	}
}

func (x *MiniBusEstimateRequest) SetDriverId(v int64) {
	if x != nil {
		x.DriverId = v
	}
}

func (x *MiniBusEstimateRequest) SetExtMap(v string) {
	if x != nil {
		x.ExtMap = v
	}
}

func (p *MiniBusEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusEstimateRequest(%+v)", *p)
}

type MiniBusEstimateResp struct {
	Errno  int32                `json:"errno" form:"errno"`
	Errmsg string               `json:"errmsg" form:"errmsg"`
	Data   *MiniBusEstimateData `json:"data,omitempty" form:"data"`
}

func (x *MiniBusEstimateResp) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *MiniBusEstimateResp) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *MiniBusEstimateResp) GetData() (r *MiniBusEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *MiniBusEstimateResp) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *MiniBusEstimateResp) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *MiniBusEstimateResp) SetData(v *MiniBusEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (p *MiniBusEstimateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusEstimateResp(%+v)", *p)
}

type MiniBusEstimateData struct {
	EstimateData            []*EstimateFromData       `json:"estimate_data,omitempty" form:"estimate_data"`
	Title                   string                    `json:"title" form:"title"`
	Subtitle                string                    `json:"subtitle" form:"subtitle"`
	CarpoolSeatModule       *MiniBusCarpoolSeatModule `json:"carpool_seat_module,omitempty" form:"carpool_seat_module"`
	IsSupportMultiSelection int16                     `json:"is_support_multi_selection" form:"is_support_multi_selection"`
	PageType                int16                     `json:"page_type" form:"page_type"`
	FeeDetailUrl            string                    `json:"fee_detail_url" form:"fee_detail_url"`
	PluginPageInfo          *MiniBusPluginPageInfo    `json:"plugin_page_info,omitempty" form:"plugin_page_info"`
	UserPayInfo             *PaymentOptionModule      `json:"user_pay_info,omitempty" form:"user_pay_info"`
	ConfirmButton           *ConfirmButton            `json:"confirm_button,omitempty" form:"confirm_button"`
	DynamicEffectParams     *DynamicEffectParams      `json:"dynamic_effect_params,omitempty" form:"dynamic_effect_params"`
	BackButton              *BackButton               `json:"back_button,omitempty" form:"back_button"`
	EstimateTraceId         string                    `json:"estimate_trace_id" form:"estimate_trace_id"`
	TokenInfo               *TokenInfo                `json:"token_info,omitempty" form:"token_info"`
	MapCurveInfo            *MapCurveInfo             `json:"map_curve_info,omitempty" form:"map_curve_info"`
	BestViewType            int32                     `json:"best_view_type" form:"best_view_type"`
	OmegaInfo               *MiniBusOmegaInfo         `json:"omega_info,omitempty" form:"omega_info"`
	Icon                    *string                   `json:"icon,omitempty" form:"icon"`
	PreMatchType            *int32                    `json:"pre_match_type,omitempty" form:"pre_match_type"`
	PneworderParams         *SmartBusNewOrderParam    `json:"pneworder_params,omitempty" form:"pneworder_params"`
	DisableBestViewSelect   int32                     `json:"disable_best_view_select" form:"disable_best_view_select"`
	StartStationInfo        *SmartBusStationInfo      `json:"start_station_info,omitempty" form:"start_station_info"`         //无TP时 地图站牌展示使用
	DestStationInfo         *SmartBusStationInfo      `json:"dest_station_info,omitempty" form:"dest_station_info"`           //无TP时 地图站牌展示使用
	SmartBusPageVersion     *string                   `json:"smart_bus_page_version,omitempty" form:"smart_bus_page_version"` //智能小巴页面版本【na: 7.0.6, wx: 6.10.25以后使用】
	RefreshInterval         *int32                    `json:"refresh_interval,omitempty" form:"refresh_interval"`             //刷新间隔
	MinibusType             *int32                    `json:"minibus_type,omitempty" form:"minibus_type"`                     //刷新间隔
}

func (x *MiniBusEstimateData) GetEstimateData() (r []*EstimateFromData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *MiniBusEstimateData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *MiniBusEstimateData) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *MiniBusEstimateData) GetCarpoolSeatModule() (r *MiniBusCarpoolSeatModule) {
	if x != nil {
		return x.CarpoolSeatModule
	}
	return r
}

func (x *MiniBusEstimateData) GetIsSupportMultiSelection() (r int16) {
	if x != nil {
		return x.IsSupportMultiSelection
	}
	return r
}

func (x *MiniBusEstimateData) GetPageType() (r int16) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *MiniBusEstimateData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *MiniBusEstimateData) GetPluginPageInfo() (r *MiniBusPluginPageInfo) {
	if x != nil {
		return x.PluginPageInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetConfirmButton() (r *ConfirmButton) {
	if x != nil {
		return x.ConfirmButton
	}
	return r
}

func (x *MiniBusEstimateData) GetDynamicEffectParams() (r *DynamicEffectParams) {
	if x != nil {
		return x.DynamicEffectParams
	}
	return r
}

func (x *MiniBusEstimateData) GetBackButton() (r *BackButton) {
	if x != nil {
		return x.BackButton
	}
	return r
}

func (x *MiniBusEstimateData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *MiniBusEstimateData) GetTokenInfo() (r *TokenInfo) {
	if x != nil {
		return x.TokenInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetMapCurveInfo() (r *MapCurveInfo) {
	if x != nil {
		return x.MapCurveInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetBestViewType() (r int32) {
	if x != nil {
		return x.BestViewType
	}
	return r
}

func (x *MiniBusEstimateData) GetOmegaInfo() (r *MiniBusOmegaInfo) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetIcon() (r string) {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return r
}

func (x *MiniBusEstimateData) GetPreMatchType() (r int32) {
	if x != nil && x.PreMatchType != nil {
		return *x.PreMatchType
	}
	return r
}

func (x *MiniBusEstimateData) GetPneworderParams() (r *SmartBusNewOrderParam) {
	if x != nil {
		return x.PneworderParams
	}
	return r
}

func (x *MiniBusEstimateData) GetDisableBestViewSelect() (r int32) {
	if x != nil {
		return x.DisableBestViewSelect
	}
	return r
}

func (x *MiniBusEstimateData) GetStartStationInfo() (r *SmartBusStationInfo) {
	if x != nil {
		return x.StartStationInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetDestStationInfo() (r *SmartBusStationInfo) {
	if x != nil {
		return x.DestStationInfo
	}
	return r
}

func (x *MiniBusEstimateData) GetSmartBusPageVersion() (r string) {
	if x != nil && x.SmartBusPageVersion != nil {
		return *x.SmartBusPageVersion
	}
	return r
}

func (x *MiniBusEstimateData) GetRefreshInterval() (r int32) {
	if x != nil && x.RefreshInterval != nil {
		return *x.RefreshInterval
	}
	return r
}

func (x *MiniBusEstimateData) GetMinibusType() (r int32) {
	if x != nil && x.MinibusType != nil {
		return *x.MinibusType
	}
	return r
}

func (x *MiniBusEstimateData) SetEstimateData(v []*EstimateFromData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *MiniBusEstimateData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *MiniBusEstimateData) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *MiniBusEstimateData) SetCarpoolSeatModule(v *MiniBusCarpoolSeatModule) {
	if x != nil {
		x.CarpoolSeatModule = v
	}
}

func (x *MiniBusEstimateData) SetIsSupportMultiSelection(v int16) {
	if x != nil {
		x.IsSupportMultiSelection = v
	}
}

func (x *MiniBusEstimateData) SetPageType(v int16) {
	if x != nil {
		x.PageType = v
	}
}

func (x *MiniBusEstimateData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *MiniBusEstimateData) SetPluginPageInfo(v *MiniBusPluginPageInfo) {
	if x != nil {
		x.PluginPageInfo = v
	}
}

func (x *MiniBusEstimateData) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *MiniBusEstimateData) SetConfirmButton(v *ConfirmButton) {
	if x != nil {
		x.ConfirmButton = v
	}
}

func (x *MiniBusEstimateData) SetDynamicEffectParams(v *DynamicEffectParams) {
	if x != nil {
		x.DynamicEffectParams = v
	}
}

func (x *MiniBusEstimateData) SetBackButton(v *BackButton) {
	if x != nil {
		x.BackButton = v
	}
}

func (x *MiniBusEstimateData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *MiniBusEstimateData) SetTokenInfo(v *TokenInfo) {
	if x != nil {
		x.TokenInfo = v
	}
}

func (x *MiniBusEstimateData) SetMapCurveInfo(v *MapCurveInfo) {
	if x != nil {
		x.MapCurveInfo = v
	}
}

func (x *MiniBusEstimateData) SetBestViewType(v int32) {
	if x != nil {
		x.BestViewType = v
	}
}

func (x *MiniBusEstimateData) SetOmegaInfo(v *MiniBusOmegaInfo) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (x *MiniBusEstimateData) SetIcon(v string) {
	if x != nil {
		x.Icon = &v
	}
}

func (x *MiniBusEstimateData) SetPreMatchType(v int32) {
	if x != nil {
		x.PreMatchType = &v
	}
}

func (x *MiniBusEstimateData) SetPneworderParams(v *SmartBusNewOrderParam) {
	if x != nil {
		x.PneworderParams = v
	}
}

func (x *MiniBusEstimateData) SetDisableBestViewSelect(v int32) {
	if x != nil {
		x.DisableBestViewSelect = v
	}
}

func (x *MiniBusEstimateData) SetStartStationInfo(v *SmartBusStationInfo) {
	if x != nil {
		x.StartStationInfo = v
	}
}

func (x *MiniBusEstimateData) SetDestStationInfo(v *SmartBusStationInfo) {
	if x != nil {
		x.DestStationInfo = v
	}
}

func (x *MiniBusEstimateData) SetSmartBusPageVersion(v string) {
	if x != nil {
		x.SmartBusPageVersion = &v
	}
}

func (x *MiniBusEstimateData) SetRefreshInterval(v int32) {
	if x != nil {
		x.RefreshInterval = &v
	}
}

func (x *MiniBusEstimateData) SetMinibusType(v int32) {
	if x != nil {
		x.MinibusType = &v
	}
}

func (p *MiniBusEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusEstimateData(%+v)", *p)
}

type TokenInfo struct {
	MapinfoStartCacheToken string `json:"mapinfo_start_cache_token" form:"mapinfo_start_cache_token"`
	MapinfoDestCacheToken  string `json:"mapinfo_dest_cache_token" form:"mapinfo_dest_cache_token"`
}

func (x *TokenInfo) GetMapinfoStartCacheToken() (r string) {
	if x != nil {
		return x.MapinfoStartCacheToken
	}
	return r
}

func (x *TokenInfo) GetMapinfoDestCacheToken() (r string) {
	if x != nil {
		return x.MapinfoDestCacheToken
	}
	return r
}

func (x *TokenInfo) SetMapinfoStartCacheToken(v string) {
	if x != nil {
		x.MapinfoStartCacheToken = v
	}
}

func (x *TokenInfo) SetMapinfoDestCacheToken(v string) {
	if x != nil {
		x.MapinfoDestCacheToken = v
	}
}

func (p *TokenInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TokenInfo(%+v)", *p)
}

type EstimateFromData struct {
	EstimateId        string                 `json:"estimate_id" form:"estimate_id"`
	IsSelected        int16                  `json:"is_selected" form:"is_selected"`
	UserPayInfo       *NewFormUserPayInfo    `json:"user_pay_info,omitempty" form:"user_pay_info"`
	Tp                *TP                    `json:"tp,omitempty" form:"tp"`
	ExtraMap          *MiniBusExtraMap       `json:"extra_map,omitempty" form:"extra_map"`
	StartStationInfo  *SmartBusStationInfo   `json:"start_station_info,omitempty" form:"start_station_info"`
	DestStationInfo   *SmartBusStationInfo   `json:"dest_station_info,omitempty" form:"dest_station_info"`
	ConfirmSubtitle   *string                `json:"confirm_subtitle,omitempty" form:"confirm_subtitle"` //确认按钮副标题，切换TP会变化【na: 7.0.6, wx: 6.10.25以后使用】
	SideParams        *SideParam             `json:"side_params,omitempty" form:"side_params"`           //带给sideEstimate的参数【na: 7.0.6, wx: 6.10.25以后使用】
	PreMatchType      int32                  `json:"pre_match_type" form:"pre_match_type"`               //带给sideEstimate的参数【na: 7.0.6, wx: 6.10.25以后使用】
	BestViewType      int32                  `json:"best_view_type" form:"best_view_type"`
	ApproachPointList []*SmartBusStationInfo `json:"approach_point_list,omitempty" form:"approach_point_list"`
	OmegaInfo         *TPShowOmega           `json:"omega_info,omitempty" form:"omega_info"`
}

func (x *EstimateFromData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *EstimateFromData) GetIsSelected() (r int16) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *EstimateFromData) GetUserPayInfo() (r *NewFormUserPayInfo) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *EstimateFromData) GetTp() (r *TP) {
	if x != nil {
		return x.Tp
	}
	return r
}

func (x *EstimateFromData) GetExtraMap() (r *MiniBusExtraMap) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *EstimateFromData) GetStartStationInfo() (r *SmartBusStationInfo) {
	if x != nil {
		return x.StartStationInfo
	}
	return r
}

func (x *EstimateFromData) GetDestStationInfo() (r *SmartBusStationInfo) {
	if x != nil {
		return x.DestStationInfo
	}
	return r
}

func (x *EstimateFromData) GetConfirmSubtitle() (r string) {
	if x != nil && x.ConfirmSubtitle != nil {
		return *x.ConfirmSubtitle
	}
	return r
}

func (x *EstimateFromData) GetSideParams() (r *SideParam) {
	if x != nil {
		return x.SideParams
	}
	return r
}

func (x *EstimateFromData) GetPreMatchType() (r int32) {
	if x != nil {
		return x.PreMatchType
	}
	return r
}

func (x *EstimateFromData) GetBestViewType() (r int32) {
	if x != nil {
		return x.BestViewType
	}
	return r
}

func (x *EstimateFromData) GetApproachPointList() (r []*SmartBusStationInfo) {
	if x != nil {
		return x.ApproachPointList
	}
	return r
}

func (x *EstimateFromData) GetOmegaInfo() (r *TPShowOmega) {
	if x != nil {
		return x.OmegaInfo
	}
	return r
}

func (x *EstimateFromData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *EstimateFromData) SetIsSelected(v int16) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *EstimateFromData) SetUserPayInfo(v *NewFormUserPayInfo) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *EstimateFromData) SetTp(v *TP) {
	if x != nil {
		x.Tp = v
	}
}

func (x *EstimateFromData) SetExtraMap(v *MiniBusExtraMap) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *EstimateFromData) SetStartStationInfo(v *SmartBusStationInfo) {
	if x != nil {
		x.StartStationInfo = v
	}
}

func (x *EstimateFromData) SetDestStationInfo(v *SmartBusStationInfo) {
	if x != nil {
		x.DestStationInfo = v
	}
}

func (x *EstimateFromData) SetConfirmSubtitle(v string) {
	if x != nil {
		x.ConfirmSubtitle = &v
	}
}

func (x *EstimateFromData) SetSideParams(v *SideParam) {
	if x != nil {
		x.SideParams = v
	}
}

func (x *EstimateFromData) SetPreMatchType(v int32) {
	if x != nil {
		x.PreMatchType = v
	}
}

func (x *EstimateFromData) SetBestViewType(v int32) {
	if x != nil {
		x.BestViewType = v
	}
}

func (x *EstimateFromData) SetApproachPointList(v []*SmartBusStationInfo) {
	if x != nil {
		x.ApproachPointList = v
	}
}

func (x *EstimateFromData) SetOmegaInfo(v *TPShowOmega) {
	if x != nil {
		x.OmegaInfo = v
	}
}

func (p *EstimateFromData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimateFromData(%+v)", *p)
}

type TP struct {
	FeeAmount      float64           `json:"fee_amount" form:"fee_amount"`
	FeeMsg         string            `json:"fee_msg" form:"fee_msg"`
	NoticeTitle    string            `json:"notice_title" form:"notice_title"`
	NoticeSubtitle string            `json:"notice_subtitle" form:"notice_subtitle"`
	Tag            string            `json:"tag" form:"tag"`
	FeeDescList    []*NewFormFeeDesc `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	NoticeText     *string           `json:"notice_text,omitempty" form:"notice_text"`       //TP卡片下层文案【na: 7.0.6, wx: 6.10.25以后使用】
	NoticeSubtext  *string           `json:"notice_subtext,omitempty" form:"notice_subtext"` //TP卡片下层副文案【na: 7.0.6, wx: 6.10.25以后使用】
	SubTag         *string           `json:"sub_tag,omitempty" form:"sub_tag"`               //TP卡片上层标签【na: 7.0.6, wx: 6.10.25以后使用】
}

func (x *TP) GetFeeAmount() (r float64) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *TP) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *TP) GetNoticeTitle() (r string) {
	if x != nil {
		return x.NoticeTitle
	}
	return r
}

func (x *TP) GetNoticeSubtitle() (r string) {
	if x != nil {
		return x.NoticeSubtitle
	}
	return r
}

func (x *TP) GetTag() (r string) {
	if x != nil {
		return x.Tag
	}
	return r
}

func (x *TP) GetFeeDescList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *TP) GetNoticeText() (r string) {
	if x != nil && x.NoticeText != nil {
		return *x.NoticeText
	}
	return r
}

func (x *TP) GetNoticeSubtext() (r string) {
	if x != nil && x.NoticeSubtext != nil {
		return *x.NoticeSubtext
	}
	return r
}

func (x *TP) GetSubTag() (r string) {
	if x != nil && x.SubTag != nil {
		return *x.SubTag
	}
	return r
}

func (x *TP) SetFeeAmount(v float64) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *TP) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *TP) SetNoticeTitle(v string) {
	if x != nil {
		x.NoticeTitle = v
	}
}

func (x *TP) SetNoticeSubtitle(v string) {
	if x != nil {
		x.NoticeSubtitle = v
	}
}

func (x *TP) SetTag(v string) {
	if x != nil {
		x.Tag = v
	}
}

func (x *TP) SetFeeDescList(v []*NewFormFeeDesc) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *TP) SetNoticeText(v string) {
	if x != nil {
		x.NoticeText = &v
	}
}

func (x *TP) SetNoticeSubtext(v string) {
	if x != nil {
		x.NoticeSubtext = &v
	}
}

func (x *TP) SetSubTag(v string) {
	if x != nil {
		x.SubTag = &v
	}
}

func (p *TP) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TP(%+v)", *p)
}

type FeeDetail struct {
	FeeAmount   float64    `json:"fee_amount" form:"fee_amount"`
	FeeMsg      string     `json:"fee_msg" form:"fee_msg"`
	FeeDescList []*FeeDesc `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
}

func (x *FeeDetail) GetFeeAmount() (r float64) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *FeeDetail) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *FeeDetail) GetFeeDescList() (r []*FeeDesc) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *FeeDetail) SetFeeAmount(v float64) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *FeeDetail) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *FeeDetail) SetFeeDescList(v []*FeeDesc) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (p *FeeDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeDetail(%+v)", *p)
}

type MiniBusExtraMap struct {
	ProductId         int64   `json:"product_id" form:"product_id"`       //产品线ID
	BusinessId        int64   `json:"business_id" form:"business_id"`     //业务线
	Etp               int64   `json:"etp" form:"etp"`                     //etp
	RequireLevel      int64   `json:"require_level" form:"require_level"` //车型
	ComboType         int64   `json:"combo_type" form:"combo_type"`       //combo_type
	LevelType         int32   `json:"level_type" form:"level_type"`       //combo_type
	CarpoolType       int64   `json:"carpool_type" form:"carpool_type"`
	ProductCategory   int64   `json:"product_category" form:"product_category"`
	ComboId           *int64  `json:"combo_id,omitempty" form:"combo_id"`
	DepartureTime     *int64  `json:"departure_time,omitempty" form:"departure_time"`
	BusServiceShiftId *string `json:"bus_service_shift_id,omitempty" form:"bus_service_shift_id"`
}

func (x *MiniBusExtraMap) GetProductId() (r int64) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *MiniBusExtraMap) GetBusinessId() (r int64) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *MiniBusExtraMap) GetEtp() (r int64) {
	if x != nil {
		return x.Etp
	}
	return r
}

func (x *MiniBusExtraMap) GetRequireLevel() (r int64) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *MiniBusExtraMap) GetComboType() (r int64) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *MiniBusExtraMap) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *MiniBusExtraMap) GetCarpoolType() (r int64) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *MiniBusExtraMap) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *MiniBusExtraMap) GetComboId() (r int64) {
	if x != nil && x.ComboId != nil {
		return *x.ComboId
	}
	return r
}

func (x *MiniBusExtraMap) GetDepartureTime() (r int64) {
	if x != nil && x.DepartureTime != nil {
		return *x.DepartureTime
	}
	return r
}

func (x *MiniBusExtraMap) GetBusServiceShiftId() (r string) {
	if x != nil && x.BusServiceShiftId != nil {
		return *x.BusServiceShiftId
	}
	return r
}

func (x *MiniBusExtraMap) SetProductId(v int64) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *MiniBusExtraMap) SetBusinessId(v int64) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *MiniBusExtraMap) SetEtp(v int64) {
	if x != nil {
		x.Etp = v
	}
}

func (x *MiniBusExtraMap) SetRequireLevel(v int64) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *MiniBusExtraMap) SetComboType(v int64) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *MiniBusExtraMap) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *MiniBusExtraMap) SetCarpoolType(v int64) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (x *MiniBusExtraMap) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *MiniBusExtraMap) SetComboId(v int64) {
	if x != nil {
		x.ComboId = &v
	}
}

func (x *MiniBusExtraMap) SetDepartureTime(v int64) {
	if x != nil {
		x.DepartureTime = &v
	}
}

func (x *MiniBusExtraMap) SetBusServiceShiftId(v string) {
	if x != nil {
		x.BusServiceShiftId = &v
	}
}

func (p *MiniBusExtraMap) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusExtraMap(%+v)", *p)
}

type SmartBusStationInfo struct {
	PoiId        string  `json:"poi_id" form:"poi_id"`
	Lat          float64 `json:"lat" form:"lat"`
	Lng          float64 `json:"lng" form:"lng"`
	DisplayName  string  `json:"display_name" form:"display_name"`
	AddressDesc  string  `json:"address_desc" form:"address_desc"`
	LinkId       string  `json:"link_id" form:"link_id"`
	WalkDistance *int32  `json:"walk_distance,omitempty" form:"walk_distance"`
	WalkTime     *int32  `json:"walk_time,omitempty" form:"walk_time"`
}

func (x *SmartBusStationInfo) GetPoiId() (r string) {
	if x != nil {
		return x.PoiId
	}
	return r
}

func (x *SmartBusStationInfo) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *SmartBusStationInfo) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *SmartBusStationInfo) GetDisplayName() (r string) {
	if x != nil {
		return x.DisplayName
	}
	return r
}

func (x *SmartBusStationInfo) GetAddressDesc() (r string) {
	if x != nil {
		return x.AddressDesc
	}
	return r
}

func (x *SmartBusStationInfo) GetLinkId() (r string) {
	if x != nil {
		return x.LinkId
	}
	return r
}

func (x *SmartBusStationInfo) GetWalkDistance() (r int32) {
	if x != nil && x.WalkDistance != nil {
		return *x.WalkDistance
	}
	return r
}

func (x *SmartBusStationInfo) GetWalkTime() (r int32) {
	if x != nil && x.WalkTime != nil {
		return *x.WalkTime
	}
	return r
}

func (x *SmartBusStationInfo) SetPoiId(v string) {
	if x != nil {
		x.PoiId = v
	}
}

func (x *SmartBusStationInfo) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *SmartBusStationInfo) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *SmartBusStationInfo) SetDisplayName(v string) {
	if x != nil {
		x.DisplayName = v
	}
}

func (x *SmartBusStationInfo) SetAddressDesc(v string) {
	if x != nil {
		x.AddressDesc = v
	}
}

func (x *SmartBusStationInfo) SetLinkId(v string) {
	if x != nil {
		x.LinkId = v
	}
}

func (x *SmartBusStationInfo) SetWalkDistance(v int32) {
	if x != nil {
		x.WalkDistance = &v
	}
}

func (x *SmartBusStationInfo) SetWalkTime(v int32) {
	if x != nil {
		x.WalkTime = &v
	}
}

func (p *SmartBusStationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SmartBusStationInfo(%+v)", *p)
}

type MiniBusCarpoolSeatModule struct {
	Title            string `json:"title" form:"title"`                           //乘车人数
	MaxCount         int32  `json:"max_count" form:"max_count"`                   //2
	SelectCount      int32  `json:"select_count" form:"select_count"`             //二次预估选中的人数
	SeatsExceedToast string `json:"seats_exceed_toast" form:"seats_exceed_toast"` //"最大不可超过2人
}

func (x *MiniBusCarpoolSeatModule) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *MiniBusCarpoolSeatModule) GetMaxCount() (r int32) {
	if x != nil {
		return x.MaxCount
	}
	return r
}

func (x *MiniBusCarpoolSeatModule) GetSelectCount() (r int32) {
	if x != nil {
		return x.SelectCount
	}
	return r
}

func (x *MiniBusCarpoolSeatModule) GetSeatsExceedToast() (r string) {
	if x != nil {
		return x.SeatsExceedToast
	}
	return r
}

func (x *MiniBusCarpoolSeatModule) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *MiniBusCarpoolSeatModule) SetMaxCount(v int32) {
	if x != nil {
		x.MaxCount = v
	}
}

func (x *MiniBusCarpoolSeatModule) SetSelectCount(v int32) {
	if x != nil {
		x.SelectCount = v
	}
}

func (x *MiniBusCarpoolSeatModule) SetSeatsExceedToast(v string) {
	if x != nil {
		x.SeatsExceedToast = v
	}
}

func (p *MiniBusCarpoolSeatModule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusCarpoolSeatModule(%+v)", *p)
}

type MiniBusPluginPageInfo struct {
	ShowH5    string `json:"show_h5" form:"show_h5"`
	ConfirmH5 string `json:"confirm_h5" form:"confirm_h5"`
}

func (x *MiniBusPluginPageInfo) GetShowH5() (r string) {
	if x != nil {
		return x.ShowH5
	}
	return r
}

func (x *MiniBusPluginPageInfo) GetConfirmH5() (r string) {
	if x != nil {
		return x.ConfirmH5
	}
	return r
}

func (x *MiniBusPluginPageInfo) SetShowH5(v string) {
	if x != nil {
		x.ShowH5 = v
	}
}

func (x *MiniBusPluginPageInfo) SetConfirmH5(v string) {
	if x != nil {
		x.ConfirmH5 = v
	}
}

func (p *MiniBusPluginPageInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusPluginPageInfo(%+v)", *p)
}

type ConfirmButton struct {
	Title    string  `json:"title" form:"title"`
	Subtitle string  `json:"subtitle" form:"subtitle"`
	LinkUrl  *string `json:"link_url,omitempty" form:"link_url"`
}

func (x *ConfirmButton) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *ConfirmButton) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *ConfirmButton) GetLinkUrl() (r string) {
	if x != nil && x.LinkUrl != nil {
		return *x.LinkUrl
	}
	return r
}

func (x *ConfirmButton) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *ConfirmButton) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *ConfirmButton) SetLinkUrl(v string) {
	if x != nil {
		x.LinkUrl = &v
	}
}

func (p *ConfirmButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmButton(%+v)", *p)
}

type DynamicEffectParams struct {
	MarkIcon        string  `json:"mark_icon" form:"mark_icon"`
	BackgroundIcon  string  `json:"background_icon" form:"background_icon"`
	DefaultIcon     string  `json:"default_icon" form:"default_icon"`
	DefaultText     string  `json:"default_text" form:"default_text"`
	RetryButtonText string  `json:"retry_button_text" form:"retry_button_text"`
	SelectBgColor   *string `json:"select_bg_color,omitempty" form:"select_bg_color"`
}

func (x *DynamicEffectParams) GetMarkIcon() (r string) {
	if x != nil {
		return x.MarkIcon
	}
	return r
}

func (x *DynamicEffectParams) GetBackgroundIcon() (r string) {
	if x != nil {
		return x.BackgroundIcon
	}
	return r
}

func (x *DynamicEffectParams) GetDefaultIcon() (r string) {
	if x != nil {
		return x.DefaultIcon
	}
	return r
}

func (x *DynamicEffectParams) GetDefaultText() (r string) {
	if x != nil {
		return x.DefaultText
	}
	return r
}

func (x *DynamicEffectParams) GetRetryButtonText() (r string) {
	if x != nil {
		return x.RetryButtonText
	}
	return r
}

func (x *DynamicEffectParams) GetSelectBgColor() (r string) {
	if x != nil && x.SelectBgColor != nil {
		return *x.SelectBgColor
	}
	return r
}

func (x *DynamicEffectParams) SetMarkIcon(v string) {
	if x != nil {
		x.MarkIcon = v
	}
}

func (x *DynamicEffectParams) SetBackgroundIcon(v string) {
	if x != nil {
		x.BackgroundIcon = v
	}
}

func (x *DynamicEffectParams) SetDefaultIcon(v string) {
	if x != nil {
		x.DefaultIcon = v
	}
}

func (x *DynamicEffectParams) SetDefaultText(v string) {
	if x != nil {
		x.DefaultText = v
	}
}

func (x *DynamicEffectParams) SetRetryButtonText(v string) {
	if x != nil {
		x.RetryButtonText = v
	}
}

func (x *DynamicEffectParams) SetSelectBgColor(v string) {
	if x != nil {
		x.SelectBgColor = &v
	}
}

func (p *DynamicEffectParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DynamicEffectParams(%+v)", *p)
}

type ShowOmega struct {
	Key    string                 `json:"key" form:"key"`
	Params map[string]interface{} `json:"params" form:"params"`
}

func (x *ShowOmega) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *ShowOmega) GetParams() (r map[string]interface{}) {
	if x != nil {
		return x.Params
	}
	return r
}

func (x *ShowOmega) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *ShowOmega) SetParams(v map[string]interface{}) {
	if x != nil {
		x.Params = v
	}
}

func (p *ShowOmega) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ShowOmega(%+v)", *p)
}

type MiniBusOmegaInfo struct {
	ShowOmega *ShowOmega `json:"show_omega,omitempty" form:"show_omega"`
}

func (x *MiniBusOmegaInfo) GetShowOmega() (r *ShowOmega) {
	if x != nil {
		return x.ShowOmega
	}
	return r
}

func (x *MiniBusOmegaInfo) SetShowOmega(v *ShowOmega) {
	if x != nil {
		x.ShowOmega = v
	}
}

func (p *MiniBusOmegaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MiniBusOmegaInfo(%+v)", *p)
}

type TPShowOmega struct {
	ShowOmega *ShowOmega `json:"show_omega,omitempty" form:"show_omega"`
}

func (x *TPShowOmega) GetShowOmega() (r *ShowOmega) {
	if x != nil {
		return x.ShowOmega
	}
	return r
}

func (x *TPShowOmega) SetShowOmega(v *ShowOmega) {
	if x != nil {
		x.ShowOmega = v
	}
}

func (p *TPShowOmega) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TPShowOmega(%+v)", *p)
}

type SmartBusNewOrderParam struct {
	Dchn *string `json:"dchn,omitempty" form:"dchn"`
}

func (x *SmartBusNewOrderParam) GetDchn() (r string) {
	if x != nil && x.Dchn != nil {
		return *x.Dchn
	}
	return r
}

func (x *SmartBusNewOrderParam) SetDchn(v string) {
	if x != nil {
		x.Dchn = &v
	}
}

func (p *SmartBusNewOrderParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SmartBusNewOrderParam(%+v)", *p)
}

// 带给sideEstimate的参数，直接透传【na: 7.0.6, wx: 6.10.25以后使用】
type SideParam struct {
	WalkDist     *int32 `json:"walk_dist,omitempty" form:"walk_dist"`
	PreMatchType *int32 `json:"pre_match_type,omitempty" form:"pre_match_type"`
}

func (x *SideParam) GetWalkDist() (r int32) {
	if x != nil && x.WalkDist != nil {
		return *x.WalkDist
	}
	return r
}

func (x *SideParam) GetPreMatchType() (r int32) {
	if x != nil && x.PreMatchType != nil {
		return *x.PreMatchType
	}
	return r
}

func (x *SideParam) SetWalkDist(v int32) {
	if x != nil {
		x.WalkDist = &v
	}
}

func (x *SideParam) SetPreMatchType(v int32) {
	if x != nil {
		x.PreMatchType = &v
	}
}

func (p *SideParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SideParam(%+v)", *p)
}
