namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct MiniBusEstimateRequest{
    1: required string lang // 端语言
    2: required string token // 用户认证token
    3: required i32    access_key_id // 端来源
    4: required string app_version // 端版本
    5: required string    channel // 渠道号
    6: optional string    xpsid
    7: optional string    xpsid_root
    8: required i32       carpool_seat_num // 选择的座位数
    9: required i32    client_type // 端类型
    10: required double from_lng // 用户当前经度
    11: required double from_lat // 用户当前纬度
    12: required string from_address
    13: required string from_name
    14: required string from_poi_id
    15: required string from_poi_type
    16: required double lat // 定位点
    17: required double lng
    18: required string map_type // 地图类型
    19: required i32    platform_type // 端(平台)类型-2
    20: required i32       payments_type // 选择的支付方式
    21: required string to_address
    22: required double to_lat // 终点
    23: required double to_lng
    24: required string to_name
    25: required string to_poi_id
    26: required string to_poi_type
    27: required UserType  user_type // 1普通用户；2企业用户
    28: required string choose_f_searchid //用户选择起点请求ID
    29: required string choose_t_searchid //用户选择终点请求ID
    30: required string mapinfo_cache_token
    31: required int16  from_type
    32: required string multi_require_product // 二次预估
    33: required string mapinfo_start_cache_token
    34: required string mapinfo_dest_cache_token
    35: optional i32 page_type // 页面类型，51: 智能小巴
    36: optional string dchn // 终端来源
    37: optional string trans_data //透传数据，端根据link上 trans_data 透传
    38: required string start_station_info //起点站点信息
    39: required string dest_station_info //终点站点信息
    40: required i64 driver_id //司机id
    41: required string ext_map //扩展字段  "{}"，不影响预估流程
}

struct MiniBusEstimateResp{
    1: required	i32 errno
	2: required string errmsg
	3: optional MiniBusEstimateData data
}

struct MiniBusEstimateData {
    1: optional list<EstimateFromData> estimate_data
    2: required string title
    3: required string subtitle
    4: optional MiniBusCarpoolSeatModule carpool_seat_module
    5: required int16 is_support_multi_selection
    6: required int16 page_type
    7: required string fee_detail_url
    8: optional MiniBusPluginPageInfo plugin_page_info
    9: optional PaymentOptionModule user_pay_info
    10: optional ConfirmButton confirm_button
    11: optional DynamicEffectParams dynamic_effect_params
    12: optional BackButton back_button
    13: required string estimate_trace_id
    14: required TokenInfo token_info
    15: required MapCurveInfo map_curve_info
    16: required i32 best_view_type
    17: required MiniBusOmegaInfo omega_info
    18: optional string icon
    19: optional i32 pre_match_type
    20: optional SmartBusNewOrderParam pneworder_params
    21: required i16  disable_best_view_select
    22: optional SmartBusStationInfo start_station_info //无TP时 地图站牌展示使用
    23: optional SmartBusStationInfo dest_station_info //无TP时 地图站牌展示使用
    24: optional string smart_bus_page_version //智能小巴页面版本【na: 7.0.6, wx: 6.10.25以后使用】
    25: optional i32 refresh_interval // 刷新间隔
    26: optional i32 minibus_type
}

struct TokenInfo {
    1: required string mapinfo_start_cache_token
    2: required string mapinfo_dest_cache_token
}

struct EstimateFromData {
    1: required string estimate_id
    2: required int16 is_selected
    3: required NewFormUserPayInfo user_pay_info
    4: optional TP tp
    5: optional MiniBusExtraMap extra_map
    6: optional SmartBusStationInfo start_station_info
    7: optional SmartBusStationInfo dest_station_info
    8: optional string confirm_subtitle //确认按钮副标题，切换TP会变化【na: 7.0.6, wx: 6.10.25以后使用】
    9: optional SideParam side_params //带给sideEstimate的参数【na: 7.0.6, wx: 6.10.25以后使用】
    10: required i32 pre_match_type
    11: required i32 best_view_type
    12: optional list<SmartBusStationInfo> approach_point_list
    13: required TPShowOmega omega_info
}

struct TP {
    1: required float64 fee_amount
    2: required string fee_msg
    3: required string notice_title
    4: required string notice_subtitle
    5: required string tag
    6: optional list<NewFormFeeDesc> fee_desc_list
    7: optional string notice_text //TP卡片下层文案【na: 7.0.6, wx: 6.10.25以后使用】
    8: optional string notice_subtext //TP卡片下层副文案【na: 7.0.6, wx: 6.10.25以后使用】
    9: optional string sub_tag //TP卡片上层标签【na: 7.0.6, wx: 6.10.25以后使用】
}

struct FeeDetail {
    1: required double fee_amount
    2: required string fee_msg
    3: optional list<FeeDesc> fee_desc_list
}

struct MiniBusExtraMap {
    1: required i64 product_id                  // 产品线ID
    2: required i64 business_id                 // 业务线
    3: required i64 etp                         // etp
    4: required i64 require_level               // 车型
    5: required i64 combo_type                  // combo_type
    6: required i32 level_type
    7: required i64 carpool_type
    8: required i64 product_category
    9: optional i64 combo_id
    10:optional i64 departure_time
    11:optional string bus_service_shift_id
}

struct SmartBusStationInfo {
    1: required string poi_id
    2: required double lat
    3: required double lng
    4: required string display_name
    5: required string address_desc
    6: required string link_id
    7: optional int32 walk_distance
    8: optional int32 walk_time
}

struct MiniBusCarpoolSeatModule{
    1: required string title //乘车人数
    2: required i32 max_count //2
    3: required i32 select_count // 二次预估选中的人数
    5: required string seats_exceed_toast // "最大不可超过2人
}

struct MiniBusPluginPageInfo {
    1: required string show_h5
    2: required string confirm_h5
}

struct ConfirmButton {
    1: required string title
    2: required string subtitle
    3: optional string link_url
}

struct DynamicEffectParams {
    1: required string mark_icon
    2: required string background_icon
    3: required string default_icon
    4: required string default_text
    5: required string retry_button_text
    6: optional string select_bg_color
}

struct ShowOmega {
   1:required string key
   2:required string params  (go.type="map[string]interface{}",php.type="array")
}

struct MiniBusOmegaInfo {
   1:required ShowOmega show_omega
}

struct TPShowOmega {
   1:required ShowOmega show_omega
}

struct SmartBusNewOrderParam {
    1: optional string dchn
}

//带给sideEstimate的参数，直接透传【na: 7.0.6, wx: 6.10.25以后使用】
struct SideParam {
    1: optional i32 walk_dist
    2: optional i32 pre_match_type
}
