# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
mamba

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
log
**/__naming__
vendor/
*.log
*.log.*
*.swp
.idea/
./DS_Store
output/
.deploy/
.statsd/
./conf/app.toml
conf/app.toml
register.yaml

# vim swap file
[._]*.sw[a-p]

# intellij
.idea

# Dependency directories (remove the comment below to include it)
# vendor/

**/.tmp/
.vscode/
proto/*
!proto/*.proto