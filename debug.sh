#!/bin/bash

set -e

## export 环境变量
export DIDIENV_DDCLOUD_ENV_TYPE=dev
export OE_OSIM_DISF_BASE_ENV=zjy-dev-v
export DIDIENV_ODIN_CLUSTER=zjy-dev-v
export DIDIENV_ODIN_SU=zjy-dev-v.biz-gs-mamba

## 安装disf-agent
ProcDisf=`ps -ef | grep -w offline-disf-agent | grep -v grep | wc -l`
if [ $ProcDisf -le 0 ];then
  curl -s https://git.xiaojukeji.com/disf/public_script/raw/master/offline/disf-agent/offline_install.sh | sh -s dev
fi

## 编译程序
go build -o mamba

## 拷贝配置文件
mkdir -p /tmp/xiaoju/ep/as/store/conf/BizGoTimeout && cp ./conf/debug/gs_biz_api_timeout /tmp/xiaoju/ep/as/store/conf/BizGoTimeout/gs_biz_api_timeout
cp ./conf/app_dev.toml ./conf/app.toml

## 启动
./mamba
