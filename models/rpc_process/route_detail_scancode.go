package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
)

// routeDetailScanCodeRpc 大车班车站点模式路线推荐与库存查询
type routeDetailScanCodeRpc struct {
	baseReq *models.BaseReqData

	routeDetail *Prfs.RouteDetailData
}

func (rd *routeDetailScanCodeRpc) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

// NewRouteScanCodeDetail 模式路线推荐与库存查询
func NewRouteScanCodeDetail(baseReq *models.BaseReqData) *routeDetailScanCodeRpc {
	return &routeDetailScanCodeRpc{
		baseReq: baseReq,
	}
}

// BuildCommonBizInfo ...
func (rd *routeDetailScanCodeRpc) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rd.baseReq != nil && rd.routeDetail != nil && rd.routeDetail.RouteBasicInfo != nil && len(rd.routeDetail.RouteBasicInfo.StationList) > 0 {
		for _, stationItem := range rd.routeDetail.RouteBasicInfo.StationList {
			if stationItem == nil {
				continue
			}

			if int64(stationItem.StationId) == rd.baseReq.CommonBizInfo.StartStationId {
				info.StartStation = stationItem
			}

			if int64(stationItem.StationId) == rd.baseReq.CommonBizInfo.EndStationId {
				info.EndStation = stationItem
			}
		}
	}
	info.RouteDetail = rd.routeDetail
}

// Fetch 获取/加载
func (rd *routeDetailScanCodeRpc) Fetch(ctx context.Context, products []*models.Product) bool {
	if rd.baseReq == nil {
		return true
	}

	routeGroup := rd.baseReq.CommonBizInfo.RouteId

	req := rd.buildRequest(ctx, routeGroup)
	if req == nil {
		return false
	}

	resp := prfs.GetRouteDetail(ctx, req)
	if resp == nil || len(resp.Data) <= 0 {
		return false
	}

	if routeDetail, ok := resp.Data[int32(routeGroup)]; ok {
		rd.routeDetail = routeDetail
	}

	return true
}

// buildRequest ...
func (rd *routeDetailScanCodeRpc) buildRequest(ctx context.Context, routeGroup int64) *Prfs.RouteDetailReq {
	return &Prfs.RouteDetailReq{
		RouteId:     strconv.Itoa(int(routeGroup)),
		CallerScene: PRFS_CALLER_SCENE,
		StartCityId: rd.baseReq.AreaInfo.Area,
	}
}
