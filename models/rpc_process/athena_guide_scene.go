package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
)

type GuideScene struct {
	request      *AthenaApiv3.AthenaBubbleNewReq
	guideProduct map[int32]bool
}

func NewGuideSceneRPC(ctx context.Context, generator *models.BaseReqData) *GuideScene {

	commonInfo := generator.CommonInfo
	passengerInfo := generator.PassengerInfo
	areaInfo := generator.AreaInfo
	//commonBizInfo := generator.CommonBizInfo

	req := &AthenaApiv3.AthenaBubbleNewReq{
		ClientType:  int64(commonInfo.ClientType),
		AppVersion:  commonInfo.AppVersion,
		Lang:        commonInfo.Lang,
		OrderType:   commonInfo.OrderType,
		Channel:     &commonInfo.Channel,
		MenuID:      &commonInfo.MenuID,
		AccessKeyID: &commonInfo.AccessKeyID,
		PageType:    &commonInfo.PageType,

		MapType:    areaInfo.MapType,
		CurrentLng: &areaInfo.CurLng,
		CurrentLat: &areaInfo.CurLat,
		FromArea:   int64(areaInfo.Area),
		FromLng:    areaInfo.FromLng,
		FromLat:    areaInfo.FromLat,
		ToLng:      areaInfo.ToLng,
		ToLat:      areaInfo.ToLat,
		FromName:   &areaInfo.FromName,
		ToName:     &areaInfo.ToName,

		// 用户信息
		Phone:    passengerInfo.Phone,
		Pid:      strconv.FormatInt(passengerInfo.PID, 10),
		UserType: &passengerInfo.UserType,

		APIAddProduct: nil,

		LinkSource: nil,
		ExtraInfo:  nil,
	}
	return &GuideScene{
		request:      req,
		guideProduct: make(map[int32]bool),
	}

}

func (rp *GuideScene) Fetch(ctx context.Context, products []*models.Product) bool {

	var list []*AthenaApiv3.AthenaEstimateProductInfo
	for _, product := range products {
		list = append(list, &AthenaApiv3.AthenaEstimateProductInfo{
			ProductID:          product.ProductID,
			RequireLevel:       product.RequireLevel,
			ComboType:          product.ComboType,
			CarpoolType:        &product.CarpoolType,
			IsSpecialPrice:     &product.IsSpecialPrice,
			ProductCategory:    &product.ProductCategory,
			EstimateID:         &product.EstimateID,
			LevelType:          &product.LevelType,
			IsDualCarpoolPrice: &product.IsDualCarpoolPrice,
			CarpoolPriceType:   &product.CarpoolPriceType,
		})
	}

	if len(list) > 0 {
		rp.request.APIAddProduct = list
	} else {
		return false
	}

	resp := athena.GetBubbleGuideScene(ctx, rp.request)
	if resp == nil || len(resp.GuideLine) <= 0 {
		return false
	}
	for _, pcID := range resp.GuideLine {
		rp.guideProduct[pcID] = true
	}
	return true
}

func (rp *GuideScene) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (rp *GuideScene) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {

}
