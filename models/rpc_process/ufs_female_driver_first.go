package rpc_process

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"strconv"
	"time"
)

type Scene struct {
	ID    int    `json:"id"`
	Value string `json:"value"`
}

type FemaleDriverFirst struct {
	Domain                  string
	OrderID                 string
	OrderInfo               *dos.OrderInfo
	OrderTime               string
	SceneList               *string
	UfsKey                  string
	IsFemaleDriverFirst     int64
	IsShowFemaleDriverFirst int64
	ApolloParam             map[string]string
}

func (fd *FemaleDriverFirst) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

const (
	FormatTime = "2006-01-02 15:04:05"
	ABKey      = "wycll_bubble_bottom_com_select_driver"
)

func FemaleDriverFirstRPC(ctx context.Context, domain string, orderInfo *dos.OrderInfo, req *models.BaseReqData) *FemaleDriverFirst {
	params := map[string]string{
		"city":          fmt.Sprintf("%d", req.AreaInfo.City),
		"phone":         req.PassengerInfo.Phone,
		"uid":           fmt.Sprintf("%d", req.PassengerInfo.UID),
		"pid":           fmt.Sprintf("%d", req.PassengerInfo.PID),
		"city_id":       fmt.Sprintf("%d", req.AreaInfo.City),
		"lang":          req.CommonInfo.Lang,
		"access_key_id": fmt.Sprintf("%d", req.CommonInfo.AccessKeyID),
		"app_version":   req.CommonInfo.AppVersion,
		"caller":        "mamba",
	}

	return &FemaleDriverFirst{
		Domain:      domain,
		OrderID:     orderInfo.OrderId,
		OrderInfo:   orderInfo,
		OrderTime:   orderInfo.BirthTime,
		SceneList:   orderInfo.SceneList,
		UfsKey:      ufs.FemaleDriverKey,
		ApolloParam: params,
	}
}

func (fd *FemaleDriverFirst) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
		ufsResult   int64
	)

	//校验用户是否选择女司机优先 (ufs)
	feature := ufsClient.Feature{
		Domain: fd.Domain,
		Keys:   []string{fd.UfsKey},
		Params: map[string]string{
			"order_id": fd.OrderID,
		},
	}
	UFSFeatures = append(UFSFeatures, feature)

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get female driver status from ufs fail with err %v", err)
		return false
	}

	//把UFS结果映射为map
	for _, ufsResp := range UFSResp {
		if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value == "" {
			continue
		}

		serviceErr := json.Unmarshal([]byte(*ufsResp.Value), &ufsResult)
		if serviceErr != nil {
			return false
		}

		if consts.Checked == ufsResult {
			fd.IsFemaleDriverFirst = 1
			fd.IsShowFemaleDriverFirst = 1
			goto TimeCheck
		}
	}

	//校验dos的sceneList是否有女司机场景（改派场景）
	if CheckSceneAvailable(ctx, fd.SceneList) {
		fd.IsFemaleDriverFirst = 1
		fd.IsShowFemaleDriverFirst = 1
		goto TimeCheck
	}

TimeCheck:
	//校验dos的生效时间是否在分单时间之内(只影响标签是否展示)
	if !CheckTimeAvailable(ctx, fd.OrderTime, fd.ApolloParam) {
		fd.IsShowFemaleDriverFirst = 0
	}

	return true
}

func (fd *FemaleDriverFirst) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.IsFemaleDriverFirst = fd.IsFemaleDriverFirst
	info.IsShowFemaleDriverFirst = fd.IsShowFemaleDriverFirst
}

func CheckSceneAvailable(ctx context.Context, SceneList *string) bool {
	//校验dos内是否有夜间女司机场景
	var sceneList []*Scene
	if SceneList != nil && *SceneList != "" && *SceneList != "\"\"" {
		str := "`" + *SceneList + "`"
		s, err := strconv.Unquote(str)
		if err == nil {
			err = json.Unmarshal([]byte(s), &sceneList)
			if err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "Unmarshal SceneList err")
			}
		}
	}

	for _, scene := range sceneList {
		if scene.ID == consts.SceneIDFemaleDriver {
			return true
		}
	}

	return false
}

func CheckTimeAvailable(ctx context.Context, OrderTime string, param map[string]string) bool {
	birthTime := str2Int64Time(OrderTime)
	currentTime := time.Now().Unix()
	toggle := apollo.GetHitToggleByNamespace(ctx, param["pid"], ABKey, param)
	if toggle == nil {
		return false
	}

	compareTime := toggle.GetAssignment().GetParameter("wait_time", "30")
	waitTime, err := strconv.ParseInt(compareTime, 10, 64)
	if err != nil {
		return false
	}

	if (currentTime - birthTime) < waitTime {
		return true
	}
	return false
}

func str2Int64Time(val string) (ret int64) {
	if timeObj, err := time.ParseInLocation(FormatTime, val, time.Local); err == nil {
		ret = timeObj.Unix()
	} else {
		ret = 0
	}
	return ret
}
