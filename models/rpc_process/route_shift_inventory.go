package rpc_process

import (
	"context"
	"strconv"
	"time"

	PopePAP "git.xiaojukeji.com/dirpc/dirpc-go-http-PopePAPService"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pope_pap"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

// RouteShiftInventory 站点巴士获取优惠
type RouteShiftInventory struct {
	baseReq *models.BaseReqData

	datesCouponInfo map[int64]bool

	BusRegionInventoryList []*CarpoolOpenApi.BusRegionInventory
}

func (rd *RouteShiftInventory) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

// NewRouteShiftInventory 站点巴士获取优惠
func NewRouteShiftInventory(baseReq *models.BaseReqData) *RouteShiftInventory {
	if baseReq == nil || baseReq.CommonBizInfo.StationInfo.SupportCounty != 1 {
		return nil
	}

	return &RouteShiftInventory{
		baseReq:         baseReq,
		datesCouponInfo: make(map[int64]bool, 14),
	}

}

func (rd *RouteShiftInventory) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.DatesCouponInfo = rd.datesCouponInfo
	info.BusRegionInventoryList = rd.BusRegionInventoryList
}

// Fetch 获取/加载
func (rd *RouteShiftInventory) Fetch(ctx context.Context, products []*models.Product) bool {

	if !IsOpen(rd.baseReq.PassengerInfo, rd.baseReq.CommonInfo, rd.baseReq.AreaInfo) {
		return false
	}
	ddsreq := &EstimateDecision.ProductSimpleReq{
		From:        2,
		Uid:         int64(rd.baseReq.PassengerInfo.UID),
		Pid:         int64(rd.baseReq.PassengerInfo.PID),
		PhoneStr:    rd.baseReq.PassengerInfo.Phone,
		AppVersion:  "6.8.0",
		AccessKeyId: &rd.baseReq.CommonInfo.AccessKeyID,
		Lang:        &rd.baseReq.CommonInfo.Lang,
		PageType:    int32(rd.baseReq.CommonInfo.PageType),
		CityId:      rd.baseReq.CommonBizInfo.StationInfo.StartCity,
		ToCityId:    rd.baseReq.CommonBizInfo.StationInfo.EndCity,
	}
	ddsresp, err := decision.GetDDSProductsSimple(ctx, ddsreq)
	var productIdList []int32
	if err != nil || ddsresp == nil || len(ddsresp.ProductList) == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get dds open city list err: %s", err)
		return false
	}
	for _, v := range ddsresp.ProductList {
		if !v.RemoveFlag {
			productIdList = append(productIdList, int32(v.ProductId))
		}
	}

	// 过滤product_id
	productIdList = rd.filterProduct(productIdList)

	if productIdList == nil || len(productIdList) == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "productIdList is nil")
		return false
	}

	traceInfo := &CarpoolOpenApi.Trace{}
	if tracer, ok := trace.GetCtxTrace(ctx); ok {
		traceInfo = &CarpoolOpenApi.Trace{
			LogId:       tracer.TraceId,
			Caller:      tracer.CallerFunc,
			SpanId:      &tracer.SpanId,
			HintContent: &tracer.HintContent,
		}
		value, err := strconv.ParseInt(tracer.HintCode, 10, 64)
		if err == nil {
			traceInfo.HintCode = &value
		}
	}

	pid := strconv.FormatUint(uint64(rd.baseReq.PassengerInfo.PID), 10)
	req := &CarpoolOpenApi.QueryRouteShiftInventoryInfoReq{
		SrcRegion: &CarpoolOpenApi.BusRegion{
			CityID:    rd.baseReq.CommonBizInfo.StationInfo.StartCity,
			CountyID:  &rd.baseReq.CommonBizInfo.StationInfo.StartCountyId,
			StationID: &rd.baseReq.CommonBizInfo.StationInfo.StartStationId,
			GeoPoint: &CarpoolOpenApi.GeoPoint{
				Lng: rd.baseReq.CommonBizInfo.StationInfo.FromLng,
				Lat: rd.baseReq.CommonBizInfo.StationInfo.FromLat,
			},
		},
		DestRegion: &CarpoolOpenApi.BusRegion{
			CityID:    rd.baseReq.CommonBizInfo.StationInfo.EndCity,
			CountyID:  &rd.baseReq.CommonBizInfo.StationInfo.EndCountyId,
			StationID: &rd.baseReq.CommonBizInfo.StationInfo.EndStationId,
			GeoPoint: &CarpoolOpenApi.GeoPoint{
				Lng: rd.baseReq.CommonBizInfo.StationInfo.ToLng,
				Lat: rd.baseReq.CommonBizInfo.StationInfo.ToLat,
			},
		},
		TimeRange: &CarpoolOpenApi.TimeWindow{
			BeginTime: time.Now().Unix(),
			EndTime:   getNowLastTimeStamp() + 13*24*60*60,
		},
		PassengerID:   &pid,
		ProductIDList: productIdList,
	}

	resp, err := carpool_open_api.GetQueryRouteShiftInventoryByDay(ctx, req, traceInfo)
	if resp == nil || resp.RetCode != 0 || err != nil {
		return false
	}
	rd.BusRegionInventoryList = resp.BusRegionInventoryList
	rd.datesCouponInfo = rd.getDiscountStatus(ctx, resp.BusRegionInventoryList)
	return true
}

func (rd *RouteShiftInventory) filterProduct(productIdList []int32) []int32 {
	stationInfo := rd.baseReq.CommonBizInfo.StationInfo

	if stationInfo.ProductId == 0 || len(stationInfo.OpenSource) == 0 {
		return productIdList
	}

	param := ApolloModel.NewUser("").
		With("passenger_phone", rd.baseReq.PassengerInfo.Phone).
		With("city", cast.ToString(rd.baseReq.AreaInfo.City)).
		With("open_source", stationInfo.OpenSource)
	toggle, err := ApolloSDK.FeatureToggle("gs_intercity_station_filter_product", param)
	if err != nil || !toggle.IsAllow() {
		return productIdList
	}

	var filterProducts []int32
	for _, product := range productIdList {
		if product == stationInfo.ProductId {
			filterProducts = append(filterProducts, product)
			break
		}
	}
	return filterProducts
}

func (rd *RouteShiftInventory) getDiscountStatus(ctx context.Context, list []*CarpoolOpenApi.BusRegionInventory) map[int64]bool {
	// 通过duse返回的数据拼接给pope获取优惠的数据
	dates := []int64{}
	routeIdList := []int64{}
	result := make(map[int64]bool)
	for _, v := range list {
		if v == nil || v.Status != 1 {
			continue
		}
		dates = append(dates, v.DayStartTime)
		routeIdList = append(routeIdList, v.RouteGroupList...)
	}
	routeIdList = util.RemoveDuplicates(routeIdList)
	if len(dates) == 0 || len(routeIdList) == 0 {
		return nil
	}
	resp, err := pope_pap.GetBusRoutePOPECoupon(ctx, &PopePAP.BusRouteCouponRequest{RouteIds: routeIdList, Dates: dates})
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pope engine get bus route pope coupon err: %s", err)
		return nil
	}
	if resp == nil || resp.Data == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "pope engine get bus route pope coupon is nil")
		return nil
	}

	for time, v := range resp.Data {
		if v == nil || v.Data == nil {
			continue
		}
		for _, w := range v.Data {
			if w == nil {
				continue
			}
			if w.HasCoupon {
				result[time] = true
				break
			}
		}
	}
	return result
}

// GetStationList 获取当天24点时间戳
func getNowLastTimeStamp() int64 {
	year, month, day := time.Now().Date()
	local, _ := time.LoadLocation("Asia/Shanghai")
	return time.Date(year, month, day, 0, 0, 0, 0, local).Unix() + 24*60*60
}

func IsOpen(passengerInfo models.PassengerInfo, commonInfo models.CommonInfo, areaInfo models.AreaInfo) bool {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(int(areaInfo.City))).
		With("phone", passengerInfo.Phone).
		With("access_key_id", strconv.Itoa(int(commonInfo.AccessKeyID))).
		With("app_version", commonInfo.AppVersion)
	toggle, err := ApolloSDK.FeatureToggle("gs_discount_intercity_station_swtich", param)
	if err == nil && toggle.IsAllow() {
		return true
	}
	return false
}
