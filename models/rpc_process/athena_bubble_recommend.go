package rpc_process

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_price"
	"strconv"
	"time"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/tripcloud"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const (
	// RecognizeTaxiPeakKey 识别出租车峰期加价的标识
	RecognizeTaxiPeakKey = "is_peak_period"
	// BargainIsSupport 司乘议价推荐或者多勾支持字段
	BargainIsSupport = "1"
	EstimateFee      = "estimate_fee"
)

type BubbleRecommendInfo struct {
	bubbleRecommendReq *AthenaApiv3.AthenaBubbleRecommendReq
	resp               *AthenaApiv3.AthenaGuideRecommendResp
	pcId2EstimateId    map[int64]string

	orderMatchType int32

	UID           int64
	DepartureTime int64

	CheckedPcIdMap     map[int64]int  // 默勾数据
	ChengYiDanDiscount map[int32]bool // 橙意单车型

	selectStatusRecCarpool int32 // 默认拼区分二次预估状态 -1 不是二次预估 1 一座 2 两座 0 不拼座

}

const (
	SingleType        = 0 // 0:单车型
	ShortDistanceType = 2 // 2:聚合车型(三方,出租车)
	PeakType          = 3 // 3:高峰期盒子

	EstimateOrderMatchType       = 0 // 默认(主预估)
	AnycarOrderMatchType         = 1 // 等待应答
	CombinedTravelOrderMatchType = 2 // 组合出行

	EstimateType = 0

	SCENE_FLAG_LINEUP = 2 // 排队状态
	LogTag            = "athena_bubble"
)

func NewBubbleRecommendInfoRPC(generator *models.BaseReqData, orderMatchType int32) *BubbleRecommendInfo {

	commonInfo := generator.CommonInfo
	passengerInfo := generator.PassengerInfo
	areaInfo := generator.AreaInfo
	commonBizInfo := generator.CommonBizInfo
	toArea := int64(areaInfo.ToArea)
	req := &AthenaApiv3.AthenaBubbleRecommendReq{
		AthenaBubbleReq: &AthenaApiv3.AthenaNewFormBubbleReq{
			// 基础信息

			PreOrderMatch: &orderMatchType, // 0:默认 1:等待应答 2:组合出行
			ClientType:    int64(commonInfo.ClientType),
			AppVersion:    commonInfo.AppVersion,
			Lang:          commonInfo.Lang,
			OrderType:     commonInfo.OrderType,
			Channel:       &commonInfo.Channel,
			MenuID:        &commonInfo.MenuID,
			CallCarType:   &commonInfo.CallCarType,
			AccessKeyID:   &commonInfo.AccessKeyID,
			PageType:      &commonInfo.PageType,
			SourceID:      &commonInfo.SourceID,
			ScreenPixels:  &commonInfo.ScreenPixels,
			ScreenScale:   &commonInfo.ScreenScale,
			EventKey:      generator.CommonInfo.EventKey,

			// 用户信息
			Phone:    passengerInfo.Phone,
			Pid:      strconv.FormatInt(passengerInfo.PID, 10),
			UserType: &passengerInfo.UserType,

			// 地点信息
			FromArea:   int64(areaInfo.Area),
			FromLng:    areaInfo.FromLng,
			FromLat:    areaInfo.FromLat,
			ToLng:      areaInfo.ToLng,
			ToLat:      areaInfo.ToLat,
			ToArea:     &toArea,
			FromName:   &areaInfo.FromName,
			ToName:     &areaInfo.ToName,
			FromCounty: &areaInfo.FromCounty,
			ToCounty:   &areaInfo.ToCounty,
			CurrentLng: &areaInfo.CurLng,
			CurrentLat: &areaInfo.CurLat,
			District:   &areaInfo.District,
			DestPoiID:  &areaInfo.ToPoiID,
			MapType:    areaInfo.MapType,

			// 订单
			OrderID:         &generator.SendOrder.OrderId,
			OrderCreateTime: &generator.SendOrder.NewTime,
			IsQueue:         &generator.SendOrder.IsQueue,
			// no need -> RequireLevels
		},
		Mode: 2, // 1:供需准入 2:获取所有能力
	}
	logic := &BubbleRecommendInfo{
		pcId2EstimateId: make(map[int64]string),
		UID:             passengerInfo.UID,
		orderMatchType:  orderMatchType,
	}

	if logic.orderMatchType == AnycarOrderMatchType { // 等待应答
		req.AthenaBubbleReq.PassengerPreferenceList = getDefaultSelect(commonBizInfo, true)
		multiRequireProduct := generator.SendOrder.MultiRequiredProduct
		if len(multiRequireProduct) == 0 {
			return nil
		}
		var sendOrderCarList []*AthenaApiv3.ApiAddProduct
		for _, p := range multiRequireProduct {
			pcId := int64(p.ProductCategory)
			levelType := int32(p.LevelType)
			isTC := tripcloud.IsTripcloudProductID(bizCommonConsts.ProductID(p.ProductId))
			bid := int64(p.BusinessId)
			carpoolType := p.CarpoolType
			sendOrderCarList = append(sendOrderCarList, &AthenaApiv3.ApiAddProduct{
				ProductCategory: &pcId,
				ProductID:       int64(p.ProductId),
				RequireLevel:    p.RequiredLevel,
				ComboType:       int64(p.ComboType),
				LevelType:       &levelType,
				IsTripCloud:     &isTC,
				BusinessID:      &bid,
				CarpoolType:     &carpoolType,
				APIGuideInfo: &AthenaApiv3.ApiGuideInfo{
					PriceInfo: buildCarpoolPriceInfo(p), // 价格信息
				},
			})
		}
		req.AthenaBubbleReq.SendOrderProduct = sendOrderCarList
		// 等待应答传递冒泡预估traceid到athena,athena用于对冒泡和等待应答预估做关联
		extraInfo := make(map[string]string, 0)
		extraInfo["bubble_trace_Id"] = commonInfo.MainEstimateTraceId
		req.AthenaBubbleReq.ExtraInfo = extraInfo
		logic.DepartureTime = commonInfo.DepartureTime

	} else {
		req.AthenaBubbleReq.PassengerPreferenceList = getDefaultSelect(commonBizInfo, false)
		if commonInfo.TabList != "" {
			req.AthenaBubbleReq.TabList = getTabList(commonInfo.TabList)
		}
		req.AthenaBubbleReq.EstimateStyleType = &commonInfo.EstimateStyleType
		req.AthenaBubbleReq.TabID = &commonInfo.TabId
		req.AthenaBubbleReq.PreferenceFilterID = &commonInfo.PreferenceFilterId
		req.AthenaBubbleReq.FilterStyle = buildFilterStyle(generator, logic.orderMatchType)

		logic.selectStatusRecCarpool = -1
		if commonBizInfo.RecCarpoolFormSytle != 0 {
			logic.selectStatusRecCarpool = buildRecParams(commonBizInfo, req)
		}

		req.AthenaBubbleReq.ExtraInfo = buildAthenaParamExtraInfo(commonInfo)

	}
	logic.bubbleRecommendReq = req
	return logic
}

func buildCarpoolPriceInfo(p dos.RequiredProductStruct) map[string]string {
	if p.CarpoolType != carpool.TypeLowPrice && p.CarpoolType != carpool.TypeStation && p.CarpoolType != carpool.TypeMiniBus {
		return nil
	}
	multiPriceScene := p.MultiPriceScene
	var priceInfo = make(map[string]string)
	var carpoolSceneData []*option_gen_price.MultiPriceScene
	err := json.Unmarshal([]byte(multiPriceScene), &carpoolSceneData)
	if err != nil {
		return nil
	}
	var priceScene = make(map[string]interface{})
	if p.CarpoolType == carpool.TypeLowPrice {
		for _, scene := range carpoolSceneData {
			if scene == nil || scene.Option == nil {
				continue
			}
			priceScene[scene.Option.SceneKey] = map[string]float64{
				EstimateFee: scene.EstimateFee,
			}
		}
		priceInfo["low_price_carpool_price_info"] = util.ToJSONStringNotNull(priceScene)
	}
	if p.CarpoolType == carpool.TypeStation {
		for _, scene := range carpoolSceneData {
			if scene == nil || scene.Option == nil {
				continue
			}
			if scene.Option.IsCarpoolSuccess {
				priceScene["success"] = map[string]float64{
					EstimateFee: scene.EstimateFee,
				}
			} else {
				priceScene["failed"] = map[string]float64{
					EstimateFee: scene.EstimateFee,
				}
			}
		}
		priceInfo["carpool_dual_price_info"] = util.ToJSONStringNotNull(priceScene)
	}
	if p.CarpoolType == carpool.TypeMiniBus {
		for _, scene := range carpoolSceneData {
			if scene == nil || scene.Option == nil {
				continue
			}
			priceScene["failed"] = map[string]float64{
				EstimateFee: scene.EstimateFee,
			}
		}
		priceInfo["minibus_price_info"] = util.ToJSONStringNotNull(priceScene)
	}
	return priceInfo
}

func buildAthenaParamExtraInfo(commonInfo models.CommonInfo) map[string]string {
	extraInfo := make(map[string]string, 0)

	return extraInfo
}

func buildFilterStyle(req *models.BaseReqData, matchType int32) *int32 {
	style := int32(0)
	if req.CommonInfo.OrderType != 0 {
		return &style
	}

	if req.CommonInfo.Lang != "zh-CN" {
		return &style
	}

	if matchType == AnycarOrderMatchType {
		return &style
	}

	// 筛选器暂不支持
	//style = int32(2)
	return &style
}

func buildRecParams(commonBizInfo models.CommonBizInfo, req *AthenaApiv3.AthenaBubbleRecommendReq) int32 {
	// 默认拼表单
	selectStatusRecCarpool := int32(-1)

	formStyle := int32(commonBizInfo.RecCarpoolFormSytle)
	req.AthenaBubbleReq.CarpoolFormStyle = &formStyle

	preferenceSeatNum := int32(-1)

	if len(commonBizInfo.MultiRequireProduct) > 0 {
		for _, item := range commonBizInfo.MultiRequireProduct {
			if item.CarpoolSeatNum != 0 && item.IsSelected == 1 {
				preferenceSeatNum = item.CarpoolSeatNum
				break
			}
			if item.CarpoolSeatNum == 0 && item.IsSelected == 1 {
				preferenceSeatNum = 0
				break
			}
		}
	}
	if preferenceSeatNum == -1 {
		preferenceSeatNum = 1
	} else {
		selectStatusRecCarpool = preferenceSeatNum
	}
	req.AthenaBubbleReq.PreferenceSeatNum = &preferenceSeatNum
	return selectStatusRecCarpool
}

func (rp *BubbleRecommendInfo) Fetch(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {

	var estimateCarList []*AthenaApiv3.ApiAddProduct
	for _, productFull := range products {
		p := productFull.Product

		if productFull.BaseReqData.CommonInfo.PageType == page_type.PageTypeGuideAnyCar && productFull.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
			userPid, params := productFull.ApolloParamsGen(apollo_model.WithPIDKey, apollo_model.WithProductCategory)
			ok, assignment := apollo.FeatureExp(ctx, "pcl_addto_anycar_list", userPid, params)
			if !(ok && "treatment_group" == assignment.GetGroupName()) {
				continue
			}
		}

		sceneType := int32(p.SceneType)
		rp.pcId2EstimateId[p.ProductCategory] = p.EstimateID
		isTC := p.IsTripcloudProduct(ctx)
		apiAddProduct := &AthenaApiv3.ApiAddProduct{
			ProductCategory: &p.ProductCategory,
			ProductID:       p.ProductID,
			RequireLevel:    p.RequireLevel,
			ComboType:       p.ComboType,
			LevelType:       &p.LevelType,
			CarpoolType:     &p.CarpoolType,
			IsSpecialPrice:  &p.IsSpecialPrice,
			EstimateID:      &p.EstimateID,
			AirportType:     &p.AirportType,
			SceneType:       &sceneType,
			SubGroupID:      &p.SubGroupId,
			APIGuideInfo: &AthenaApiv3.ApiGuideInfo{
				GuideType:          buildGuideType(productFull),
				PriceInfo:          rp.buildPriceInfo(productFull, p), // 价格信息
				IsDualCarpoolPrice: isDualCarpoolPrice(productFull),
				CarpoolPriceType:   buildCarpoolPriceType(p),
			},
			ExtraInfo: rp._buildProductExtraInfo(productFull),

			// mock数据(防止athena panic)
			GuideActivity: make([]*AthenaApiv3.GuideActivity, 0),
			IsTripCloud:   &isTC,
		}

		if rp.orderMatchType == EstimateOrderMatchType {
			apiAddProduct.FormShowType = &p.BizInfo.FormShowType
			if p.BizInfo.SpecialOrderInfo != nil {
				apiAddProduct.DepartureTime = &p.BizInfo.SpecialOrderInfo.DepartureTime
			}

			if p.BizInfo.UserMemberProfile.LevelID > 0 {
				rp.bubbleRecommendReq.AthenaBubbleReq.ExtraInfo["member_level_id"] = cast.ToString(p.BizInfo.UserMemberProfile.LevelID)
			}

			if productFull.GetBillInfo().HistoryExtraMap != nil {
				data := productFull.GetBillInfo().HistoryExtraMap
				switch data.(type) {
				case map[string]interface{}:
					if argueCarInfoString, ok := data.(map[string]interface{})["argue_car_info"].(string); ok {
						rp.bubbleRecommendReq.AthenaBubbleReq.ExtraInfo["bargain_recommend_status"] =
							gjson.Get(argueCarInfoString, "bubble_info").Get("recommend_status").String()
						rp.bubbleRecommendReq.AthenaBubbleReq.ExtraInfo["price_strategy_type"] =
							gjson.Get(argueCarInfoString, "price_strategy_type").String()
					}

				}
			}
		}

		estimateCarList = append(estimateCarList, apiAddProduct)

	}

	if len(estimateCarList) == 0 {
		return true
	}

	rp.bubbleRecommendReq.AthenaBubbleReq.APIAddProduct = estimateCarList
	if len(products) > 0 {
		bizInfo := products[0].GetCommonBizInfo()
		rp.bubbleRecommendReq.AthenaBubbleReq.CompensationInfo = buildCompensation(bizInfo)
		rp.bubbleRecommendReq.AthenaBubbleReq.DefaultSelectedCompensationInfo = buildDefaultSelectedCompensationInfo(bizInfo)
	}

	resp := athena.GetAthenaBubbleRecommend(ctx, rp.bubbleRecommendReq)
	if resp != nil {
		rp.resp = resp
	}
	return true

}

func buildCompensation(info models.CommonBizInfo) []*AthenaApiv3.CompensationProduct {
	if len(info.NoAnswerCompensationData) <= 0 {
		return nil
	}

	var compensationProductList = make([]*AthenaApiv3.CompensationProduct, 0)

	for _, data := range info.NoAnswerCompensationData {
		if data == nil {
			continue
		}

		if data.PrivilegeSource != nil && *data.PrivilegeSource == "peak_season" {
			compensationProductList = append(compensationProductList, &AthenaApiv3.CompensationProduct{
				Status:              1,
				Privilege:           *data.PrivilegeSource,
				ProductCategoryList: data.ProductCategoryList,
			})
		}
	}

	return compensationProductList
}

func buildDefaultSelectedCompensationInfo(info models.CommonBizInfo) *AthenaApiv3.DefaultSelectedCompensationInfo {
	var (
		compensationStatus int32
	)

	productCategoryList := make([]int64, 0)
	if info.DefaultSelectedCompensation != nil && info.DefaultSelectedCompensation.Decision == 1 &&
		info.DefaultSelectedCompensation.IsAddInsure == false { // 还没有赔付，需要athena给包框
		productCategoryList = info.DefaultSelectedCompensation.Contents.ProductCategoryList
		compensationStatus = 1
	}

	return &AthenaApiv3.DefaultSelectedCompensationInfo{
		CompensateStatus:    util.Int32Ptr(compensationStatus),
		ProductCategoryList: productCategoryList,
	}
}

func getDefaultSelect(commonBizInfo models.CommonBizInfo, inOrderMatch bool) (list []*AthenaApiv3.PreferenceProductItem) {
	multiRequireProduct := commonBizInfo.MultiRequireProduct
	if len(multiRequireProduct) == 0 {
		return
	}

	for _, item := range multiRequireProduct {
		judge := item.IsSelected == 1
		// 等应答不判断IsSelected
		if inOrderMatch {
			judge = true
		}
		if judge {
			list = append(list, &AthenaApiv3.PreferenceProductItem{
				ProductCategory: int32(item.ProductCategory),
			})
		}
	}
	return
}

func buildGuideType(p *biz_runtime.ProductInfoFull) int32 {
	switch p.GetBizInfo().FormShowType {
	case consts.FormShowTypeGuide:
		return 2
	case consts.FormShowTypeAnycar:
		return 1
	}
	return 1
}

func buildCarpoolPriceType(p *models.Product) *int32 {
	carpoolPriceType := int32(0)
	if p.CarpoolPriceType != 0 {
		carpoolPriceType = p.CarpoolPriceType
	}
	return &carpoolPriceType
}

func isDualCarpoolPrice(productFull *biz_runtime.ProductInfoFull) *bool {
	bIsDualCarpoolPrice := false
	if productFull.IsDualCarpoolPrice() {
		bIsDualCarpoolPrice = true
	}
	return &bIsDualCarpoolPrice
}

func (rp *BubbleRecommendInfo) buildPriceInfo(productFull *biz_runtime.ProductInfoFull, p *models.Product) map[string]string {
	var priceInfo = make(map[string]string)
	if productFull.GetBillInfo() != nil {

		priceInfo["estimate_id"] = p.EstimateID

		totalFee := map[string]interface{}{
			"estimate_fee":          productFull.GetEstimateFee(),
			"personal_estimate_fee": productFull.GetPersonalEstimateFee(),
		}
		if productFull.GetCouponInfo() != nil {
			couponAmount, err := strconv.ParseFloat(productFull.GetCouponInfo().Amount, 64)
			if err == nil {
				totalFee["coupon_amount"] = strconv.FormatFloat(couponAmount/100, 'f', 2, 64)
			}
		}
		if productFull.GetBillInfo().HistoryExtraMap != nil {
			data := productFull.GetBillInfo().HistoryExtraMap
			switch data.(type) {
			case map[string]interface{}:
				totalFee["dynamic_info"] = data.(map[string]interface{})["dynamic_info"]
				totalFee["discount_info"] = data.(map[string]interface{})["discount_info"]
				if v, ok := data.(map[string]interface{})["price_privilege_type"].(int); ok {
					priceInfo["price_privilege_type"] = strconv.Itoa(v)
				}
				if v, ok := data.(map[string]interface{})["sp_open"].(json.Number); ok {
					priceInfo["sp_open"] = v.String()
				}
				if v, ok := data.(map[string]interface{})["price_hide_level"].(string); ok {
					priceInfo["price_hide_level"] = v
				}
			}
		}

		if estimate_pc_id.EstimatePcIdHuiXuanCar == productFull.GetProductCategory() {
			leftPrice, err1 := strconv.ParseInt(productFull.GetFastRangeRecommendInfo(consts.RecommendPriceLower), 10, 64)
			rightPrice, err2 := strconv.ParseInt(productFull.GetFastRangeRecommendInfo(consts.RecommendPriceUpper), 10, 64)
			if err1 == nil && err2 == nil {
				totalFee["price_range_show_price"] = productFull.GetBizInfo().RecommendPriceLower
				totalFee["left_range_price"] = leftPrice
				totalFee["right_range_price"] = rightPrice
			}
		}

		priceInfo["total_fee"] = util.ToJSONStringNotNull(totalFee)

		priceInfo["dynamic_diff_price"] = strconv.FormatFloat(productFull.GetBillInfo().DynamicDiffPrice, 'f', 2, 64)
		priceInfo["cap_price"] = strconv.FormatFloat(productFull.GetBillInfo().CapPrice, 'f', 2, 64)
		priceInfo["time_cost"] = strconv.FormatInt(productFull.GetBillInfo().DriverMinute, 10)
		priceInfo["count_price_type"] = strconv.Itoa(int(productFull.GetBillInfo().CountPriceType))
		priceInfo["pre_total_fee"] = strconv.FormatFloat(productFull.GetBillInfo().PreTotalFee, 'f', 2, 64)
		priceInfo["fee_detail_info"] = util.ToJSONStringNotNull(productFull.GetBillFeeDetailInfo())
		priceInfo["driver_metre"] = cast.ToString(productFull.GetBillDriverMetre())

		// 拼车数据
		buildCarpoolParams(productFull, priceInfo)

		// 支付方式
		if productFull.GetPaymentInfo() != nil {
			priceInfo["payment_type"] = strconv.Itoa(int(productFull.GetPaymentInfo().DefaultPayType))
		}

		// 优惠处理
		buildDiscountInfoParams(productFull, priceInfo)

	}
	return priceInfo
}

func buildCarpoolParams(p *biz_runtime.ProductInfoFull, priceInfo map[string]string) {

	fee, b := p.GetCarpoolFailEstimateFee()
	if b && p.IsDualCarpoolPrice() {

		failCouponAmount := 0
		if p.GetCarpoolFailCouponInfo() != nil {
			failCouponAmount = cast.ToInt(p.GetCarpoolFailCouponInfo().Amount)
		}

		var data = map[string]map[string]interface{}{
			"success": {
				"estimate_fee":  p.GetEstimateFee(),
				"coupon_amount": cast.ToInt(p.GetCouponAmount()),
			},
			"failed": {
				"estimate_fee":  fee,
				"coupon_amount": failCouponAmount,
			},
		}
		priceInfo["carpool_dual_price_info"] = util.ToJSONStringNotNull(data)
	}

	if p.GetCarpoolType() == carpool.TypeLowPrice {
		extendList := p.GetExtendList()
		if len(extendList) == 0 {
			return
		}
		priceScene := make(map[string]interface{})
		for _, extend := range extendList {
			if extend == nil || extend.SceneMark == nil {
				continue
			}
			seatNum := extend.SceneMark["seat_num"]
			poolNum := extend.SceneMark["pool_num"]
			isCarpoolSuccess := extend.SceneMark["is_carpool_success"]
			if isCarpoolSuccess == "1" {
				key := fmt.Sprintf("success_%v_%v", seatNum, poolNum)
				priceScene[key] = map[string]float64{
					EstimateFee: extend.EstimateFee,
				}
			} else {
				failKey := fmt.Sprintf("fail_%v", seatNum)
				priceScene[failKey] = map[string]float64{
					EstimateFee: extend.EstimateFee,
				}
			}
		}
		priceInfo["low_price_carpool_price_info"] = util.ToJSONStringNotNull(priceScene)
	}

	if p.GetCarpoolType() == carpool.TypeMiniBus {
		var priceScene = make(map[string]interface{})
		priceScene["failed"] = map[string]float64{
			EstimateFee: p.GetEstimateFee(),
		}
		priceInfo["minibus_price_info"] = util.ToJSONStringNotNull(priceScene)
	}
}

func buildDiscountInfoParams(productFull *biz_runtime.ProductInfoFull, priceInfo map[string]string) {
	if productFull.GetCouponInfo() != nil && len(productFull.GetCouponInfo().CouponSource) > 0 {
		var couponKey string
		if "pope" == productFull.GetCouponInfo().CouponSource {
			couponKey = "activity_coupon"
		} else {
			couponKey = "default_coupon"
		}
		var couponInfo = map[string]map[string]interface{}{
			couponKey: {
				"batch_id":      productFull.GetCouponInfo().BatchId,
				"batch_type":    productFull.GetCouponInfo().CouponType,
				"discount":      productFull.GetCouponInfo().Discount,
				"coupon_amount": productFull.GetCouponInfo().Amount,
				"custom_tag":    productFull.GetCouponInfo().CustomTag,
			},
		}
		priceInfo["coupon_info"] = util.ToJSONStringNotNull(couponInfo)
	}

	if productFull.GetRevolvingAccountRebate() != nil {
		priceInfo["aplus_pay_return"] = productFull.GetRevolvingAccountRebate().Amount
		priceInfo["crazy_aplus_pay_return_type"] = cast.ToString(productFull.GetRevolvingAccountRebate().ActivityType)
		priceInfo["crazy_aplus_pay_return_multiple"] = cast.ToString(productFull.GetRevolvingAccountRebate().Multiple)
	}
	if productFull.GetRevolvingAccountDiscount() != nil {
		priceInfo["revolving_account_balance"] = productFull.GetRevolvingAccountDiscount().Amount
	}
}

func (rp *BubbleRecommendInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

	rp.CheckedPcIdMap = make(map[int64]int)

	if rp.orderMatchType == EstimateOrderMatchType && (rp.resp == nil || len(rp.resp.CheckedProductCategory) == 0) {
		// 兜底策略处理 有快车优先兜快车
		_, hasFast := rp.pcId2EstimateId[estimate_pc_id.EstimatePcIdFastCar]
		if hasFast {
			rp.CheckedPcIdMap[estimate_pc_id.EstimatePcIdFastCar] = consts.Checked
		} else {
			for pcID := range rp.pcId2EstimateId {
				rp.CheckedPcIdMap[pcID] = consts.Checked
				break
			}
		}
	}

	if rp.resp == nil {
		return
	}

	// 默认拼
	if info.RecCarpoolFormSytle != 0 {
		info.RecCarpoolInfo = models.RecCarpoolInfo{
			RecCarpoolInfoMap:    make(map[int32]*models.RecCarpoolPCInfo),
			RecCarpoolDefaultNum: int16(rp.selectStatusRecCarpool),
		}
		for _, item := range rp.resp.CheckedProductCategory {
			if item == nil {
				continue
			}
			if info.RecCarpoolInfo.RecCarpoolInfoMap[item.ProductCategory] == nil {
				info.RecCarpoolInfo.RecCarpoolInfoMap[item.ProductCategory] = &models.RecCarpoolPCInfo{}
			}
			info.RecCarpoolInfo.RecCarpoolInfoMap[item.ProductCategory].IsSelected = true
		}
		info.RecCarpoolInfo.RecCarpoolOther = rp.resp.CarpoolFormInfo

		// 二次预估数据
		info.RecCarpoolInfo.RecCarpoolMultiRequireProduct = make(map[int32]struct{})
		if info.RecCarpoolInfo.RecCarpoolDefaultNum == 0 {
			for _, item := range info.MultiRequireProduct {
				if item.IsSelected == 1 {
					info.RecCarpoolInfo.RecCarpoolMultiRequireProduct[int32(item.ProductCategory)] = struct{}{}
				}
			}
		}
	}

	// 存储默勾
	if len(rp.resp.CheckedProductCategory) > 0 {
		for _, item := range rp.resp.CheckedProductCategory {
			rp.CheckedPcIdMap[int64(item.GetProductCategory())] = consts.Checked
		}
	}

	// 存储聚合车型推荐信息
	if rp.resp.SubGroupRecList != nil && len(rp.resp.SubGroupRecList) > 0 {
		info.SubGroup2RecPos = make(map[int32]int32)
		info.SubGroup2IsOutBox = make(map[int32]bool)
		for _, subGroupRecItem := range rp.resp.SubGroupRecList {

			// 盒子位置
			info.SubGroup2RecPos[subGroupRecItem.GetSubGroupID()] = subGroupRecItem.GetRecPos()

			// 需要出盒子的品类
			for _, pcID := range subGroupRecItem.SplitCategory {
				info.SubGroup2IsOutBox[pcID] = true
			}
		}

	}

	// 存储推荐信息
	if len(rp.resp.RecResult_) > 0 {
		info.RecPosMap = make(map[int32]int32)
		info.GuidePcIds = make([]int32, 0)
		info.ETInfoMap = make(map[int32]*AthenaApiv3.EstimatedTimeInfo)
		info.RecommendInfoMap = make(map[int32]*AthenaApiv3.RecommendInfo)
		info.RecommendExtraInfoMap = make(map[int32]map[string]string)
		for _, recItem := range rp.resp.RecResult_ {
			if recItem == nil {
				continue
			}

			// 移动盒子
			if recItem.SubGroupID != nil {
				if recItem.GetSubGroupID() == consts.SubGroupIdDiscountAlliance {
					if len(info.PcID2NewSubGroupId) <= 0 {
						info.PcID2NewSubGroupId = make(map[int32]int32)
					}
					info.PcID2NewSubGroupId[recItem.GetProductCategory()] = recItem.GetSubGroupID()
				}
			}

			// 存储位置
			info.RecPosMap[recItem.GetProductCategory()] = recItem.RecPos

			// 导流车型
			if recItem.RecPos == consts.RecPosTopArea && recItem.GetProductCategory() != 0 {
				info.GuidePcIds = append(info.GuidePcIds, recItem.GetProductCategory())
			}

			// 存储车型预期信息
			if recItem.EstimatedTimeInfo != nil {
				info.ETInfoMap[recItem.GetProductCategory()] = recItem.EstimatedTimeInfo
			}

			// 推荐气泡
			info.RecommendInfoMap[recItem.GetProductCategory()] = recItem.Info

			// 橙意单折扣(分单给降价司机)
			if recItem.DiscountForm != nil && *recItem.DiscountForm == 1 {
				if len(rp.ChengYiDanDiscount) <= 0 {
					rp.ChengYiDanDiscount = make(map[int32]bool)
				}
				rp.ChengYiDanDiscount[recItem.ProductCategory] = true
			}

			// extra_info
			info.RecommendExtraInfoMap[recItem.GetProductCategory()] = recItem.ExtraInfo
		}
	}

	// 存储置顶信息
	if rp.resp.TopRec != nil && len(rp.resp.TopRec.TopRecList) > 0 {
		info.TopData = new(models.TopData)
		for _, item := range rp.resp.TopRec.TopRecList {

			switch item.Type {
			case SingleType:
				info.TopData.TopPcIdList = append(info.TopData.TopPcIdList, int64(item.GetCategory()))
			case ShortDistanceType:
				info.TopData.TopSubGroupIdList = append(info.TopData.TopSubGroupIdList, int64(item.GetCategory()))
			}
		}
	}

	// 存储置顶信息
	if rp.resp.TopRec != nil && len(rp.resp.TopRec.TopRecList) > 0 {
		info.TopData = new(models.TopData)
		for _, item := range rp.resp.TopRec.TopRecList {

			switch item.Type {
			case SingleType:
				info.TopData.TopPcIdList = append(info.TopData.TopPcIdList, int64(item.GetCategory()))
			case ShortDistanceType:
				info.TopData.TopSubGroupIdList = append(info.TopData.TopSubGroupIdList, int64(item.GetCategory()))
			}
		}
	}
	// 存储置顶推荐
	info.TopRec = rp.resp.TopRec

	// 品类排序信息
	info.ProductSortInfo = rp.resp.ProductSortInfo

	// 筛选器
	if rp.resp.FilterInfo != nil && len(rp.resp.FilterInfo.FilterList) > 0 {
		info.FilterInfo = rp.resp.FilterInfo
	}

	// 供需失衡场景
	if rp.resp.SupplyInfo != nil && rp.resp.SupplyInfo.GetIsBubbleShortSupply() != 0 && rp.resp.ExtraInfo != nil {
		extraInfo := rp.resp.GetExtraInfo()
		if groupName, ok := extraInfo["bubble_supply_exp_group"]; ok {
			if groupName != "" && groupName != "empty_group" {
				info.IsImbalanced = 1
			}
		}

	}

	if rp.resp.SupplyInfo != nil && rp.resp.SupplyInfo.SupplySceneInfo != nil {
		info.SupplySceneInfo = rp.resp.SupplyInfo.SupplySceneInfo
	}

	// other
	if len(rp.resp.GetExtraInfo()) > 0 {
		m := rp.resp.GetExtraInfo()

		if num, ok := m["show_car_num"]; ok {
			info.ShowCarNum = cast.ToInt(num)
		} else {
			info.ShowCarNum = 5
		}

		if _, ok := m["hit_luxr_premium_protect"]; ok {
			info.HitLuxPremiumProtect = 1
		} else {
			info.HitLuxPremiumProtect = 0
		}
	}
}

func (rp *BubbleRecommendInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	productCategory := product.GetProductCategory()
	if rp.CheckedPcIdMap[productCategory] == consts.Checked {
		info.CheckStatus = consts.Checked
	}

	info.BargainIsMultiSelect = rp.buildBargainIsMultiSelect(ctx, product)
	// athena出盒子
	if len(product.BaseReqData.CommonBizInfo.SubGroup2IsOutBox) > 0 {
		if v, ok := product.BaseReqData.CommonBizInfo.SubGroup2IsOutBox[int32(productCategory)]; ok && v {
			product.Product.SubGroupId = 0
		}
	}

	// athena 移动盒子
	if len(product.BaseReqData.CommonBizInfo.PcID2NewSubGroupId) > 0 {
		if newSubGroupId, ok := product.BaseReqData.CommonBizInfo.PcID2NewSubGroupId[int32(productCategory)]; ok {
			product.Product.SubGroupId = newSubGroupId
		}
	}

	if len(rp.ChengYiDanDiscount) > 0 && rp.ChengYiDanDiscount[int32(productCategory)] {
		go setChengYiDanCache(ctx, product)
	}

	// athena返回的呼叫人数
	if rp.resp != nil && rp.resp.SupplyInfo != nil && rp.resp.SupplyInfo.ProductSupplyDataMap != nil &&
		rp.resp.SupplyInfo.ProductSupplyDataMap[int32(productCategory)] != nil {
		product.Product.BizInfo.OrderReceiveCnt = rp.resp.SupplyInfo.ProductSupplyDataMap[int32(productCategory)].GetOrderReceiveCnt()
	}
}

func setChengYiDanCache(ctx context.Context, p biz_runtime.ProductInfoFull) {
	if _, err := redis.GetClient().SetEx(ctx,
		"P_GUIDE_DISCOUNT_FORM"+p.GetEstimateID(), 600*time.Second, "1"); err != nil {

		log.Trace.Warnf(ctx, "setChengYiDanCache", "set redis failed %s", err)
	}
}

// HandlerFilter athena最终供需准入
func (rp *BubbleRecommendInfo) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) (filter []models.ProductCategory) {

	for _, full := range productMap {
		// 过滤导流品类，Athena未设置需要导流的
		if full.Product.BizInfo.FormShowType == consts.FormShowTypeGuide &&
			!util.InArrayInt32(int32(full.GetProductCategory()), full.BaseReqData.CommonBizInfo.GuidePcIds) {
			filter = append(filter, models.ProductCategory(full.GetProductCategory()))
		}
	}

	if rp.resp == nil || len(rp.resp.DisableProductCategory) < 1 {
		return
	}

	for _, item := range rp.resp.DisableProductCategory {
		if item.ProductItem == nil {
			continue
		}
		filter = append(filter, models.ProductCategory(item.ProductItem.ProductCategory))
	}

	return filter
}

func getTabList(tabListStr string) []*AthenaApiv3.TabItem {
	var tabList []*AthenaApiv3.TabItem
	_ = json.Unmarshal([]byte(tabListStr), &tabList)
	return tabList
}

func (rp *BubbleRecommendInfo) _buildProductExtraInfo(full *biz_runtime.ProductInfoFull) map[string]string {
	res := map[string]string{}
	// 是否命中出租车峰期
	for _, displayLine := range full.GetBillDisplayLines() {
		if displayLine.Name == BillItemName {
			res[RecognizeTaxiPeakKey] = cast.ToString(1)
		}
	}

	if full.Product.ProductCategory == estimate_pc_id.EstimatePcIdBargain {
		if len(full.GetBizInfo().StartFenceList) > 0 {
			res["start_fence_id"] = util.Int64SliceToString(full.GetBizInfo().StartFenceList)
		}
		if len(full.GetBizInfo().StopFenceList) > 0 {
			res["stop_fence_id"] = util.Int64SliceToString(full.GetBizInfo().StopFenceList)
		}
	}

	return res
}

// 司乘议价是否多勾
func (rp *BubbleRecommendInfo) buildBargainIsMultiSelect(ctx context.Context, product biz_runtime.ProductInfoFull) bool {

	if product.GetProductCategory() != estimate_pc_id.EstimatePcIdBargain {
		return false
	}

	params := map[string]string{
		"phone":         product.GetUserPhone(),
		"app_version":   product.GetAppVersion(),
		"access_key_id": util.ToString(product.GetAccessKeyId()),
		"city":          util.ToString(product.GetCityID()),
		"pid":           util.ToString(product.GetUserPID()),
	}
	if apollo.FeatureToggle(ctx, "bargain_recommend_v2_switch", params["pid"], params) {
		return true
	}
	return false
}
