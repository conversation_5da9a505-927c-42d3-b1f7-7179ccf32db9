package rpc_process

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hundun"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

type AdditionalServiceHundun struct {
	needSupportServiceIdList []int64                                                  // 每个品类必须支持服务id列表
	baseReq                  *models.BaseReqData                                      // 请求信息
	pcId2ServiceListMap      map[models.ProductCategory][]*hundunClient.PcServiceData // hundun 响应
}

// NewAdditionalService http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=731194098
func NewAdditionalServiceHundun(ctx context.Context, req *models.BaseReqData) *AdditionalServiceHundun {
	return &AdditionalServiceHundun{
		needSupportServiceIdList: getNeedFilterServiceIdList(ctx, req),
		baseReq:                  req,
		pcId2ServiceListMap:      make(map[models.ProductCategory][]*hundunClient.PcServiceData),
	}
}

// 获取rpc错误信息
func (s *AdditionalServiceHundun) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch RPC  请求hundun, 返回品类对应服务
func (s *AdditionalServiceHundun) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		targetProducts = make([]*models.Product, 0)
		reqs           []*hundun.HundunNodeReq
		hundunReq      = &hundunClient.SceneReq{}
		nodeReq        string
	)

	if len(products) == 0 {
		return false
	}

	apolloParam := s.baseReq.GetApolloParam()
	apolloParam["caller"] = "mamba"

	//混沌降级开关
	if !apollo.FeatureToggle(ctx, "hundun_access_controller", strconv.Itoa(int(s.baseReq.PassengerInfo.UID)), apolloParam) {
		return false
	}

	commonData := &hundunClient.SceneCommReq{
		Caller:      hundun.Caller,
		AppVersion:  s.baseReq.CommonInfo.AppVersion,
		AccessKeyId: cast.ToString(s.baseReq.CommonInfo.AccessKeyID),
		Channel:     int32(s.baseReq.CommonInfo.Channel),
		Pid:         s.baseReq.PassengerInfo.PID,
		Area:        s.baseReq.AreaInfo.Area,
		Phone:       s.baseReq.PassengerInfo.Phone,
		Lang:        s.baseReq.CommonInfo.Lang,
		Callee:      hundun.Callee,
		District:    s.baseReq.AreaInfo.District,
	}

	for _, p := range products {
		if p.IsTripcloudProduct(ctx) {
			continue
		}

		targetProducts = append(targetProducts, p)

		airportId := int64(s.baseReq.CommonInfo.AirportId)
		req := &hundun.HundunNodeReq{
			ProductCategory:   cast.ToInt(p.ProductCategory),
			ProductId:         cast.ToInt(p.ProductID),
			BusinessId:        cast.ToInt(p.BusinessID),
			ComboType:         cast.ToInt(p.ComboType),
			CarpoolType:       cast.ToInt(p.CarpoolType),
			RequireLevel:      p.RequireLevel,
			Pid:               s.baseReq.PassengerInfo.PID,
			Phone:             s.baseReq.PassengerInfo.Phone,
			AccessKeyId:       cast.ToInt(s.baseReq.CommonInfo.AccessKeyID),
			AppVersion:        s.baseReq.CommonInfo.AppVersion,
			Lang:              s.baseReq.CommonInfo.Lang,
			Area:              cast.ToInt(s.baseReq.AreaInfo.Area),
			Flat:              s.baseReq.AreaInfo.FromLat,
			Flng:              s.baseReq.AreaInfo.FromLng,
			Tlat:              s.baseReq.AreaInfo.ToLat,
			Tlng:              s.baseReq.AreaInfo.ToLng,
			Type:              cast.ToInt(s.baseReq.CommonInfo.OrderType),
			PageType:          cast.ToInt(s.baseReq.CommonInfo.PageType),
			CallCarType:       cast.ToInt(s.baseReq.CommonInfo.CallCarType),
			DepartureTime:     s.baseReq.CommonInfo.DepartureTime,
			MenuId:            s.baseReq.CommonInfo.MenuID,
			SubMenuId:         "",
			TrafficNumber:     s.baseReq.CommonInfo.TrafficNumber,
			TrafficDepTime:    s.baseReq.CommonInfo.TrafficDepTime,
			AirportId:         cast.ToInt(airportId),
			RailwayType:       cast.ToInt(p.RailwayType),
			FlightDepCode:     s.baseReq.CommonInfo.FlightDepCode,
			FlightDepTerminal: s.baseReq.CommonInfo.FlightDepTerminal,
			ExtraInfo:         make(map[string]string, 1),
		}
		req.ExtraInfo["gender"] = strconv.Itoa(int(s.baseReq.PassengerInfo.UserGender))
		reqs = append(reqs, req)
	}

	if len(reqs) == 0 {
		return false
	}

	nodeData, err := json.Marshal(reqs)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagCheckDiffS2H, "req marshel err")
		return false
	}

	nodeReq = fmt.Sprintf("%s", nodeData)
	hundunReq.SetCommonReq(commonData)
	hundunReq.SetNodeReq(nodeReq)
	s.pcId2ServiceListMap = hundun.GetService(ctx, hundunReq)
	return true
}

// BuildCommonBizInfo RPC
func (s *AdditionalServiceHundun) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo RPC
func (s *AdditionalServiceHundun) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {

	// 构建每个品类自己支持的服务
	if value, ok := s.pcId2ServiceListMap[models.ProductCategory(product.ProductCategory)]; ok {
		if len(value) > 0 {
			privateBizInfo.CustomFeatureList = value
		}
	}
}

// Exec Filter 选择过滤的品类Id
func (s *AdditionalServiceHundun) Exec(ctx context.Context, products []*models.Product) (needFilterPcIds []models.ProductCategory) {
	if len(s.needSupportServiceIdList) < 1 {
		// 没有必须支持的服务, 不需要过滤
		return
	}

	// 遍历品类
	for _, product := range products {
		//宠物出行屏蔽增值服务过滤逻辑
		if product.ProductID == product_id.ProductIdPetFastCar {
			continue
		}

		if len(product.BizInfo.CustomFeatureList) == 0 {
			// 需要过滤
			needFilterPcIds = append(needFilterPcIds, models.ProductCategory(product.ProductCategory))
			continue
		}

		// 必须包含needFilterServiceIdMap内所有服务
		productSupportServiceMap := make(map[int64]interface{})
		for _, featureStruct := range product.BizInfo.CustomFeatureList {
			productSupportServiceMap[featureStruct.ServiceId] = true
		}

		for _, serviceId := range s.needSupportServiceIdList {
			// 遍历品类支持的服务, 如果不在必须列表, 则剔除
			if _, ok := productSupportServiceMap[serviceId]; !ok {
				needFilterPcIds = append(needFilterPcIds, models.ProductCategory(product.ProductCategory))
				break
			}
		}
	}

	return needFilterPcIds
}
