package biz_runtime

import (
	"context"
	"encoding/json"
	"errors"
	"math"
	"net/http"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"
	"git.xiaojukeji.com/lego/dirpc-go"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"github.com/spf13/cast"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	passenger_common "git.xiaojukeji.com/gulfstream/passenger-common/dto"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

var (
	ErrorNoProductForEstimate = errors.New("没有可预估品类")
)

const (
	DefaultOpenAppointRange = 2
)

type ProductInfoFull struct {
	BaseReqData  *models.BaseReqData
	Product      *models.Product
	BillDetail   *PriceApi.BillDetail     // Deprecated: 弃用 V3接口没有这个东西
	DiscountInfo []*PriceApi.DiscountInfo // Deprecated: 弃用 V3接口没有这个东西
	PayInfo      *PriceApi.PaymentInfo    // Deprecated: 弃用 V3接口没有这个东西
	ExtraInfo    map[string]string        `json:"extra_info"` //扩展字段:目前是透传estimate/lite接口的扩展字段信息

	hu *PriceApi.EstimateNewFormData // 糊: 不直接暴露字段, 通过若干方法访问内部数据

	selectorInput map[string]interface{}

	_ap *apollo_model.ParamsConnector
}

func (a *ProductInfoFull) GetFontScaleType() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.FontScaleType
}

// GetCurrentSeatNum 发单座位数
func (a *ProductInfoFull) GetCurrentSeatNum() int32 {
	return a.Product.BizInfo.CarpoolSeatNum
}

// GetBillTaxiPeakFee 从账单获取峰期加价信息费金额
func (a *ProductInfoFull) GetBillTaxiPeakFee() float64 {
	if a.GetBillDetail() == nil || a.GetBillDetail().FeeDetailInfo == nil {
		return 0
	}
	fee := float64(0)
	feeDetailMap := a.GetBillDetail().FeeDetailInfo
	if peakFee, ok := feeDetailMap["taxi_peak_price"]; ok {
		fee += peakFee
	}
	if discountFee, ok := feeDetailMap["taxi_peak_discount"]; ok {
		fee += discountFee
	}
	return fee
}

func (a *ProductInfoFull) GetPriceInfo() *PriceApi.EstimateNewFormBillInfo {
	tmp := a.hu.BillInfo
	return tmp
}

func (a *ProductInfoFull) GetExactEstimateFee() float64 {
	return a.hu.ExactEstimateFee
}

func (a *ProductInfoFull) JudgePriceInfoValid() bool {
	if a.hu == nil {
		return false
	}

	return true
}

func (a *ProductInfoFull) GetDiscountCard() *PriceApi.DiscountCard {
	if a.hu == nil || a.hu.DiscountSet == nil {
		return nil
	}

	return a.hu.DiscountSet.DiscountCard

}

func (a *ProductInfoFull) GetBargainBubbleRecommendFee() float64 {
	if a.GetBillExtraMap() == nil {
		return 0.0
	}

	amount, ok := a.GetBillExtraMap()["bargain_bubble_recommend_fee"].(json.Number)
	if !ok {
		return 0.0
	}

	if v, err := amount.Float64(); err == nil {
		return v
	}
	return 0.0
}

func (a *ProductInfoFull) GetCashBack() *PriceApi.CashbackXinZhuInfo {
	if a.hu != nil && a.hu.DiscountSet != nil {
		return a.hu.DiscountSet.CashbackXinZhuInfo
	}
	return nil
}

func (a *ProductInfoFull) IsTripcloud(ctx context.Context) bool {
	return a.Product.IsTripcloudProduct(ctx)
}

// NewProductsGenerator ...
// 返回默认的ProductsGenerator
func NewProductsGenerator() *ProductsGenerator {
	gen, _ := NewProductsGeneratorWithOption()
	return gen
}

// GeneratorOption ProductsGenerator的初始化参数
type GeneratorOption func(*ProductsGenerator) error

// NewProductsGeneratorWithOption ...
func NewProductsGeneratorWithOption(opts ...GeneratorOption) (*ProductsGenerator, error) {
	generator := &ProductsGenerator{
		BaseReqData:     &models.BaseReqData{},
		removedProducts: make([]*models.Product, 0),
	}

	for _, opt := range opts {
		if err := opt(generator); err != nil {
			return nil, err
		}
	}

	return generator, nil
}

func WithReqFrom(from string) GeneratorOption {
	// http://ab.intra.xiaojukeji.com/launch/1047/normal/104037?activeKey=detail
	return func(pg *ProductsGenerator) error {
		pg.reqFrom = from
		return nil
	}
}

func WithBaseReq(req *models.BaseReqData) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if req == nil {
			return consts.ErrorBaseReqNotBuild
		}
		pg.BaseReqData = req
		pg.IsBaseReqInit = true // ?

		return nil
	}
}

func WithCommonReq(ctx context.Context, request *proto.MultiEstimatePriceRequest) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if request == nil {
			return consts.ErrorInvalidParams
		}
		pg.BaseReqData.CommonInfo.InitByReq(request)
		if err := pg.BaseReqData.AreaInfo.InitByReq(ctx, request); err != nil {
			return consts.ErrorFailToParseLocation
		}
		if err := pg.BaseReqData.PassengerInfo.InitByReq(ctx, request.Token, request.UserType); err != nil {
			return consts.ErrorFailToGetUserInfo
		}
		pg.BaseReqData.CommonBizInfo.InitByRequest(ctx, request)
		pg.IsBaseReqInit = true
		return nil
	}
}

// WithSeatDetailInfoV2 ...
func WithSeatDetailInfoV2(seatDetailInfo []*models.SeatDetailInfo) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if pg == nil {
			return errors.New("products generator is nil")
		}

		pg.BaseReqData.CommonBizInfo.SeatDetailInfo = seatDetailInfo
		return nil
	}
}

// WithPassengerDetailInfo ...
func WithPassengerDetailInfo(info *models.PassengerDetailInfo) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if pg == nil {
			return errors.New("products generator is nil")
		}

		pg.BaseReqData.CommonBizInfo.PassengerDetailInfo = info
		return nil
	}
}

// WithInventory ...
func WithInventory(passengerInventory int32, carryChildrenInventory int32) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if pg == nil {
			return errors.New("products generator is nil")
		}

		pg.BaseReqData.CommonBizInfo.MaxInventory = passengerInventory
		pg.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory = carryChildrenInventory

		return nil
	}
}

func NewProductsGeneratorByCommonReq(ctx context.Context, request *proto.MultiEstimatePriceRequest) (*ProductsGenerator, error) {
	return NewProductsGeneratorWithOption(WithCommonReq(ctx, request))
}

func WithEnableCapPrice(enableCapPrice *string, enableFastCapPrice *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		businessOption := map[string]string{}
		if enableCapPrice != nil && *enableCapPrice != "" {
			businessOption["enable_cap_price"] = *enableCapPrice
		}
		if enableFastCapPrice != nil && *enableFastCapPrice != "" {
			businessOption["enable_fast_cap_price"] = *enableFastCapPrice
		}
		if len(businessOption) > 0 {
			businessOptionBytes, _ := json.Marshal(businessOption)
			pg.BaseReqData.CommonInfo.SetBusinessOption(string(businessOptionBytes))
		}
		return nil
	}
}

func (pg *ProductsGenerator) getOpenAppointmentRange(req *proto.CarpoolEstimateRequest) int64 {
	param := ApolloModel.NewUser("").
		With("access_key_id", strconv.Itoa(int(req.AccessKeyId))).
		With("app_version", req.AppVersion).
		With("route_id", strconv.Itoa(int(req.RouteId))).
		With("pid", strconv.Itoa(int(pg.BaseReqData.PassengerInfo.PID))).
		With("city", strconv.Itoa(int(pg.BaseReqData.AreaInfo.City))).
		With("scene", pg.BaseReqData.CommonBizInfo.Scene)
	toggle, err := ApolloSDK.FeatureToggle("bus_open_appointment_time", param)
	if err != nil || !toggle.IsAllow() {
		return DefaultOpenAppointRange
	}
	i, err := strconv.ParseInt(toggle.GetAssignment().GetParameter("appiontment_range", ""), 10, 64)
	if err != nil {
		return DefaultOpenAppointRange
	} else {
		return i
	}
}

func WithRouteInfo(req *proto.CarpoolEstimateRequest) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if req == nil {
			return consts.ErrorInvalidParams
		}
		pg.BaseReqData.CommonBizInfo.RouteId = req.RouteId
		pg.BaseReqData.CommonBizInfo.StartStationId = req.StartStationId
		pg.BaseReqData.CommonBizInfo.EndStationId = req.EndStationId
		if req.BizSceneType != nil {
			pg.BaseReqData.CommonBizInfo.Scene = *req.BizSceneType
		}
		pg.BaseReqData.CommonBizInfo.OpenAppointmentRange = pg.getOpenAppointmentRange(req)

		return nil
	}
}

// WithRouteInfoV2 ...
func WithRouteInfoV2(routeInfo *models.RouteInfo) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if routeInfo == nil {
			return consts.ErrorInvalidParams
		}
		pg.BaseReqData.CommonBizInfo.RouteId = routeInfo.RouteId
		pg.BaseReqData.CommonBizInfo.StartStationId = routeInfo.StartStationId
		pg.BaseReqData.CommonBizInfo.EndStationId = routeInfo.EndStationId
		pg.BaseReqData.CommonBizInfo.CorrectStation = routeInfo.CorrectStation
		return nil
	}
}

func WithOrderInfo(ctx context.Context, request models.OrderMatchRequest, orderInfo *dos.OrderInfo, userInfo *passport.UserInfo, orderFeature *ufs.OrderFeature) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if request == nil || orderInfo == nil || userInfo == nil || orderFeature == nil {
			return consts.ErrorEmptyPriceReq
		}
		pg.BaseReqData.CommonInfo.InitByReqAndOrderInfo(ctx, orderInfo, request, orderFeature)
		pg.BaseReqData.CommonBizInfo.InitByOrderInfo(ctx, orderInfo, request)
		pg.BaseReqData.AreaInfo.InitByOrderInfo(ctx, orderInfo, orderFeature)
		pg.BaseReqData.PassengerInfo.BuildByUserInfo(ctx, userInfo, orderFeature)
		pg.BaseReqData.SendOrder.BuildByDosOrder(ctx, orderInfo, request.GetOrderId())
		pg.IsBaseReqInit = true
		return nil
	}
}

func WithSourceID(ctx context.Context, sourceID int32) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		pg.BaseReqData.CommonInfo.SetSourceID(sourceID)
		return nil
	}
}

func WithRiskCode(ctx context.Context, riskCode int32) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		pg.BaseReqData.CommonInfo.SetRiskCode(riskCode)
		return nil
	}
}

func WithScene(ctx context.Context, scene *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if scene == nil || *scene == "" {
			return nil
		}
		pg.BaseReqData.CommonBizInfo.Scene = *scene
		return nil
	}
}

func WithRebookInfo(ctx context.Context, req *proto.CarpoolEstimateRequest, data param_handler.CommonData) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if req.BizSceneType != nil && *req.BizSceneType == ticket_detail_consts.SceneRebook {
			orderHelper := util2.Helper{}
			orderParam, err := orderHelper.DecodeId(req.GetPreOrderId())
			if err != nil {
				log.Trace.Warnf(ctx, "WithRebookInfo", "decode order id fail, err:%v", err)
				return err
			}

			pg.BaseReqData.CommonBizInfo.Scene = req.GetBizSceneType()
			pg.BaseReqData.CommonBizInfo.PreOrderID = req.GetPreOrderId()
			pg.BaseReqData.CommonBizInfo.PreHighOrderID = util.Int642String(orderHelper.GetHighIntOrderId(orderParam.Oid, orderParam.District))
			pg.BaseReqData.CommonBizInfo.PreEstimateID = data.PreEstimateId
			pg.BaseReqData.CommonBizInfo.PassengerTickerInfo = make([]*passenger_common.PassengerDetail, 0)
			pg.BaseReqData.CommonBizInfo.SeletedBusServiceShiftId = req.GetSelectedBusServiceShiftId()
			pg.BaseReqData.CommonBizInfo.SelectedBusServiceDay = req.GetSelectedBusServiceDay()
			pg.BaseReqData.CommonBizInfo.PreBusServiceShiftId = data.PreBusServiceShiftId
			pg.BaseReqData.CommonBizInfo.PreDepartureTime = util.DateStringToUnixTime(ctx, data.PreDepartureTime)
			pg.BaseReqData.CommonBizInfo.IsCommonCashierOrder = data.IsCommonCashierOrder
			pg.BaseReqData.CommonBizInfo.RouteType = util.ToInt64(data.RouteType)
			// 根据端传的ticket_id筛选出改签车票的信息
			rebookIdMap := map[int64]struct{}{}
			ticketIds := make([]int64, 0)
			json.Unmarshal([]byte(req.GetPreRebookTicketIds()), &ticketIds)
			for _, id := range ticketIds {
				rebookIdMap[id] = struct{}{}
			}

			for _, ticket := range data.PassengerDetail {
				if ticket == nil {
					continue
				}
				if _, hit := rebookIdMap[int64(ticket.TicketId)]; hit {
					pg.BaseReqData.CommonBizInfo.PassengerTickerInfo = append(pg.BaseReqData.CommonBizInfo.PassengerTickerInfo, ticket)
				}
			}

			// 根据改签车票信息计算passenger_count(这时候传入的carpool_num应该为0）
			passengerCount := int32(0)
			for _, ticket := range pg.BaseReqData.CommonBizInfo.PassengerTickerInfo {
				if ticket.IsOccupySeat == 1 {
					passengerCount += 1
				}
			}
			pg.BaseReqData.CommonBizInfo.PassengerCount = util.Int32Ptr(passengerCount)

			// 构造seat_detail_info
			if req.SeatDetailInfo == nil || *req.SeatDetailInfo == "" {
				type2Cnt := map[seat_selection_consts.PassengerType]int32{
					seat_selection_consts.Adult:              0,
					seat_selection_consts.Children:           0,
					seat_selection_consts.CarryChildren:      0,
					seat_selection_consts.HomeOwnerAdult:     0,
					seat_selection_consts.HomeOwnerChildren:  0,
					seat_selection_consts.HomeOwnerOldMan:    0,
					seat_selection_consts.PreferentialPeople: 0,
				}
				type2Occupy := map[seat_selection_consts.PassengerType]int32{}
				for _, ticket := range pg.BaseReqData.CommonBizInfo.PassengerTickerInfo {
					if ticket == nil {
						continue
					}

					ticketType := seat_selection_consts.PassengerType(ticket.TicketType)
					if ticketType == seat_selection_consts.Undefined {
						ticketType = seat_selection_consts.Adult
					}

					type2Cnt[ticketType] += 1
					type2Occupy[ticketType] = ticket.IsOccupySeat
				}

				pg.BaseReqData.CommonBizInfo.SeatDetailInfo = make([]*models.SeatDetailInfo, 0)
				for t, cnt := range type2Cnt {
					pg.BaseReqData.CommonBizInfo.SeatDetailInfo = append(pg.BaseReqData.CommonBizInfo.SeatDetailInfo,
						&models.SeatDetailInfo{
							PassengerCount: cnt,
							PassengerType:  int32(t),
							IsOccupySeat:   type2Occupy[t],
						},
					)
				}
			}
		}

		return nil
	}
}

func WithBusServiceShiftId(ctx context.Context, id *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if id == nil || *id == "" {
			return nil
		}
		pg.BaseReqData.CommonBizInfo.BusServiceShiftId = *id
		return nil
	}
}

func WithSelectedBusServiceShiftId(ctx context.Context, id *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if id == nil || *id == "" {
			return nil
		}
		pg.BaseReqData.CommonBizInfo.SeletedBusServiceShiftId = *id
		return nil
	}
}

func WithInfoByRequest(ctx context.Context, request *proto.AnyCarEstimateReq) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if request == nil {
			return consts.ErrorEmptyPriceReq
		}
		pg.BaseReqData.CommonInfo.InitByAnycarReq(ctx, request)
		pg.BaseReqData.PassengerInfo.Token = request.GetToken()
		if request.MultiRequireProduct != nil {
			pg.BaseReqData.CommonBizInfo.InitByMultiRequireProduct(*request.MultiRequireProduct)
		}
		return nil
	}
}
func WithInsteadOrderSceneInfo(ctx context.Context, sceneType int32) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		pg.BaseReqData.CommonBizInfo.SceneType = sceneType
		return nil
	}
}

func WithXpsid(ctx context.Context, xpsid *string, xpsidRoot *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if xpsid != nil {
			pg.BaseReqData.CommonInfo.Xpsid = *xpsid
		}

		if xpsidRoot != nil {
			pg.BaseReqData.CommonInfo.XpsidRoot = *xpsidRoot
		}

		return nil
	}
}

func WithDecodeSeatDetailInfo(ctx context.Context, s *string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if s == nil || *s == "" {
			return nil
		}

		seatDetailInfo := make([]*models.SeatDetailInfo, 0)
		err := json.Unmarshal([]byte(*s), &seatDetailInfo)
		if err != nil {
			log.Trace.Warnf(ctx, "DecodeSeatDetailInfo", "decode seat detail info  fail, origin:%v, err:%v", *s, err)
			return nil
		}
		pg.BaseReqData.CommonBizInfo.SeatDetailInfo = seatDetailInfo

		return nil
	}
}

func WithPBDExtPID(ctx context.Context, extPID string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if extPID != "" {
			pg.BaseReqData.PassengerInfo.ExtPid = extPID
		}
		return nil
	}
}

func WithInviteInfo(ctx context.Context, orderInfo *dos.OrderInfo, estimateType int32) GeneratorOption {
	return func(pg *ProductsGenerator) error {

		if orderInfo == nil {
			return nil
		}

		if pg.BaseReqData.CommonBizInfo.InvitationInfo == nil {
			pg.BaseReqData.CommonBizInfo.InvitationInfo = &models.InvitationInfo{}
		}

		passengerCount := util.String2int32(ctx, orderInfo.PassengerCount)
		departureRange := orderInfo.ExtendFeatureParsed.DepartureRange
		for _, prod := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
			if util.String2int64(ctx, prod.EstimatePcID) == estimate_pc_id.EstimatePcIdLowPriceCarpool {
				if passengerCount == 0 {
					passengerCount = int32(prod.PassengerCount)
				}

				if len(departureRange) == 0 {
					departureRange = prod.DepartureRange
				}
			}
		}

		pg.BaseReqData.CommonBizInfo.InvitationInfo.InviterPassengerCount = passengerCount
		pg.BaseReqData.CommonBizInfo.InvitationInfo.InviterOid = orderInfo.OrderId
		pg.BaseReqData.CommonBizInfo.InvitationInfo.InviterOrderType = int16(util.String2Int(ctx, orderInfo.Type))
		pg.BaseReqData.CommonBizInfo.InvitationInfo.EstimateType = estimateType
		pg.BaseReqData.CommonBizInfo.InvitationInfo.InviterDepartureRange = departureRange
		highOrderId, err := util.EncodeOrderIdWithoutBase64(util.String2int64(ctx, orderInfo.OrderId), orderInfo.District)
		if err == nil {
			pg.BaseReqData.CommonBizInfo.InvitationInfo.InviterHighOid = util.Int642String(highOrderId)
		}

		return nil
	}
}

// 注册请求dds后需要过滤的品类
func (pg *ProductsGenerator) RegisterAfterDdsFilter(filter AfterDdsProductsFilter) {
	if pg._afterDdsFilters == nil {
		pg._afterDdsFilters = make([]AfterDdsProductsFilter, 0)
	}
	pg._afterDdsFilters = append(pg._afterDdsFilters, filter)
}

// 注册请求dds后需要裂变的品类
func (pg *ProductsGenerator) RegisterAfterDdsFission(fission AfterDdsProductsFission) {
	if pg._afterDdsFissions == nil {
		pg._afterDdsFissions = make([]AfterDdsProductsFission, 0)
	}
	pg._afterDdsFissions = append(pg._afterDdsFissions, fission)
}

// 注册请求price-api前需要过滤的品类
func (pg *ProductsGenerator) RegisterBeforePriceFilter(filter BeforePriceProductsFilter) {
	if pg._beforePriceFilters == nil {
		pg._beforePriceFilters = make([]BeforePriceProductsFilter, 0)
	}
	pg._beforePriceFilters = append(pg._beforePriceFilters, filter)
}

// 注册请求 price-api[前]  的RPC
func (pg *ProductsGenerator) RegisterBeforePriceRpcProcess(rpc RpcProcessWithBaseProducts) {
	if pg._rpcProcessBeforePrice == nil {
		pg._rpcProcessBeforePrice = make([]RpcProcessWithBaseProducts, 0)
	}
	pg._rpcProcessBeforePrice = append(pg._rpcProcessBeforePrice, rpc)
}

// 注册请求 dds[后] 不依赖品类 的RPC
func (pg *ProductsGenerator) RegisterAfterDdsNoBaseProductsRpcProcess(rpc RpcProcessWithNoBaseProducts) {
	if pg._rpcProcessBeforePrice == nil {
		pg._rpcProcessAfterDdsRpc = make([]RpcProcessWithNoBaseProducts, 0)
	}
	pg._rpcProcessAfterDdsRpc = append(pg._rpcProcessAfterDdsRpc, rpc)
}

// 注册请求 price-api[前]  第二批的RPC
func (pg *ProductsGenerator) RegisterBeforePrice2RpcProcess(rpc RpcProcessWithBaseProducts) {
	if pg._rpcProcessBeforePrice2 == nil {
		pg._rpcProcessBeforePrice2 = make([]RpcProcessWithBaseProducts, 0)
	}
	pg._rpcProcessBeforePrice2 = append(pg._rpcProcessBeforePrice2, rpc)
}

// 注册请求 和price-api[并行] 的RPC
func (pg *ProductsGenerator) RegisterWithPriceRPCProcess(rpc RpcProcessWithBaseProducts) {
	if pg._rpcProcessConcurrentPrice == nil {
		pg._rpcProcessConcurrentPrice = make([]RpcProcessWithBaseProducts, 0)
	}
	pg._rpcProcessConcurrentPrice = append(pg._rpcProcessConcurrentPrice, rpc)
}

// 注册请求 在 price-api[后] 的RPC
func (pg *ProductsGenerator) RegisterAfterPriceRPCProcess(rpc RpcProcessWithFullProducts) {
	if pg._rpcProcessAfterPrice == nil {
		pg._rpcProcessAfterPrice = make([]RpcProcessWithFullProducts, 0)
	}
	pg._rpcProcessAfterPrice = append(pg._rpcProcessAfterPrice, rpc)
}

func (pg *ProductsGenerator) RegisterBeforeAthenaRPCProcess(rpc RpcProcessWithFullProducts) {
	if pg._rpcProcessBeforeAthena == nil {
		pg._rpcProcessBeforeAthena = make([]RpcProcessWithFullProducts, 0)
	}
	pg._rpcProcessBeforeAthena = append(pg._rpcProcessBeforeAthena, rpc)
}

// 注册请求price-api后需要过滤的品类
func (pg *ProductsGenerator) RegisterAfterPriceFilter(filter AfterPriceProductsFilter) {
	if pg._afterPriceFilters == nil {
		pg._afterPriceFilters = make([]AfterPriceProductsFilter, 0)
	}
	pg._afterPriceFilters = append(pg._afterPriceFilters, filter)
}

// 注册自定义产出品类的函数
func (pg *ProductsGenerator) RegisterOptionProcessProduct(gen OptionProcessProduct) {
	if pg._optionDdsGen == nil && gen != nil {
		pg._optionDdsGen = &gen
	}
}

// 注册自定义产出价格的函数
func (pg *ProductsGenerator) RegisterOptionProcessPrice(gen OptionProcessPrice) {
	if pg._optionPriceGen == nil && gen != nil {
		pg._optionPriceGen = &gen
	}
}

// RegisterFinalFilter 注册公共流程后 最终需要过滤的品类
func (pg *ProductsGenerator) RegisterFinalFilter(filter FinalProductsFilterV2) {
	if pg._finalFilters == nil {
		pg._finalFilters = make([]FinalProductsFilterV2, 0)
	}
	pg._finalFilters = append(pg._finalFilters, filter)
}

// SetNeedMember 设置是否需要请求会员
func (pg *ProductsGenerator) SetNeedMember(b bool) {
	pg.needMember = b
}
func (pg *ProductsGenerator) SetNeedDegrade(b bool) {
	pg.needDegrade = b
}

// SetSendReqKafka 设置是否需要写Kafka
func (pg *ProductsGenerator) SetSendReqKafka(b bool) {
	pg.sendReqKafka = b
}

// SetNeedCarAggregation 设置是否需要写聚合车型
func (pg *ProductsGenerator) SetNeedCarAggregation(b bool) {
	pg.needCarAggregation = b
}

func (pg *ProductsGenerator) buildBasicProducts(ctx context.Context) ([]*models.Product, error) {
	var (
		err      error
		ddsResp  []*EstimateDecision.Product
		products []*models.Product
	)
	ddsReq := pg.BaseReqData.GenDDSProductsReq()
	ddsResp, err = decision.GetDDSProducts(ctx, ddsReq)
	if ddsResp == nil || err != nil {
		return nil, err
	}
	if len(ddsResp) == 0 {
		return nil, ErrorNoProductForEstimate
	}

	for _, ddsProduct := range ddsResp {
		product := &models.Product{}
		if ddsProduct.RemoveFlag {
			pg.removedProducts = append(pg.removedProducts, product)
		} else {
			product.BuildFromDDsResponse(ctx, ddsProduct, pg.BaseReqData)
			products = append(products, product)
		}
	}
	return products, nil
}

func (pg *ProductsGenerator) GenRemovedProducts() []*models.Product {
	return pg.removedProducts
}

func (pg *ProductsGenerator) OptionGenProducts(ctx context.Context) []*models.Product {
	e := *pg._optionDdsGen
	return e.ExecProducts(ctx, pg.BaseReqData)
}

func (pg *ProductsGenerator) OptionGenPrice(ctx context.Context, products []*models.Product) ([]*ProductInfoFull, error) {
	e := *pg._optionPriceGen
	return e.ExecPrice(ctx, pg.BaseReqData, products)
}

func (pg *ProductsGenerator) fissionProductAfterDds(ctx context.Context, baseProducts []*models.Product) []*models.Product {
	var fissionProducts []*models.Product
	for _, fission := range pg._afterDdsFissions {
		for _, p := range fission.Do(ctx, baseProducts) {
			fissionProducts = append(fissionProducts, p)
		}
	}

	log.Trace.Infof(ctx, trace.DLTagUndefined, "fission_products_after_dds=%v", fissionProducts)
	if len(fissionProducts) > 0 {
		baseProducts = append(baseProducts, fissionProducts...)
	}
	return baseProducts
}

func (pg *ProductsGenerator) filterProductAfterPrice(ctx context.Context, productsFull []*ProductInfoFull) []*ProductInfoFull {
	var toBeRemoved = make(map[models.ProductCategory]bool, 0)
	for _, filter := range pg._afterPriceFilters {
		for _, p := range filter.Do(ctx, productsFull) {
			toBeRemoved[p] = true
		}
	}

	log.Trace.Infof(ctx, "product_filter", "filter_products_after_price=%v", toBeRemoved)
	if len(toBeRemoved) > 0 {
		for i := len(productsFull) - 1; i >= 0; i-- {
			if _, ok := toBeRemoved[models.ProductCategory(productsFull[i].Product.ProductCategory)]; ok {
				productsFull = append(productsFull[:i], productsFull[i+1:]...)
			}
		}
	}
	return productsFull
}
func (pg *ProductsGenerator) finalFilterProduct(ctx context.Context, productsFull []*ProductInfoFull) []*ProductInfoFull {
	// 最终filter  串行过滤  依赖过滤顺序
	var toBeRemoved = make(map[models.ProductCategory]bool, 0)
	var productMap = make(map[int64]*ProductInfoFull, 0)

	for _, full := range productsFull {
		productMap[full.GetProductCategory()] = full
	}

	for _, filter := range pg._finalFilters {
		for _, p := range filter.HandlerFilter(ctx, productMap) {
			toBeRemoved[p] = true
			delete(productMap, int64(p))
		}
	}

	log.Trace.Infof(ctx, "product_filter", "final_filter_products=%v", toBeRemoved)
	if len(toBeRemoved) > 0 {
		for i := len(productsFull) - 1; i >= 0; i-- {
			if _, ok := toBeRemoved[models.ProductCategory(productsFull[i].Product.ProductCategory)]; ok {
				productsFull = append(productsFull[:i], productsFull[i+1:]...)
			}
		}
	}

	return productsFull
}

// getPriceInfo
// 关于caller: 可以使用ctx传递, 但是ctx的Value并不是显式结构, 不能保证覆盖和shadow, 显式通过参数传递更好
func getPriceInfo(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProducts []*models.Product) (map[string]*PriceApi.EstimateLiteItem, error) {
	var (
		err      error
		priceReq []*price_api.PriceEstimateReq
		priceRsp *PriceApi.EstimateLiteResponse
		resp     map[string]*PriceApi.EstimateLiteItem
	)

	for _, product := range baseProducts {
		req := GenProductPriceReq(ctx, caller, baseReq, product)
		if req == nil {
			continue
		}
		priceReq = append(priceReq, req)
	}

	if len(priceReq) == 0 {
		return nil, consts.ErrorEmptyPriceReq
	}

	priceRsp, err = price_api.GetMultiEstimatePriceLite(ctx, caller, priceReq)
	if err != nil {
		return resp, err
	}
	resp = make(map[string]*PriceApi.EstimateLiteItem)
	for _, item := range priceRsp.Data {
		if item != nil && item.BillInfo != nil {
			resp[item.BillInfo.EstimateId] = item
		}
	}
	return resp, nil
}

// GetPriceInfov3
func GetPriceInfov3(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProducts []*models.Product) ([]*ProductInfoFull, error) {
	var products []*ProductInfoFull
	priceResp, priceErr := getPriceInfoV3(ctx, caller, baseReq, baseProducts)
	if priceErr != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", priceErr)
		return nil, priceErr
	}
	// 6. 组装productInfoFull list
	for _, product := range baseProducts {
		if priceItem, ok := priceResp[product.EstimateID]; ok {
			pFull := &ProductInfoFull{
				BaseReqData:  baseReq,
				Product:      product,
				BillDetail:   nil,
				DiscountInfo: nil,
				PayInfo:      nil,
				hu:           priceItem,
			}
			products = append(products, pFull)
		}
	}
	return products, nil
}

// getPriceInfoV3 ...
func getPriceInfoV3(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProducts []*models.Product) (map[string]*PriceApi.EstimateNewFormData, error) {
	var (
		err      error
		priceReq []*price_api.PriceEstimateReq
		priceRsp *PriceApi.EstimateNewFormResponse
		resp     map[string]*PriceApi.EstimateNewFormData
	)

	for _, product := range baseProducts {
		req := GenProductPriceReq(ctx, caller, baseReq, product)
		if req == nil {
			continue
		}
		priceReq = append(priceReq, req)
	}

	if len(priceReq) == 0 {
		return nil, consts.ErrorEmptyPriceReq
	}

	//命中风控投毒 header增加投毒
	ctx2 := ctx
	if baseReq != nil && baseReq.CommonInfo.RiskCode == risk.SFCHackCodeForPoison {
		header := http.Header{}
		header.Set("Is-Cheating-Traffic", "1")
		ctx2 = dirpc.SetHttpHeader(ctx, header)
	}

	priceRsp, err = price_api.GetMultiEstimatePriceV3(ctx2, caller, priceReq, baseReq.PassengerInfo.ExtPid)
	if err != nil {
		return resp, err
	}
	resp = make(map[string]*PriceApi.EstimateNewFormData)
	for _, item := range priceRsp.Data {
		if item != nil && item.BillInfo != nil {
			resp[item.BillInfo.EstimateId] = item
		}
	}
	return resp, nil
}

// GetPriceInfoOversea
func GetPriceInfoOversea(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProducts []*models.Product) ([]*ProductInfoFull, error) {
	var products []*ProductInfoFull
	priceResp, priceErr := getPriceInfoOversea(ctx, caller, baseReq, baseProducts)
	if priceErr != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", priceErr)
		return nil, priceErr
	}
	// 6. 组装productInfoFull list
	for _, product := range baseProducts {
		if priceItem, ok := priceResp[product.EstimateID]; ok {
			pFull := &ProductInfoFull{
				BaseReqData:  baseReq,
				Product:      product,
				BillDetail:   nil,
				DiscountInfo: nil,
				PayInfo:      nil,
				hu:           priceItem,
			}
			products = append(products, pFull)
		}
	}
	return products, nil
}

// getPriceInfoOversea ...
func getPriceInfoOversea(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProducts []*models.Product) (map[string]*PriceApi.EstimateNewFormData, error) {
	var (
		err      error
		priceReq []*price_api.PriceEstimateReq
		priceRsp *PriceApi.EstimateNewFormResponse
		resp     map[string]*PriceApi.EstimateNewFormData
	)

	for _, product := range baseProducts {
		req := GenProductPriceReq(ctx, caller, baseReq, product)
		if req == nil {
			continue
		}
		priceReq = append(priceReq, req)
	}

	if len(priceReq) == 0 {
		return nil, consts.ErrorEmptyPriceReq
	}

	priceRsp, err = price_api.GetMultiEstimateOversea(ctx, caller, priceReq, baseReq.PassengerInfo.ExtPid)
	if err != nil {
		return resp, err
	}
	resp = make(map[string]*PriceApi.EstimateNewFormData)
	for _, item := range priceRsp.Data {
		if item != nil && item.BillInfo != nil {
			resp[item.BillInfo.EstimateId] = item
		}
	}
	return resp, nil
}

func GenProductPriceReq(ctx context.Context, caller string, baseReq *models.BaseReqData, baseProduct *models.Product) *price_api.PriceEstimateReq {
	var request *price_api.PriceEstimateReq

	if baseReq == nil || baseProduct == nil {
		return request
	}

	request = &price_api.PriceEstimateReq{}

	baseReq.CommonInfo.BuildPriceReq(request)
	baseReq.AreaInfo.BuildPriceReq(request)
	baseReq.PassengerInfo.BuildPriceReq(request, baseProduct)
	baseReq.CommonBizInfo.BuildPriceReq(request, baseProduct)
	baseReq.SendOrder.BuildPriceReq(request, baseProduct)

	baseProduct.BuildPriceReq(ctx, caller, request, baseReq)

	buildMemberInfo(baseProduct.BizInfo, request)

	return request
}

func buildMemberInfo(p *models.PrivateBizInfo, request *price_api.PriceEstimateReq) {
	if p == nil {
		return
	}
	request.PassengerInfo.IsUserUseDpa = p.IsUserUseDpa
	request.PassengerInfo.UserDpaSelected = p.UseDpaSelected
	if p.MemberProfile != nil {
		request.PassengerInfo.MemberProfile = p.MemberProfile
	}

	if p.PricePrivilegeInfo != nil {
		request.OrderExt.PricePrivilegeInfo = p.PricePrivilegeInfo
	}

	if p.MemberPrivilegeList != nil {
		request.PassengerInfo.MemberPrivilegeList = p.MemberPrivilegeList
	}

	request.PassengerInfo.MemberInfo = &price_api.MemberProfileV2Req{
		LevelID: p.UserMemberProfile.LevelID,
	}
}

func (s *ProductInfoFull) GetPricingBoxData() *taxi.PricingBoxData {
	return s.BaseReqData.CommonInfo.PricingBoxData
}

func (s *ProductInfoFull) GenSelectorInput() map[string]interface{} {
	if s.selectorInput == nil {
		if s.BillDetail != nil && s.BaseReqData != nil && s.Product != nil {
			s.selectorInput = map[string]interface{}{
				"product_id":       strconv.FormatInt(s.Product.ProductID, 10),
				"product_category": strconv.FormatInt(s.Product.ProductCategory, 10),
				"city":             strconv.FormatInt(int64(s.BaseReqData.AreaInfo.City), 10),
				"carpool_type":     strconv.FormatInt(s.Product.CarpoolType, 10),
				"is_trip_cloud":    strconv.FormatBool(tripcloud.IsTripcloudProductID(bizCommonConsts.ProductID(s.Product.ProductID))),
				"count_price_type": strconv.FormatInt(int64(s.BillDetail.CountPriceType), 10),
				"cap_price":        s.BillDetail.CapPrice,
			}
		}
	}
	return s.selectorInput
}

func (s *ProductInfoFull) IsCarpoolV3Merge(ctx context.Context) bool {
	if !carpool.IsCarpoolDualPriceV3(int32(s.Product.CarpoolType), s.Product.CarpoolPriceType, s.Product.IsDualCarpoolPrice) {
		return false
	}

	// 用卡
	if s.IsHaveCarpoolVCard(ctx) {
		return true
	}

	failedFee, ok := s.GetCarpoolFailEstimateFee()
	if !ok {
		return false
	}

	// 向上留一位小数后 判断是否相等
	return math.Ceil(s.GetEstimateFee()*10) == math.Ceil(failedFee*10)
}

// SetHu 设置hu
func (pg *ProductInfoFull) SetHu(hu *PriceApi.EstimateNewFormData) {
	pg.hu = hu
}

func (pg *ProductInfoFull) GetRebookServiceFee() float64 {
	if pg.hu == nil || pg.hu.BillInfo == nil {
		return 0
	}

	return pg.hu.BillInfo.RebookServiceFee
}

func (pg *ProductsGenerator) SetisBaseReqInit(status bool) {
	pg.IsBaseReqInit = status
}

func (pg *ProductsGenerator) GetBillInfoMap() map[int64]*PriceApi.EstimateNewFormBillInfo {
	ret := make(map[int64]*PriceApi.EstimateNewFormBillInfo, len(pg.productsInfoFull))
	for _, product := range pg.productsInfoFull {
		if billInfo := product.GetBillInfo(); billInfo != nil {
			ret[product.GetProductCategory()] = billInfo
		}
	}

	return ret
}

func (pg *ProductsGenerator) GetReqFrom() string {
	return pg.reqFrom
}

// GetAggregationCars /**
func GetAggregationCars(ctx context.Context, subGroupID int32, products []*ProductInfoFull) []int64 {
	var (
		groupsConfig []GroupConfig
		carPcids     []int64
	)
	carPcids = make([]int64, 0)
	configByteArray, err := apollo.GetConfigsByNamespace(ctx, "car_aggregation")
	if err != nil || len(configByteArray) == 0 {
		return carPcids
	}
	err = json.Unmarshal(configByteArray, &groupsConfig)
	if err != nil {
		return carPcids
	}
	finalSubGroupId2Config := make(map[int32]GroupConfig)
	for _, config := range groupsConfig {
		finalSubGroupId2Config[config.SubGroupID] = config
	}

	//填充绑定level_type的盒子
	finalSubGroupId2Config = fillingProductListByLevelType(finalSubGroupId2Config, products)

	if config, ok := finalSubGroupId2Config[subGroupID]; ok {
		for _, pcId := range config.ProductList {
			id, err := strconv.ParseInt(pcId, 10, 64)
			if err != nil {
				continue
			}
			carPcids = append(carPcids, id)
		}
		return carPcids
	}

	return carPcids
}
func IsHitBusCardTyingSale(ctx context.Context, baseReq *models.BaseReqData, productFull *ProductInfoFull) bool {
	if apollo.FeatureToggle(ctx, "new_recommend_bus_vcard_use_switch", strconv.Itoa(int(baseReq.PassengerInfo.UID)), map[string]string{
		"access_key_id": cast.ToString(baseReq.CommonInfo.AccessKeyID),
		"app_version":   baseReq.CommonInfo.AppVersion,
		"phone":         baseReq.PassengerInfo.Phone,
		"city":          cast.ToString(baseReq.AreaInfo.Area),
		"product_id":    cast.ToString(productFull.Product.ProductID),
	}) {
		return true
	}
	return false
}

func WithPetInfoByRequest(ctx context.Context, request models.OrderMatchRequest, orderInfo *dos.OrderInfo) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		if request == nil || orderInfo == nil {
			return consts.ErrorEmptyPriceReq
		}
		pg.BaseReqData.CommonBizInfo.InitPetInfoByOrderInfo(ctx, orderInfo, request)
		return nil
	}
}

// WithInitSmartBusData smartBusData初始化
func WithInitSmartBusData(ctx context.Context) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		pg.BaseReqData.CommonBizInfo.SmartBusData = &models.SmartBusData{}
		return nil
	}
}

func WithSmartBusStationInfo(ctx context.Context, startStationInfo string, destStationInfo string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		var err error
		if startInfo := strings.TrimSpace(startStationInfo); consts.EmptyObjectJson != startInfo && 0 != len(startInfo) {
			if pg.BaseReqData.CommonBizInfo.SmartBusData.StartStationInfo, err = models.BuildSmartBusStation(ctx, startStationInfo); nil != err {
				return err
			}
		}

		if destInfo := strings.TrimSpace(destStationInfo); consts.EmptyObjectJson != destInfo && 0 != len(destInfo) {
			if pg.BaseReqData.CommonBizInfo.SmartBusData.DestStationInfo, err = models.BuildSmartBusStation(ctx, destStationInfo); nil != err {
				return err
			}
		}

		return nil
	}
}

func WithSmartBusExtData(ctx context.Context, extMap string) GeneratorOption {
	return func(pg *ProductsGenerator) error {
		var err error
		if 0 != len(strings.TrimSpace(extMap)) {
			if pg.BaseReqData.CommonBizInfo.SmartBusData.SmartBusExtData, err = models.BuildSmartBusExtData(ctx, extMap); nil != err {
				return err
			}
		}
		return nil
	}
}

func WithCommericalType() GeneratorOption {
	return func(pg *ProductsGenerator) error {
		pg.BaseReqData.CommonBizInfo.CommericalType = 1
		return nil
	}
}
