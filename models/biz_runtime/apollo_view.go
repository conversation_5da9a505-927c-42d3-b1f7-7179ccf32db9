package biz_runtime

import (
	"strconv"

	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
)

// GetApolloParams 绑定在ProductInfoFull上的获取ApolloParams
// 如果当前logic的Provider，无需额外实现其他参数的获取方法，eg：当前除了Apollo参数以外，没有其他逻辑需要使用CarLevel，则可以通过注册WithCarLevel补充Apollo参数
// 如果当前logic的Provider,已经实现了GetCarLevel()，有除了Apollo参数之外的逻辑需要，则也可以直接在params返回后进行添加，eg：params['car_level'] = prov.GetCarLevel()
// 本文件所提供的paramsFunc，是用于解决当前数据源依赖缺失的情况下补充额外Apollo参数用的，已经添加了比较常见的参数，大家可以进行挑选使用
func (p *ProductInfoFull) GetApolloParams(keyFunc func(*ProductInfoFull) string, paramsFunc ...func(full *ProductInfoFull) (key, value string)) (key string, params map[string]string) {
	if keyFunc != nil {
		key = keyFunc(p)
	} else {
		key = p.GetUserPhone()
	}

	cityID := strconv.Itoa(int(p.BaseReqData.AreaInfo.Area))
	countyId := strconv.Itoa(int(p.BaseReqData.AreaInfo.FromCounty))
	params = map[string]string{
		"city":             cityID,
		"phone":            p.BaseReqData.PassengerInfo.Phone,
		"uid":              strconv.Itoa(int(p.BaseReqData.PassengerInfo.UID)),
		"pid":              strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)),
		"city_id":          cityID,
		"lang":             p.GetLang(),
		"access_key_id":    strconv.FormatInt(int64(p.GetAccessKeyId()), 10),
		"app_version":      p.BaseReqData.CommonInfo.AppVersion,
		"product_category": strconv.FormatInt(p.GetProductCategory(), 10),
		"county_id":        countyId,
	}

	for _, f := range paramsFunc {
		if f == nil {
			continue
		}

		k, v := f(p)
		if k != "" && v != "" {
			params[k] = v
		}
	}

	return
}

// ApolloParamsGen 用于实现中间apollo_model，从而解除对具体数据类型的依赖
func (p *ProductInfoFull) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string) {

	if p._ap == nil {
		p._ap = &apollo_model.ParamsConnector{
			City:        strconv.Itoa(int(p.BaseReqData.AreaInfo.Area)),
			Phone:       p.BaseReqData.PassengerInfo.Phone,
			UID:         strconv.Itoa(int(p.BaseReqData.PassengerInfo.UID)),
			PID:         strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)),
			Lang:        p.GetLang(),
			AccessKeyID: strconv.FormatInt(int64(p.GetAccessKeyId()), 10),
			AppVersion:  p.BaseReqData.CommonInfo.AppVersion,
		}

		var productCategory, county, isCrossCity, menuID string
		if p.BaseReqData != nil {
			county = cast.ToString(p.BaseReqData.AreaInfo.FromCounty)
			isCrossCity = util.Bool2string(p.BaseReqData.AreaInfo.City != p.BaseReqData.AreaInfo.ToArea)
			menuID = p.BaseReqData.CommonInfo.MenuID
		}

		productCategory = cast.ToString(p.GetProductCategory())

		p._ap.SetProductCategory(productCategory).SetCounty(county).SetMenuID(menuID).SetIsCrossCity(isCrossCity)

	}

	return p._ap.ApolloParamsGen(keyFunc, paramsFunc...)
}

//func (p *ProductInfoFull) GetApolloParamsV2(key apollo.UniqID) (string, map[string]string) {
//	params := map[string]string{
//		"city":             strconv.Itoa(int(p.BaseReqData.AreaInfo.Area)),
//		"phone":            p.BaseReqData.PassengerInfo.Phone,
//		"uid":              strconv.Itoa(int(p.BaseReqData.PassengerInfo.UID)),
//		"pid":              strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)),
//		"city_id":          strconv.Itoa(int(p.BaseReqData.AreaInfo.Area)),
//		"lang":             p.GetLang(),
//		"access_key_id":    strconv.FormatInt(int64(p.GetAccessKeyId()), 10),
//		"app_version":      p.BaseReqData.CommonInfo.AppVersion,
//		"product_category": strconv.FormatInt(p.GetProductCategory(), 10),
//		"is_cross_city":    cast.ToString(cast.ToInt64(p.BaseReqData.AreaInfo.Area != p.BaseReqData.AreaInfo.ToArea)),
//		"county":           cast.ToString(p.GetFromCounty()),
//		"menu_id":          "dache_anycar",
//	}
//
//	if key == apollo.KeyUid {
//		return strconv.Itoa(int(p.BaseReqData.PassengerInfo.UID)), params
//	} else if key == apollo.KeyPid {
//		return strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)), params
//	} else {
//		return strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID)), params
//	}
//}

func WithPhoneKey(p *ProductInfoFull) string {
	if p.BaseReqData == nil {
		return ""
	}
	return p.BaseReqData.PassengerInfo.Phone
}

func WithUIDKey(p *ProductInfoFull) string {
	if p.BaseReqData == nil {
		return ""
	}

	return strconv.Itoa(int(p.BaseReqData.PassengerInfo.UID))
}

func WithPIDKey(p *ProductInfoFull) string {
	if p.BaseReqData == nil {
		return ""
	}
	return strconv.Itoa(int(p.BaseReqData.PassengerInfo.PID))
}

func WithFromCounty(p *ProductInfoFull) (string, string) {
	if p.BaseReqData == nil {
		return "", ""
	}
	return "county", strconv.Itoa(int(p.BaseReqData.AreaInfo.FromCounty))
}

func WithCarLevel(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}

	return "car_level", p.Product.RequireLevel
}

func WithIsSpecialPrice(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}

	return "is_special_price", util.Bool2string(p.Product.IsSpecialPrice)
}

func WithIsCrossCity(p *ProductInfoFull) (string, string) {
	if p.BaseReqData == nil {
		return "", ""
	}
	return "is_cross_city", util.Bool2string(p.BaseReqData.AreaInfo.City != p.BaseReqData.AreaInfo.ToArea)
}

func WithComboID(p *ProductInfoFull) (string, string) {
	if p.Product == nil || p.Product.BizInfo == nil {
		return "", ""
	}
	return "combo_id", cast.ToString(p.Product.BizInfo.ComboID)
}

func WithRouteGroup(p *ProductInfoFull) (string, string) {
	if p.Product == nil || p.Product.BizInfo == nil || p.Product.BizInfo.RouteInfo == nil {
		return "", ""
	}

	return "route_group", cast.ToString(p.Product.BizInfo.RouteInfo.RouteGroup)
}

func WithMenuID(p *ProductInfoFull) (string, string) {
	if p.BaseReqData == nil {
		return "", ""
	}
	return "menu_id", p.BaseReqData.CommonInfo.MenuID
}

func WithProductID(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "product_id", cast.ToString(p.Product.ProductID)
}

func WithRequireLevel(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "require_level", p.GetRequireLevel()
}

func WithProductCategory(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "product_category", cast.ToString(p.GetProductCategory())
}

func WithComboType(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "combo_type ", cast.ToString(p.GetComboType())
}

func WithPageType(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "page_type", cast.ToString(p.GetPageType())
}

func WithToCity(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "to_city", cast.ToString(p.GetToCityID())
}

func WithFontScaleType(p *ProductInfoFull) (string, string) {
	if p.Product == nil {
		return "", ""
	}
	return "font_scale_type", cast.ToString(p.GetFontScaleType())
}

func WithTabID(p *ProductInfoFull) (string, string) {
	if p.BaseReqData == nil {
		return "", ""
	}
	return "tab_id", p.BaseReqData.CommonInfo.TabId
}
