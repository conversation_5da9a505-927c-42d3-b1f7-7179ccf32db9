package biz_runtime

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_allowProductIntoBox(t *testing.T) {
	convey.Convey("Test allowProductIntoBox function", t, func() {

		ctx := context.Background()

		convey.Convey("上海市-区县出租车-命中", func() {
			patches := gomonkey.ApplyFunc(apollo.FeatureToggle, func(_ context.Context, _, _ string, _ map[string]string) bool {
				return true
			})

			defer patches.Reset()

			params := map[string]string{
				"key":           "12345",
				"pid":           "12345",
				"phone":         "12345",
				"app_version":   "6.8.10",
				"access_key_id": "1",
				"lang":          "zh-CN",
				"city":          "4", // 上海
				"page_type":     "0",
			}
			pcID := int64(196)  // 区县出租车
			groupID := int64(2) // 出租车盒子
			result := allowProductIntoBox(ctx, params, pcID, groupID)
			convey.So(result, convey.ShouldBeTrue)
		})

		convey.Convey("南京市-区县出租车-过滤", func() {
			patches := gomonkey.ApplyFunc(apollo.FeatureToggle, func(_ context.Context, _, _ string, _ map[string]string) bool {
				return false
			})

			defer patches.Reset()

			params := map[string]string{
				"key":           "12345",
				"pid":           "12345",
				"phone":         "12345",
				"app_version":   "6.8.10",
				"access_key_id": "1",
				"lang":          "zh-CN",
				"city":          "11", // 南京
				"page_type":     "0",
			}
			pcID := int64(196)  // 区县出租车
			groupID := int64(2) // 出租车盒子
			result := allowProductIntoBox(ctx, params, pcID, groupID)
			convey.So(result, convey.ShouldBeFalse)
		})

		convey.Convey("出租车盒子-其它品类-命中", func() {
			patches := gomonkey.ApplyFunc(apollo.FeatureToggle, func(_ context.Context, _, _ string, _ map[string]string) bool {
				return true
			})

			defer patches.Reset()

			params := map[string]string{
				"key":           "12345",
				"pid":           "12345",
				"phone":         "12345",
				"app_version":   "6.8.10",
				"access_key_id": "1",
				"lang":          "zh-CN",
				"city":          "11", // 南京
				"page_type":     "0",
			}
			pcID := int64(1)    // 其它品类
			groupID := int64(2) // 出租车盒子
			result := allowProductIntoBox(ctx, params, pcID, groupID)
			convey.So(result, convey.ShouldBeTrue)
		})

		convey.Convey("其它盒子-命中", func() {
			patches := gomonkey.ApplyFunc(apollo.FeatureToggle, func(_ context.Context, _, _ string, _ map[string]string) bool {
				return true
			})

			defer patches.Reset()

			params := map[string]string{
				"key":           "12345",
				"pid":           "12345",
				"phone":         "12345",
				"app_version":   "6.8.10",
				"access_key_id": "1",
				"lang":          "zh-CN",
				"city":          "4", // 上海
				"page_type":     "0",
			}
			pcID := int64(196)  // 区县出租车
			groupID := int64(5) // 其它盒子
			result := allowProductIntoBox(ctx, params, pcID, groupID)
			convey.So(result, convey.ShouldBeTrue)
		})
	})
}

//func TestHandlerCarAggregation(t *testing.T) {
//	convey.Convey("Test HandlerCarAggregation function", t, func() {
//		ctx := context.Background()
//
//		// Initialize ProductsGenerator with necessary fields
//		mockProductsGenerator := &ProductsGenerator{
//			BaseReqData: &models.BaseReqData{
//				PassengerInfo: models.PassengerInfo{
//					PID:   12345,
//					Phone: "1234567890",
//				},
//				CommonInfo: models.CommonInfo{
//					AppVersion:  "6.8.10",
//					AccessKeyID: 1,
//					Lang:        "zh-CN",
//					PageType:    1,
//				},
//				AreaInfo: models.AreaInfo{
//					Area: 4,
//				},
//			},
//		}
//
//		mockProductsGenerator.FinalSubGroupId2Config = map[int32]GroupConfig{
//			2: {
//				SubGroupID:  2,
//				ProductList: []string{"44", "7", "196"},
//			},
//		}
//
//		// Add mock products
//		productsFull := []*ProductInfoFull{
//			{
//				Product: &models.Product{
//					ProductCategory: 196,
//				},
//			},
//			{
//				Product: &models.Product{
//					ProductCategory: 44,
//				},
//			},
//		}
//
//		convey.Convey("盒子内品类被过滤", func() {
//			// Mock the FeatureToggle function
//			patches := gomonkey.ApplyFunc(allowProductIntoBox, func(_ context.Context, _ map[string]string, _, _ int64) bool {
//				return false
//			})
//
//			patches.ApplyFunc(HandleXDiscountBox, func(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
//				return map[int32]GroupConfig{
//					2: {
//						SubGroupID:  2,
//						ProductList: []string{"44", "7", "196"},
//					},
//				}
//			})
//
//			patches.ApplyFunc(GetTCExperimentParams, func(_ context.Context, _ models.BaseReqData) map[string]string {
//				return map[string]string{}
//			})
//
//			defer patches.Reset()
//			// Call the function
//			mockProductsGenerator.HandlerCarAggregation(ctx, productsFull)
//
//			// Assertions
//			convey.So(len(mockProductsGenerator.pcIDToSubGroupIDMap), convey.ShouldEqual, 0)
//		})
//
//		convey.Convey("盒子内品类不过滤", func() {
//			patches := gomonkey.ApplyFunc(allowProductIntoBox, func(_ context.Context, _ map[string]string, _, _ int64) bool {
//				return true
//			})
//			patches.ApplyFunc(HandleXDiscountBox, func(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
//				return map[int32]GroupConfig{
//					2: {
//						SubGroupID:  2,
//						ProductList: []string{"44", "7", "196"},
//					},
//				}
//			})
//
//			patches.ApplyFunc(GetTCExperimentParams, func(_ context.Context, _ models.BaseReqData) map[string]string {
//				return map[string]string{}
//			})
//			patches.Reset()
//
//			// Call the function
//			mockProductsGenerator.HandlerCarAggregation(ctx, productsFull)
//			//
//			// Assertions
//			convey.So(mockProductsGenerator.pcIDToSubGroupIDMap[196], convey.ShouldEqual, 2)
//			convey.So(mockProductsGenerator.pcIDToSubGroupIDMap[44], convey.ShouldEqual, 2)
//		})
//	})
//}

//func TestLoadCarAggregationConf(t *testing.T) {
//	convey.Convey("Test loadCarAggregationConf", t, func() {
//		ctx := context.Background()
//		pg := &ProductsGenerator{
//			BaseReqData: &models.BaseReqData{
//				PassengerInfo: models.PassengerInfo{
//					PID:   12345,
//					Phone: "1234567890",
//				},
//				CommonInfo: models.CommonInfo{
//					AppVersion:  "6.8.10",
//					AccessKeyID: 1,
//					Lang:        "zh-CN",
//					PageType:    0,
//				},
//				AreaInfo: models.AreaInfo{
//					Area: 4,
//				},
//			},
//			productsInfoFull: []*ProductInfoFull{
//				{
//					Product: &models.Product{
//						ProductCategory: 196,
//					},
//				},
//				{
//					Product: &models.Product{
//						ProductCategory: 44,
//					},
//				},
//			},
//		}
//
//		convey.Convey("盒子内品类不过滤", func() {
//			// 模拟不过滤盒子内品类
//			patches := gomonkey.ApplyFunc(allowProductIntoBox, func(_ context.Context, _ map[string]string, _, _ int64) bool {
//				return true
//			})
//
//			patches.ApplyFunc(apollo.GetConfigsByNamespace, func(ctx context.Context, namespace string) ([]byte, error) {
//				configs := []GroupConfig{
//					{
//						SubGroupID:  2,
//						ProductList: []string{"44", "7", "196"},
//					},
//				}
//
//				ret, err := json.Marshal(configs)
//				return ret, err
//			})
//
//			patches.ApplyFunc(fillingProductListByLevelType, func(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
//				return finalSubGroupId2Config
//			})
//
//			patches.ApplyFunc(OutsideCarManufacturer, func(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
//				return finalSubGroupId2Config
//			})
//
//			patches.Reset()
//
//			finalSubGroupId2Config, PcIDToSubGroupIDMap := loadCarAggregationConf(ctx, pg)
//
//			convey.So(finalSubGroupId2Config, convey.ShouldNotBeNil)
//			if finalSubGroupId2Config != nil {
//				convey.So(len(finalSubGroupId2Config), convey.ShouldBeGreaterThan, 0)
//			}
//
//			if PcIDToSubGroupIDMap != nil {
//				convey.So(PcIDToSubGroupIDMap[44], convey.ShouldEqual, 2)
//				convey.So(PcIDToSubGroupIDMap[196], convey.ShouldEqual, 2)
//			}
//		})
//
//		convey.Convey("盒子内品类被过滤", func() {
//			// 模拟不过滤盒子内品类
//			patches := gomonkey.ApplyFunc(allowProductIntoBox, func(_ context.Context, _ map[string]string, _, _ int64) bool {
//				return false
//			})
//
//			patches.ApplyFunc(OutsideCarManufacturer, func(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
//				return map[int32]GroupConfig{
//					2: {
//						SubGroupID:  2,
//						ProductList: []string{"44", "7", "196"},
//					},
//				}
//			})
//
//			patches.Reset()
//
//			_, PcIDToSubGroupIDMap := loadCarAggregationConf(ctx, pg)
//
//			if PcIDToSubGroupIDMap != nil {
//				_, exists := PcIDToSubGroupIDMap[44]
//				convey.So(exists, convey.ShouldBeFalse)
//				_, exists = PcIDToSubGroupIDMap[196]
//				convey.So(exists, convey.ShouldBeFalse)
//			}
//		})
//	})
//}
