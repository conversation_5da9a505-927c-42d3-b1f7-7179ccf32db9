package biz_runtime

import (
	"context"
	"fmt"
	"sync"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ProductsGenerator struct {
	BaseReqData      *models.BaseReqData
	productsInfoFull []*ProductInfoFull
	isSort           int // 1 表示综合排序  2 表示按距离最近排序，3 表示综合排序 &&  按距离最近排序

	reqFrom string

	removedProducts []*models.Product // 被dds remove 了的产品，如果要在前端展示"不可用状态"，可以通过 GetRemovedProducts 获取列表，并写对应的render，进行处理

	IsBaseReqInit bool // base Service 是否已经构建完毕
	IsReplaceDDS  bool
	ReplaceDDS    func(context.Context) ([]*models.Product, error)

	needMember      bool // 是否需要会员信息
	sendReqKafka    bool // 发送请求price-api前的trigger
	needDegrade     bool // 是否处理降级
	AggregationConf      // 是否车型聚合

	_optionDdsGen       *OptionProcessProduct       // 自定义生成品类逻辑
	_optionPriceGen     *OptionProcessPrice         // 自定义产出价格逻辑
	_afterDdsFilters    []AfterDdsProductsFilter    // dds后做品类过滤的特殊业务逻辑
	_afterDdsFissions   []AfterDdsProductsFission   // dds后做品类裂变的特殊业务逻辑
	_beforePriceFilters []BeforePriceProductsFilter // price-api 后做品类过滤的特殊业务逻辑
	_afterPriceFilters  []AfterPriceProductsFilter  // price-api 后做品类过滤的特殊业务逻辑
	_finalFilters       []FinalProductsFilterV2     // 共用流程中最后一个准入

	_rpcProcessAfterDdsRpc     []RpcProcessWithNoBaseProducts // dds拿到后 无品类时执行的rpc
	_rpcProcessBeforePrice     []RpcProcessWithBaseProducts   // dds拿到后 需要在price-api前执行的rpc
	_rpcProcessBeforePrice2    []RpcProcessWithBaseProducts   // dds拿到后，需要在price-api前执行的第二批rpc
	_rpcProcessConcurrentPrice []RpcProcessWithBaseProducts   // dds拿到后 可以和price-api 并发的rpc

	_rpcProcessBeforeAthena []RpcProcessWithFullProducts // price拿到后，athena前
	_rpcProcessAfterPrice   []RpcProcessWithFullProducts // price拿到后，才可以并发的rpc
}

type AggregationConf struct {
	needCarAggregation bool // 是否需要车型聚合

	FinalSubGroupId2Config map[int32]GroupConfig // sub_group_id 对应 的配置
	pcIDToSubGroupIDMap    map[int64]int32
}

// price-api 保留两位小数开关：http://ab.intra.xiaojukeji.com/launch/1047/normal/104037
var priceV3 = map[string]bool{
	"pCarpoolEstimatePrice":            true,
	"pCompositeTravel":                 true,
	"pHomePageEstimate":                true,
	"pAnycarEstimate":                  true,
	"pAnycarEstimateV3":                true,
	"pAnycarEstimateV4":                true,
	"pRecCarpoolEstimatePrice":         true,
	"pServiceEstimate":                 true,
	"pSimpleEstimate":                  true,
	"pIntercityEstimatePrice":          true,
	"pPbdEstimate":                     true,
	"pPbdAnyCarEstimate":               true,
	"pPickOnTimeEstimate":              true,
	"pBargainEstimate":                 true,
	"pSFCEstimate":                     true,
	"pMiniBusEstimate":                 true,
	"pIntercityMutliEstimatePrice":     true,
	"pIntercityEstimateDetail":         true,
	"pOrderBookingPreCancelEstimate":   true,
	"pEstimateV4":                      true,
	"pInvitedEstimate":                 true,
	"pAssistantEstimate":               true,
	"pGetEstimateDataWithoutRender":    true,
	"pCombinedTravelEstimate":          true,
	"pStationBusInsteadOrderEstimate":  true,
	"pDidiMiniEstimate":                true,
	"EstimateOrderWithoutRender":       true,
	"pPbdStationBusMultiPriceEstimate": true,
	"pPbdStationBusOrderEstimate":      true,
	"pPbdStationBusDetailEstimate":     true,
	"pPbdStationBusMultiEstimate":      true,
	"pCharterMultiEstimate":            true,
	"pShuttleBusEstimate":              true,
	"PPetsTravelEstimateEstimate":      true,
	"pCarpoolInvitationEstimate":       true,
	"pSmartBusEstimate":                true,
	"pCarpoolLowPriceEstimate":         true,
	"petAnycarEstimate":                true,
	"meetingCarEstimate":               true,
	"pMCPEstimate":                     true,
}

func (pg *ProductsGenerator) GenProducts(ctx context.Context) ([]*ProductInfoFull, error) {
	var (
		err         error
		baseProduct []*models.Product
		priceItems  map[string]*PriceApi.EstimateLiteItem
		products    []*ProductInfoFull
	)
	if !pg.IsBaseReqInit {
		return nil, consts.ErrorBaseReqNotBuild
	}

	if pg._optionDdsGen != nil {
		// 自定义生成品类插件处理
		baseProduct = pg.OptionGenProducts(ctx)
		//命中特殊情况下直接报错
		if len(baseProduct) == 0 && pg.hitReturnErr() {
			return nil, fmt.Errorf("%w: %s", consts.ErrorGetFromDDSFail, err)
		}
	} else {
		// 1. 调用dds获取基本的开城
		baseProduct, err = pg.buildBasicProducts(ctx)
		if err != nil {
			if err.Error() == decision.ErrStrStopoverPointsConflictsWithScenes {
				return nil, err
			}
			return nil, fmt.Errorf("%w: %s", consts.ErrorGetFromDDSFail, err)
		}
	}

	pg.multiExecAfterDdsRpc(ctx, baseProduct)

	// 2. 拿到dds后根据当前业务特性，做品类过滤
	baseProduct, err = pg.filterProductAfterDds(ctx, baseProduct)
	if err != nil {
		return nil, err
	}

	// 品类裂变
	baseProduct = pg.fissionProductAfterDds(ctx, baseProduct)

	// 配套，写入req kafka
	if pg.sendReqKafka {
		go SendEstimateReqKafka(ctx, pg.BaseReqData, baseProduct, pg.reqFrom)
	}

	// 3. 构建会员/个性化服务等信息 (部分结果需要过滤)，price-api依赖
	err = pg.buildProductExtraInfo(ctx, pg.BaseReqData, baseProduct)
	// 当前err为强阻断流程
	if err != nil {
		return nil, err
	}
	baseProduct = pg.filterProductBeforePrice(ctx, baseProduct)
	if len(baseProduct) == 0 {
		return products, consts.ErrorGetFromDDSFail
	}

	// 4. go func 批量执行其他rpc，price-api不依赖
	var wg sync.WaitGroup
	wg.Add(1)
	go pg.multiExecConcurrentRpc(ctx, baseProduct, wg.Done)
	// 尽量不要注册_optionPriceGen
	if pg._optionPriceGen != nil {
		products, err = pg.OptionGenPrice(ctx, baseProduct)
		if err != nil {
			return nil, err
		}
	} else if _, ok := priceV3[pg.reqFrom]; ok { // 5. 调用price-api
		priceResp, priceErr := getPriceInfoV3(ctx, pg.reqFrom, pg.BaseReqData, baseProduct)
		if priceErr != nil {
			// log.Trace.Warnf(ctx, trace.DLTagUndefined, "priceErr: %v", priceErr)
			return nil, priceErr
		}

		// 6. 组装productInfoFull list
		for _, product := range baseProduct {
			if priceItem, ok := priceResp[product.EstimateID]; ok {
				pFull := &ProductInfoFull{
					BaseReqData:  pg.BaseReqData,
					Product:      product,
					BillDetail:   nil,
					DiscountInfo: nil,
					PayInfo:      nil,
					hu:           priceItem,
				}
				products = append(products, pFull)
			}
		}
	} else {
		priceItems, err = getPriceInfo(ctx, pg.reqFrom, pg.BaseReqData, baseProduct)
		if err != nil {
			return nil, err
		}
		// 6. 组装productInfoFull list
		for _, product := range baseProduct {
			if priceItem, ok := priceItems[product.EstimateID]; ok {
				pFull := &ProductInfoFull{
					BaseReqData:  pg.BaseReqData,
					Product:      product,
					BillDetail:   priceItem.BillInfo,
					DiscountInfo: priceItem.ActivityInfo,
					PayInfo:      priceItem.PaymentInfo,
					ExtraInfo:    priceItem.ExtraInfo,
				}

				products = append(products, pFull)
			}
		}
	}

	// 等待第四步的协程结束
	wg.Wait()

	pg.multiExecBeforeAthena(ctx, products)

	// 6. 拿到价格后，再做的品类过滤
	products = pg.filterProductAfterPrice(ctx, products)
	if pg.needDegrade {
		var countWyc int
		for _, product := range products {
			if !product.Product.IsTripcloudProduct(ctx) {
				countWyc++
			}
		}
		if countWyc < 1 {
			return nil, BizError.ErrEstimateDegradePriceEmpty
		}
	}

	if pg.needCarAggregation { // 赋值车型聚合信息
		pg.HandlerCarAggregation(ctx, products)
	}

	// 7. 统一处理请求price后的需要并行的rpc
	pg.multiExecAfterPriceRpc(ctx, products)

	// 8. 最终准入
	products = pg.finalFilterProduct(ctx, products)
	// log.Trace.Debugf(ctx, "product_filter", "finalFilterProduct: %s", util.JustJsonEncode(products))

	return products, nil
}

func (pg *ProductsGenerator) hitReturnErr() bool {
	if pg.BaseReqData != nil && pg.BaseReqData.CommonInfo.Channel == 40082 {
		return true
	}
	if pg.BaseReqData != nil && pg.BaseReqData.CommonInfo.SourceID == source_id.SourceIDPbdStationBus {
		return true
	}
	return false
}

func (pg *ProductsGenerator) buildProductExtraInfo(ctx context.Context, baseReq *models.BaseReqData, products []*models.Product) error {
	var err error = nil
	if baseReq == nil || len(products) == 0 {
		return err
	}

	if pg.needMember {
		if rpc := NewMemberPrivilegeRPC(pg.BaseReqData, baseReq.CommonBizInfo.MultiRequireProduct); rpc != nil {
			pg._rpcProcessBeforePrice = append(pg._rpcProcessBeforePrice, rpc)
		}
	}
	wg := sync.WaitGroup{}
	wg.Add(2) // 临时写成这样..

	util.Go(ctx, func() {
		defer wg.Done()

		if pg.needCarAggregation { // 需要聚合  提前并行加载配置
			pg.FinalSubGroupId2Config, pg.pcIDToSubGroupIDMap = loadCarAggregationConf(ctx, pg)
		}

	})

	util.Go(ctx, func() {
		defer wg.Done()

		// 批量执行RPC
		var tmpErr error = nil
		tmpErr = pg.multiExecBeforePriceSyncRpc(ctx, products)
		if err == nil {
			err = tmpErr
		}

		// 批量执行RPC-2
		tmpErr = pg.multiExecBeforePriceSyncRpc2(ctx, products)
		if err == nil {
			err = tmpErr
		}
	})

	wg.Wait()
	return err
}

func (pg *ProductsGenerator) filterProductAfterDds(ctx context.Context, baseProducts []*models.Product) ([]*models.Product, error) {
	var (
		toBeRemoved = make(map[models.ProductCategory]bool, 0)
		reasonMap   = make(map[models.ProductCategory]bool)
		originSum   = len(baseProducts)
	)

	for _, filter := range pg._afterDdsFilters {
		productCategories, removeReason := filter.AfterDdsFilter(ctx, baseProducts)
		for _, p := range productCategories {
			toBeRemoved[p] = true

			if len(removeReason) > 0 {
				if removeReason == consts.FilterSendProduct || removeReason == consts.FilterThirdParty {
					reasonMap[p] = true
				}
			}
		}
	}

	log.Trace.Infof(ctx, "product_filter", "after_dds=%v", toBeRemoved)
	if len(toBeRemoved) > 0 {
		for i := len(baseProducts) - 1; i >= 0; i-- {
			if _, ok := toBeRemoved[models.ProductCategory(baseProducts[i].ProductCategory)]; ok {
				baseProducts = append(baseProducts[:i], baseProducts[i+1:]...)
			}
		}
	}

	productCount := len(baseProducts)
	if productCount == 0 && len(reasonMap) == originSum {
		// 被已发单车型返回特殊报错
		return baseProducts, consts.ErrorNoProductCanEstimate
	} else if productCount == 0 {
		return baseProducts, consts.ErrorGetFromDDSFail
	}

	return baseProducts, nil
}

func (pg *ProductsGenerator) filterProductBeforePrice(ctx context.Context, baseProducts []*models.Product) []*models.Product {
	var toBeRemoved = make(map[models.ProductCategory]bool, 0)
	for _, filter := range pg._beforePriceFilters {
		for _, p := range filter.Exec(ctx, baseProducts) {
			toBeRemoved[p] = true
		}
	}

	log.Trace.Infof(ctx, "product_filter", "before_price=%v", toBeRemoved)
	if len(toBeRemoved) > 0 {
		for i := len(baseProducts) - 1; i >= 0; i-- {
			if _, ok := toBeRemoved[models.ProductCategory(baseProducts[i].ProductCategory)]; ok {
				baseProducts = append(baseProducts[:i], baseProducts[i+1:]...)
			}
		}
	}
	return baseProducts
}

func (pg *ProductsGenerator) multiExecBeforeAthena(ctx context.Context, products []*ProductInfoFull) {

	if len(pg._rpcProcessBeforeAthena) <= 0 || pg.BaseReqData == nil || len(products) == 0 {
		return
	}

	finishedRpc := multiExecWithFull(ctx, products, pg._rpcProcessBeforeAthena, AfterPriceTimeout)
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
		for _, productFull := range products {
			if productFull == nil || productFull.Product == nil {
				continue
			}

			if productFull.Product.BizInfo == nil {
				productFull.Product.BizInfo = &models.PrivateBizInfo{}
			}

			rpc.BuildProductBizInfo(ctx, *productFull, productFull.Product.BizInfo)
		}
	}
}

func (pg *ProductsGenerator) GetCountryIsoCode() string {
	if pg.BaseReqData != nil && pg.BaseReqData.AreaInfo.CountryIsoCode != nil {
		return *pg.BaseReqData.AreaInfo.CountryIsoCode
	}
	return ""
}

func (pg *ProductsGenerator) GetCityId() int32 {
	if pg.BaseReqData != nil {
		return pg.BaseReqData.AreaInfo.Area
	}
	return 0
}

func (pg *ProductsGenerator) GetToCityId() int32 {
	if pg.BaseReqData != nil {
		return pg.BaseReqData.AreaInfo.ToArea
	}
	return 0
}

func (pg *ProductsGenerator) IsHongKong() bool {
	return consts.HKCityId == pg.GetCityId() && consts.HKCityId == pg.GetToCityId()
}

// Deprecated: GetAfterPriceFilters 用于单测，业务禁止使用该方法.
func (pg *ProductsGenerator) GetAfterPriceFilters() []AfterPriceProductsFilter {
	return pg._afterPriceFilters
}
