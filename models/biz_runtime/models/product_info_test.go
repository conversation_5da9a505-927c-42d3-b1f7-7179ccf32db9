package models

import (
	"context"
	"testing"

	context2 "git.xiaojukeji.com/lego/context-go"

	bizconsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	tcutil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestProduct_IsTripcloudProduct(t *testing.T) {
	// case 1: p.IsTripcloud 非空，直接返回
	valTrue := true
	valFalse := false
	p1 := &Product{IsTripcloud: &valTrue}
	p2 := &Product{IsTripcloud: &valFalse}
	assert.True(t, p1.IsTripcloudProduct(context.Background()))
	assert.False(t, p2.IsTripcloudProduct(context.Background()))

	// case 2: p.IsTripcloud 为空，走 tcutil.IsTripcloudProductID
	patch := mockey.Mock(tcutil.IsTripcloudProductID).To(func(ctx context.Context, pid bizconsts.ProductID) bool {
		return pid == 1001
	}).Build()
	defer patch.UnPatch()

	p3 := &Product{ProductID: 1001, IsTripcloud: nil}
	p4 := &Product{ProductID: 1002, IsTripcloud: nil}
	assert.True(t, p3.IsTripcloudProduct(context.Background()))
	assert.False(t, p4.IsTripcloudProduct(context.Background()))
}

func TestProduct_BuildEstimateID(t *testing.T) {
	tracer := &context2.DefaultTrace{TraceId: "z13cni324u9104801849018490rj"}
	mockey.Mock(context2.GetTrace).Return(tracer).Build()
	mockey.Mock((&context2.DefaultTrace{}).GetTraceId).Return("").Build()
	mockey.Mock(reqctx.GetInt).Return(0).Build()
	mockey.Mock(reqctx.Set).Return().Build()

	defer mockey.UnPatchAll()

	p := &Product{}
	p.BuildEstimateID(context.Background(), nil, nil)
	assert.Equal(t, 32, len(p.EstimateID))
}
