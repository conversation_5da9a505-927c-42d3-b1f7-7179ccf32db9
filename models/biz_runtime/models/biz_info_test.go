package models

import (
	"context"
	"testing"

	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

// 该测试函数由AI自动生成
//func TestStringManipulation(t *testing.T) {
//	tests := []struct {
//		name     string
//		input    string
//		expected string
//	}{
//		{
//			name:     "Trim spaces",
//			input:    "hello",
//			expected: "hello",
//		},
//		//{
//		//	name:     "To upper",
//		//	input:    "HELLO",
//		//	expected: "HELLO",
//		//},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			result := strings.TrimSpace(tt.input)
//			result = strings.ToUpper(result)
//			assert.Equal(t, tt.expected, result)
//		})
//	}
//}

// 该测试函数由AI自动生成
//func TestLogFunctionality(t *testing.T) {
//	patches := ApplyFunc(log.Trace.Infof, func(ctx context.Context, format string, args ...interface{}) {
//		// Mock log function
//	})
//	defer patches.Reset()
//
//	t.Run("Log info", func(t *testing.T) {
//		ctx := context.Background()
//		log.Trace.Infof(ctx, "Test log: %s", "info")
//		// No assertion needed as we are testing log output
//	})
//}

// 定义测试所需的类型
type ExtendFeatureParsed struct {
	BusinessTravelOrderType string
	MultiRequiredProduct    []ProductInfo
}

type ProductInfo struct {
	ProductCategory int64
	CustomFeature   string
}

// mockOrderMatchRequest 实现 OrderMatchRequest 接口
type mockOrderMatchRequest struct {
	accessKeyId int32
	appVersion  string
	lang        string
	channel     int64
	clientType  int32
	orderId     string
	pageType    int32
	token       string
}

func (m mockOrderMatchRequest) GetAccessKeyId() int32 {
	return m.accessKeyId
}

func (m mockOrderMatchRequest) GetAppVersion() string {
	return m.appVersion
}

func (m mockOrderMatchRequest) GetLang() string {
	return m.lang
}

func (m mockOrderMatchRequest) GetCarpoolSeatNum() int64 {
	return 1
}

func (m mockOrderMatchRequest) GetToken() string {
	return "w4trefdaadfghmjhgfds"
}

func (m mockOrderMatchRequest) GetChannel() int64 {
	return m.channel
}

func (m mockOrderMatchRequest) GetClientType() int32 {
	return m.clientType
}

func (m mockOrderMatchRequest) GetOrderId() string {
	return m.orderId
}

func (m mockOrderMatchRequest) GetPageType() int32 {
	return m.pageType
}

// mockOrderInfo 用于测试的订单信息结构
type mockOrderInfo struct {
	PassengerCount      string
	ExtendFeatureParsed ExtendFeatureParsed
}

func TestCommonBizInfo_InitByOrderInfo(t *testing.T) {
	tests := []struct {
		name         string
		orderInfo    *dos.OrderInfo
		request      OrderMatchRequest
		wantSourceId int32
	}{
		{
			name: "正常场景-基础信息",
			orderInfo: &dos.OrderInfo{
				OrderInfo: &sdk.OrderInfo{
					PassengerCount: "2",
				},
				ExtendFeatureParsed: dos.ExtendFeatureStruct{},
			},
			request: mockOrderMatchRequest{
				accessKeyId: 1,
				appVersion:  "7.0.20",
				lang:        "zh-CN",
				channel:     1,
				clientType:  1,
				orderId:     "test_order_1",
				pageType:    1,
			},
			wantSourceId: 0,
		},
		{
			name: "新筑场景",
			orderInfo: &dos.OrderInfo{
				OrderInfo: &sdk.OrderInfo{
					PassengerCount: "3",
				},
				ExtendFeatureParsed: dos.ExtendFeatureStruct{
					BusinessTravelOrderType: "xinzhu_v1",
				},
			},
			request: mockOrderMatchRequest{
				accessKeyId: 1,
				appVersion:  "7.0.20",
				lang:        "zh-CN",
				channel:     1,
				clientType:  1,
				orderId:     "test_order_2",
				pageType:    1,
			},
			wantSourceId: source_id.SourceIDXinZhuAppendCar,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommonBizInfo{}
			c.InitByOrderInfo(context.Background(), tt.orderInfo, tt.request)

			// 验证sourceId
			if c.SourceId != tt.wantSourceId {
				t.Errorf("SourceId = %v, want %v", c.SourceId, tt.wantSourceId)
			}
		})
	}
}
