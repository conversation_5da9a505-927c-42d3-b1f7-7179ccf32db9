package fill_product_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type FillProductMetaInfo struct {
	baseReq *models.BaseReqData // 请求信息
}

func NewFillProductMetaInfo(ctx context.Context, req *models.BaseReqData) *FillProductMetaInfo {
	return &FillProductMetaInfo{
		baseReq: req,
	}
}

func (c *FillProductMetaInfo) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {

	for _, prod := range products {
		// 导流位赋值
		prod.BizInfo.FormShowType = consts.FormShowTypeAnycar
		if carpool.IsLowPriceCarpoolByInfos(prod.GetComboType(), prod.GetProductId(), prod.GetCarpoolType(), prod.GetRequireLevel()) ||
			(carpool.IsInterCityCarpool(prod.GetCarpoolType(), prod.GetComboType()) && consts.CarPoolTypeInterCityNew != prod.GetCarpoolType()) ||
			product_id.IsAutoDriving(prod.GetProductId(), prod.GetRequireLevelInt(), prod.GetComboType()) ||
			product_id.IsBargainCar(prod.GetProductId(), prod.GetRequireLevelInt()) {
			prod.BizInfo.FormShowType = consts.FormShowTypeGuide
		}

	}
	return nil, ""
}
