package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type DealMutexFilter struct {
	userType        int32
	currentProducts []models.ProductCategory
}

func NewDealMutexFilter(ctx context.Context, baseReqData *models.BaseReqData) *DealMutexFilter {
	return &DealMutexFilter{}
}

func (f *DealMutexFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var (
		currentProducts = make([]models.ProductCategory, 0)
		mutexMap        = map[string]string{
			util.ToString(estimate_pc_id.EstimatePcIdFastTaxi):       util.ToString(estimate_pc_id.EstimatePcIdTaxiMarketisationPutong),
			util.ToString(estimate_pc_id.EstimatePcIdFastHandpicked): util.ToString(estimate_pc_id.EstimatePcIdSpaciousCar),
		}
	)

	// mutexMap: key存在时，过滤value
	for _, product := range products {
		if losePcID, ok := mutexMap[util.ToString(product.ProductCategory)]; ok {
			currentProducts = append(currentProducts, models.ProductCategory(util.ToInt(losePcID)))
		}
	}

	f.currentProducts = currentProducts

	return f.currentProducts, ""
}
