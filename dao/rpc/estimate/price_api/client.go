package price_api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	PriceApiClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/priceApiClient"
)

const defaultCaller = "passenger"

var (
	client                     *PriceApiClient.Client
	ErrEmptyMultiEstimatePrice = errors.New("emptyMultiEstimatePrice")
	ErrSetQuotationFailed      = errors.New("setQuotationFailed")
)

func InitClient() (err error) {

	client, err = PriceApiClient.NewPriceApiClient("disf!biz-gs-price_api")
	if err != nil {
		return err
	}
	return nil
}

// GetMultiEstimatePriceParams 获取PriceApi入参
func GetMultiEstimatePriceParams(estimatePriceReqs []*PriceApi.EstimatePriceReq, extraInfo map[string]string) (req *PriceApi.MultiEstimatePriceReq) {
	return &PriceApi.MultiEstimatePriceReq{
		EstimatePriceReqs: estimatePriceReqs,
		Caller:            defaultCaller,
		ExtraInfo:         extraInfo,
	}
}

// GetMultiEstimatePrice 使用构建的入参数, 去price-api获取价格
func GetMultiEstimatePrice(ctx context.Context, multiEstimatePriceReq *PriceApi.MultiEstimatePriceReq) (*PriceApi.EstimateLiteResponse, error) {
	resp, err := client.EstimateLite(ctx, multiEstimatePriceReq)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetMultiEstimatePriceLite 使用构建的入参数, 去price-api获取价格
func GetMultiEstimatePriceLite(ctx context.Context, caller string, reqs []*PriceEstimateReq) (*PriceApi.EstimateLiteResponse, error) {
	var (
		err                   error
		pJs, orderExt         []byte
		multiEstimatePriceReq *PriceApi.MultiEstimatePriceReq
		resp                  *PriceApi.EstimateLiteResponse
	)
	multiEstimatePriceReq = &PriceApi.MultiEstimatePriceReq{
		Caller: defaultCaller,
	}
	businessOptionState := 0
	for _, req := range reqs {
		if req == nil {
			continue
		}
		estimateReq := &PriceApi.EstimatePriceReq{}
		if req.ExtraInfo != nil && req.ExtraInfo["business_option"] != "" && businessOptionState == 0 {
			if multiEstimatePriceReq.ExtraInfo == nil {
				multiEstimatePriceReq.ExtraInfo = make(map[string]string, 0)
			}
			multiEstimatePriceReq.ExtraInfo["business_option"] = req.ExtraInfo["business_option"]
			businessOptionState = 1
		}
		estimateReq.ExtraInfo = make(map[string]string)
		estimateReq.CommonInfo = req.CommonInfoSt
		estimateReq.OrderInfo = req.OrderInfoSt
		pJs, err = json.Marshal(req.PassengerInfo)
		if err != nil {
			continue
		}
		estimateReq.PassengerInfo = string(pJs)
		orderExt, err = json.Marshal(req.OrderExt)
		if err != nil {
			continue
		}
		estimateReq.ExtraInfo["order"] = string(orderExt)
		estimateReq.CustomServiceInfo = "[]"
		estimateReq.OneKeyActivity = "{}"
		multiEstimatePriceReq.EstimatePriceReqs = append(multiEstimatePriceReq.EstimatePriceReqs, estimateReq)
	}
	if caller != "" {
		multiEstimatePriceReq.Caller = caller
	}
	resp, err = client.EstimateLite(ctx, multiEstimatePriceReq)

	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetMultiEstimatePriceV3 ...
func GetMultiEstimatePriceV3(ctx context.Context, caller string, reqs []*PriceEstimateReq, extPID string) (*PriceApi.EstimateNewFormResponse, error) {
	var (
		err                   error
		pJs, orderExt         []byte
		multiEstimatePriceReq *PriceApi.MultiEstimatePriceReq
		resp                  *PriceApi.EstimateNewFormResponse
	)
	multiEstimatePriceReq = &PriceApi.MultiEstimatePriceReq{
		Caller: defaultCaller,
	}
	if extPID != "" {
		if multiEstimatePriceReq.ExtraInfo == nil {
			multiEstimatePriceReq.ExtraInfo = make(map[string]string)
		}
		multiEstimatePriceReq.ExtraInfo["ext_pid"] = extPID
	}
	for _, req := range reqs {
		if req == nil {
			continue
		}
		estimateReq := &PriceApi.EstimatePriceReq{}
		estimateReq.ExtraInfo = req.ExtraInfo
		estimateReq.CommonInfo = req.CommonInfoSt
		estimateReq.OrderInfo = req.OrderInfoSt
		pJs, err = json.Marshal(req.PassengerInfo)
		if err != nil {
			continue
		}
		estimateReq.PassengerInfo = string(pJs)
		orderExt, err = json.Marshal(req.OrderExt)
		if err != nil {
			continue
		}
		estimateReq.ExtraInfo["order"] = string(orderExt)
		estimateReq.CustomServiceInfo = "[]"
		estimateReq.OneKeyActivity = "{}"
		multiEstimatePriceReq.EstimatePriceReqs = append(multiEstimatePriceReq.EstimatePriceReqs, estimateReq)
	}
	if caller != "" {
		multiEstimatePriceReq.Caller = caller
	}
	resp, err = client.EstimateV3(ctx, multiEstimatePriceReq)

	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetMultiEstimateOversea ...
func GetMultiEstimateOversea(ctx context.Context, caller string, reqs []*PriceEstimateReq, extPID string) (*PriceApi.EstimateNewFormResponse, error) {
	var (
		err                   error
		pJs, orderExt         []byte
		multiEstimatePriceReq *PriceApi.MultiEstimatePriceReq
		resp                  *PriceApi.EstimateNewFormResponse
	)
	multiEstimatePriceReq = &PriceApi.MultiEstimatePriceReq{
		Caller: defaultCaller,
	}
	if extPID != "" {
		if multiEstimatePriceReq.ExtraInfo == nil {
			multiEstimatePriceReq.ExtraInfo = make(map[string]string)
		}
		multiEstimatePriceReq.ExtraInfo["ext_pid"] = extPID
	}
	for _, req := range reqs {
		if req == nil {
			continue
		}
		estimateReq := &PriceApi.EstimatePriceReq{}
		estimateReq.ExtraInfo = req.ExtraInfo
		estimateReq.CommonInfo = req.CommonInfoSt
		estimateReq.OrderInfo = req.OrderInfoSt
		pJs, err = json.Marshal(req.PassengerInfo)
		if err != nil {
			continue
		}
		estimateReq.PassengerInfo = string(pJs)
		orderExt, err = json.Marshal(req.OrderExt)
		if err != nil {
			continue
		}
		estimateReq.ExtraInfo["order"] = string(orderExt)
		estimateReq.CustomServiceInfo = "[]"
		estimateReq.OneKeyActivity = "{}"
		multiEstimatePriceReq.EstimatePriceReqs = append(multiEstimatePriceReq.EstimatePriceReqs, estimateReq)
	}
	if caller != "" {
		multiEstimatePriceReq.Caller = caller
	}
	resp, err = client.EstimateOversea(ctx, multiEstimatePriceReq)

	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetQuotationBatch 批量获取报价单
func GetQuotationBatch(ctx context.Context, req *PriceQuotationBatch) (*PriceApi.GetQuotationBatchResp, error) {
	if req == nil {
		return nil, errors.New("req is nil")
	}

	getQuotationBatchReq := &PriceApi.GetQuotationBatchReq{
		Fields:         req.Fields,
		EstimateIdList: req.EstimateIdList,
	}

	resp, err := client.GetQuotationBatch(ctx, getQuotationBatchReq)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetQuotationBatchInfo 批量获取报价单
func GetQuotationBatchInfo(ctx context.Context, req *PriceQuotationBatch) (map[string]PriceApi.EstimateQuotation, error) {
	if req == nil {
		return nil, errors.New("req is nil")
	}

	getQuotationBatchReq := &PriceApi.GetQuotationBatchReq{
		Fields:         req.Fields,
		EstimateIdList: req.EstimateIdList,
	}

	resp, err := client.GetQuotationBatch(ctx, getQuotationBatchReq)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp.Data, nil
}

// GetMultiEstimatePriceForRoute ...
func GetMultiEstimatePriceForRoute(ctx context.Context, caller string, reqs []*PriceEstimateReq, extraInfo map[string]string) (*PriceApi.EstimateNewFormResponse, error) {
	var (
		err                   error
		pJs                   []byte
		multiEstimatePriceReq *PriceApi.MultiEstimatePriceReq
		resp                  *PriceApi.EstimateNewFormResponse
	)
	multiEstimatePriceReq = &PriceApi.MultiEstimatePriceReq{
		Caller:    caller,
		ExtraInfo: extraInfo,
	}
	for _, req := range reqs {
		if req == nil {
			continue
		}
		estimateReq := &PriceApi.EstimatePriceReq{}
		estimateReq.ExtraInfo = req.ExtraInfo
		estimateReq.CommonInfo = req.CommonInfoSt
		estimateReq.OrderInfo = req.OrderInfoSt
		pJs, err = json.Marshal(req.PassengerInfo)
		if err != nil {
			continue
		}
		estimateReq.PassengerInfo = string(pJs)

		estimateReq.CustomServiceInfo = "[]"
		estimateReq.OneKeyActivity = "{}"
		multiEstimatePriceReq.EstimatePriceReqs = append(multiEstimatePriceReq.EstimatePriceReqs, estimateReq)
	}
	if caller != "" {
		multiEstimatePriceReq.Caller = caller
	}
	resp, err = client.EstimateForRoute(ctx, multiEstimatePriceReq)

	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetMultiEstimatePriceForRouteDriverOff ...
func GetMultiEstimatePriceForRouteDriverOff(ctx context.Context, req *PriceApi.MultiEstimatePriceByRouteForDriveOffReq) (*PriceApi.EstimateNewFormResponse, error) {
	resp, err := client.EstimateForRouteDriveOff(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp, nil
}

// GetMultiEstimatePriceForRouteCapPrice ...
func GetMultiEstimatePriceForRouteCapPrice(ctx context.Context, req *PriceApi.MultiEstimatePriceByRouteForDriveOffReq) (map[string]*PriceApi.EstimateForRouteCapPriceData, error) {
	resp, err := client.EstimateForRouteCapPrice(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrEmptyMultiEstimatePrice
	}

	return resp.Data, nil
}

// GetQuotationExpands ...
func GetQuotationExpands(ctx context.Context, estimateList []string) (map[string][]*PriceApi.FlattenQuotation, error) {
	if len(estimateList) == 0 {
		return nil, errors.New("err req : estiamte ids is nil")
	}

	req := &PriceApi.FlattenQuotationRequest{
		EstimateIds: estimateList,
	}
	resp, err := client.GetQuotationExpand(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 || resp.Data == nil {
		return nil, errors.New(fmt.Sprintf("err with quotation resp: errno = %v, data==nil %v", resp.Errno, resp.Data == nil))
	}

	return resp.Data, nil
}

// MultiEstimateByOrder ...
func MultiEstimateByOrder(ctx context.Context, request *PriceApi.MultiEstimateByOrderRequest) (map[string]*PriceApi.EstimateByOrderData, error) {
	resp, err := client.MultiEstimatePriceByOrder(ctx, request)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, errors.New("errno is not equal zero")
	}

	return resp.Data, nil
}

// SetQuotation 写报价单
func SetQuotation(ctx context.Context, req *PriceApi.QuotationSetV2Req) (resp *PriceApi.QuotationSetV2Resp, err error) {
	if req == nil {
		return nil, errors.New("req is nil")
	}
	setQuotationReq := &PriceApi.QuotationSetV2Req{
		Params:     req.Params,
		EstimateId: req.EstimateId,
	}

	resp, err = client.SetQuotationV2(ctx, setQuotationReq)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Errno != 0 {
		return nil, ErrSetQuotationFailed
	}

	return resp, nil
}
