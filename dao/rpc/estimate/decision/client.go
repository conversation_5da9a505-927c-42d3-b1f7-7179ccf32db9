package decision

import (
	"context"
	"errors"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	EstimateDecisionClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/estimateDecisionClient"
)

var (
	client                                        *EstimateDecisionClient.Client
	ErrGetProducts                                      = errors.New("productsWithErrno")
	ErrStrStopoverPointsConflictsWithScenes             = "productsWithErrnoStopoverPointsConflictsWithScenes"
	ErrWithErrnoStopoverPointsConflictsWithScenes       = errors.New(ErrStrStopoverPointsConflictsWithScenes)
	ErrNumStopoverPointsConflictsWithScenes       int32 = 1035013
	ErrGetDDSProductsSimple                             = errors.New("GetDDSProductsSimpleWithErrno")
)

func InitClient() (err error) {
	client, err = EstimateDecisionClient.NewEstimateDecisionClient("disf!biz-gs-dds")
	if err != nil {
		return err
	}
	return nil
}

//func GetProductsParams(ctx context.Context, userInfo *params.UserInfo, areaInfo *params.AreaInfo, commonInfo *params.CommonInfo) (userInfoV2 *EstimateDecision.UserInfoV2, commonInfoV2 *EstimateDecision.CommonInfoV2) {
//	var (
//		stopoverPoints string
//	)
//
//	if commonInfo.StopoverPoints != nil {
//		var wayPointsInfo []*params.WayPointsInfo
//		if commonInfo.StopoverPoints.WayPointsAInfo != nil {
//			wayPointsInfo = append(wayPointsInfo, commonInfo.StopoverPoints.WayPointsAInfo)
//		}
//		if commonInfo.StopoverPoints.WayPointsBInfo != nil {
//			wayPointsInfo = append(wayPointsInfo, commonInfo.StopoverPoints.WayPointsBInfo)
//		}
//		bsStopoverPoints, err := json.Marshal(wayPointsInfo)
//		if err == nil {
//			stopoverPoints = string(bsStopoverPoints)
//		} else {
//			log.Trace.Infof(ctx, trace.DLTagUndefined, "jsonFormat: %v", err)
//		}
//	}
//
//	userInfoV2 = &EstimateDecision.UserInfoV2{
//		Uid:   int64(userInfo.UID),
//		Pid:   int64(userInfo.PID),
//		Phone: userInfo.Phone,
//	}
//	commonInfoV2 = &EstimateDecision.CommonInfoV2{
//		AppVersion:   commonInfo.AppVersion,
//		AccessKeyId:  commonInfo.AccessKeyID,
//		ClientType:   commonInfo.ClientType,
//		Channel:      commonInfo.Channel,
//		Lang:         commonInfo.Lang,
//		Imei:         commonInfo.Imei,
//		StartLat:     areaInfo.StartLat,
//		StartLng:     areaInfo.StartLng,
//		DestLat:      areaInfo.DestLat,
//		DestLng:      areaInfo.DestLng,
//		City:         areaInfo.City,
//		District:     areaInfo.District,
//		County:       areaInfo.County,
//		ToCounty:     areaInfo.ToCounty,
//		StartPoiId:   areaInfo.StartPoiID,
//		StartPoiType: areaInfo.StartPoiType,
//		//PoiSite:               "",
//		StartName:      areaInfo.StartName,
//		StartAddress:   areaInfo.StartAddress,
//		DestPoiId:      areaInfo.DestPoiID,
//		DestPoiType:    areaInfo.DestPoiType,
//		DestName:       areaInfo.DestName,
//		DestAddress:    areaInfo.DestAddress,
//		MapType:        commonInfo.MapType,
//		MenuId:         commonInfo.MenuID,
//		PageType:       0,
//		OrderType:      int16(commonInfo.OrderType),
//		CallCarType:    commonInfo.CallCarType,
//		StopoverPoints: &stopoverPoints,
//		//DepartureRange:        ,
//		//DepartureTime:         0,
//		//CurrentLat:            0,
//		//CurrentLng:            0,
//		//From:                  "",
//		//Popefs:                nil,
//		//LuxurySelectCarlevels: nil,
//		//AirportType:           nil,
//		//StopoverPoints:        nil,
//	}
//	return
//}

func GetDDSProducts(ctx context.Context, req *EstimateDecision.ProductsReq) ([]*EstimateDecision.Product, error) {
	var (
		err          error
		productsResp *EstimateDecision.ProductsResp
	)

	productsResp, err = client.Products(ctx, req)
	if err != nil {
		return nil, err
	}
	if productsResp == nil {
		return nil, ErrGetProducts
	}
	if productsResp.Errno != 0 || productsResp.Data == nil {

		if productsResp.Errno == ErrNumStopoverPointsConflictsWithScenes {
			return nil, ErrWithErrnoStopoverPointsConflictsWithScenes
		}
		return nil, ErrGetProducts
	}
	return productsResp.Data.ProductList, nil
}

func GetDDSProductsSimple(ctx context.Context, req *EstimateDecision.ProductSimpleReq) (*EstimateDecision.ProductList, error) {
	var (
		err  error
		resp *EstimateDecision.ProductSimpleResp
	)

	resp, err = client.Productsimple(ctx, req)
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, ErrGetDDSProductsSimple
	}
	if resp.Errno != 0 || resp.Data == nil {
		return nil, ErrGetDDSProductsSimple
	}
	return resp.Data, nil
}
