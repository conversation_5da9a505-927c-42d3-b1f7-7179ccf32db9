package athena

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	Trace "git.xiaojukeji.com/nuwa/trace"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/AthenaApiv3Client"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	context2 "git.xiaojukeji.com/lego/context-go"
)

var client *AthenaApiv3Client.Client

func InitClient() (err error) {
	client, err = AthenaApiv3Client.NewAthenaApiv3Client("disf!engine-athena-athena_api")
	if err != nil {
		return err
	}
	return nil
}

func GetUserRecommendInfo(ctx context.Context, req *AthenaApiv3.AthenaUserRecommendInfoReq) *AthenaApiv3.AthenaUserRecommendInfoResp {
	trace := getTrace(ctx)
	resp, err := client.GetAthenaUserRecommendInfo(ctx, req, &trace)
	if err != nil || resp == nil {
		return nil
	}

	if 0 != resp.Errno {
		return nil
	}

	return resp
}

func GetAthenaBubbleRecommend(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) *AthenaApiv3.AthenaGuideRecommendResp {
	trace := getTrace(ctx)
	resp, err := client.GetAthenaBubbleRecommend(ctx, req, &trace)
	if err != nil || resp == nil {
		return nil
	}

	if 0 != resp.ErrNo {
		return nil
	}

	return resp
}

func GetBubbleGuideScene(ctx context.Context, req *AthenaApiv3.AthenaBubbleNewReq) *AthenaApiv3.AthenaGuideSceneResponse {
	trace := getTrace(ctx)
	resp, err := client.GetBubbleGuideScene(ctx, req, &trace)
	if err != nil || resp == nil {
		return nil
	}

	if 0 != resp.ErrNo {
		return nil
	}

	return resp
}

func GetMultiProductRank(ctx context.Context, orderInfo *AthenaApiv3.MultiProductOrderInfo, products []*AthenaApiv3.MultiRequireProduct) []*AthenaApiv3.MultiRequireProduct {
	productsStr, err := json.Marshal(products)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "fail to json products for GetMultiProductRank with err %v", err)
	}
	trace := getTrace(ctx)
	resp, err := client.GetMultiProductRank(ctx, orderInfo, string(productsStr), &trace)
	if err != nil || resp == nil {
		return nil
	}

	if 0 != resp.ErrNo {
		return nil
	}
	return resp.MultiRequireProduct
}

func UTRecommend(ctx context.Context, req *AthenaApiv3.AthenaUTRecommendReq) *AthenaApiv3.AthenaUTRecommendResp {
	trace := getTrace(ctx)
	resp, err := client.UTRecommend(ctx, req, &trace)
	if err != nil || resp == nil {
		return nil
	}

	if 0 != resp.ErrNo {
		return nil
	}
	return resp
}

func GetMultiEstimateEtpEtaInfo(ctx context.Context, req *AthenaApiv3.AthenaMultiEstimateEtpEtdInfoReq) *AthenaApiv3.AthenaMultiEstimateEtpEtdInfoResp {
	trace := getTrace(ctx)
	resp, err := client.GetMultiEstimateEtpEtaInfo(ctx, req, &trace)
	if err != nil || resp == nil || resp.EstimateEtpEtdInfo == nil {
		return nil
	}

	if 0 != resp.ErrNo {
		return nil
	}

	return resp
}

func getTrace(ctx context.Context) AthenaApiv3.Trace {
	_trace := context2.GetTrace(ctx)
	hintContent := _trace.GetHintContent()
	hintCode, _ := strconv.ParseInt(_trace.GetHintCode(), 10, 64)
	return AthenaApiv3.Trace{
		TraceID:     _trace.GetTraceId(),
		SpanID:      _trace.GetSpanId(),
		Caller:      conf.Viper.GetString("module.name"),
		SrcMethod:   nil,
		HintCode:    &hintCode,
		HintContent: &hintContent,
	}
}

func GetAthenaUserRecommendInfo(ctx context.Context, req *AthenaApiv3.AthenaUserRecommendInfoReq) (*AthenaApiv3.AthenaUserRecommendInfoResp, error) {
	_trace := getTrace(ctx)
	resp, err := client.GetAthenaUserRecommendInfo(ctx, req, &_trace)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		err = errors.New("resp is nil")
		return nil, err
	}

	if 0 != resp.Errno {
		err = errors.New("errno is not equal zero")
		return nil, err
	}

	return resp, nil
}

// GetAthenaBubbleExpectInfo ...
func GetAthenaBubbleExpectInfo(ctx context.Context, req *AthenaApiv3.AthenaBubbleExpectInfoReq) (*AthenaApiv3.AthenaBubbleExpectInfoResp, error) {
	_trace := getTrace(ctx)
	resp, err := client.GetAthenaBubbleExpectInfo(ctx, req, &_trace)
	if err != nil {
		return nil, err
	}

	if resp == nil {
		err = errors.New("resp is nil")
		return nil, err
	}

	if 0 != resp.ErrNo {
		err = errors.New("errno is not equal zero")
		return nil, err
	}

	return resp, nil
}
