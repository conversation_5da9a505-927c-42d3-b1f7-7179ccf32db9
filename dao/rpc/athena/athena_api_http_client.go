package athena

import (
	"context"
	"encoding/json"

	AthenaApiHttp "git.xiaojukeji.com/dirpc/dirpc-go-http-AthenaApiHttp"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/athenaApiClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	RoleDriver      = 1
	OrderStatBubble = 5
	CoordTypeSoso   = 2
	AnyCar          = 666
)

var httpClient *athenaApiClient.Client

func InitAthenaApiHttpClient() error {
	var err error
	httpClient, err = athenaApiClient.NewAthenaApiHttpClient("disf!engine-athena-athena_api")
	if err != nil {
		return err
	}
	return nil
}

func getSrc(accessKeyID int32) string {
	if accessKeyID == bizCommonConsts.AccessKeyIDDiDiIos {
		return "ios"
	}
	if accessKeyID == bizCommonConsts.AccessKeyIDDiDiAndroid {
		return "android"
	}
	return "webapp"
}

type ReqMultiProduct struct {
	BusinessId   int64  `json:"product_id"` //他们的坑，这里显示product_id
	RequireLevel int64  `json:"require_level"`
	ComboType    int64  `json:"combo_type"`
	IsSelected   int    `json:"is_selected"`
	BubbleId     string `json:"bubble_id"`
	RouteId      string `json:"route_id"`
	LevelType    int32  `json:"level_type"`
}

type ReqExtra struct {
	Tlat   float64 `json:"tlat"`
	Tlng   float64 `json:"tlng"`
	MenuId string  `json:"menu_id"`
}

func getMultiProductJSON(ctx context.Context, productInfo *biz_runtime.ProductInfoFull) string {
	var multiProduct = make([]*ReqMultiProduct, 0)
	if productInfo == nil || productInfo.Product == nil {
		return ""
	}
	Product := &ReqMultiProduct{
		BusinessId:   productInfo.Product.BusinessID,
		RequireLevel: util.ToInt64(productInfo.Product.RequireLevel),
		ComboType:    productInfo.Product.ComboType,
		IsSelected:   1,
		BubbleId:     productInfo.Product.EstimateID,
		LevelType:    productInfo.Product.LevelType,
	}
	multiProduct = append(multiProduct, Product)
	multiProductJSON, err := json.Marshal(multiProduct)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json.Marshal(MultiProduct) error[%+v] MultiProduct[%+v]", err, util.JustJsonEncode(multiProduct))
		return ""
	}
	return string(multiProductJSON)
}

func InitAthenaApiHttpReq(ctx context.Context, req *proto.HomePageCallCarEstimateReq,
	productInfo *biz_runtime.ProductInfoFull) *AthenaApiHttp.DriverListAndHintInfoReq {
	if req == nil || productInfo == nil || productInfo.Product == nil {
		return nil
	}
	ProductId := int32(AnyCar)
	Role := int32(RoleDriver)
	Radius := float64(5000)
	OrderTab := int32(1)
	OrderStat := int32(OrderStatBubble)
	coordType := int32(CoordTypeSoso)
	CarLevel := int32(0)
	TripCountry := "CN"
	bubbleID := productInfo.GetEstimateID()
	multiProductJson := getMultiProductJSON(ctx, productInfo)
	src := getSrc(req.AccessKeyId)
	return &AthenaApiHttp.DriverListAndHintInfoReq{
		ProductId:    &ProductId,
		Role:         &Role,
		Radius:       &Radius,
		OrderTab:     &OrderTab,
		OrderStat:    &OrderStat,
		CoordType:    &coordType,
		CarLevel:     &CarLevel,
		TripCountry:  &TripCountry,
		Token:        &req.Token,
		AppVersion:   &req.AppVersion,
		Lang:         &req.Lang,
		Lat:          &req.FromLat,
		Lng:          &req.FromLng,
		TLat:         &req.ToLat,
		TLng:         &req.ToLng,
		BubbleId:     &bubbleID,
		MultiProduct: &multiProductJson,
		Src:          &src,
	}
}

func GetDriverEta(ctx context.Context, req *proto.HomePageCallCarEstimateReq, productInfo *biz_runtime.ProductInfoFull) int32 {
	if productInfo == nil || productInfo.Product == nil {
		return 0
	}
	athenaReq := InitAthenaApiHttpReq(ctx, req, productInfo)
	if athenaReq == nil {
		return 0
	}
	resp, err := httpClient.GetDriverLocJson(ctx, athenaReq)
	if err != nil || resp == nil || resp.Errno != 0 || resp.Data == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "athena GetDriverLocJson error req[%+v],resp[%+v],error[%+v]",
			util.ToJSONString(req), util.ToJSONString(resp), err)
		return 0
	}
	return resp.Data.Eta
}
