package dos

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	DosClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/dosclient"
)

type RequiredProductStruct struct {
	ProductCategory           int     `json:"product_category"`
	EstimatePcID              string  `json:"estimate_pc_id"`
	ProductId                 int     `json:"product_id"`
	BusinessId                int     `json:"business_id"`
	RequiredLevel             string  `json:"require_level"`
	ComboType                 int     `json:"combo_type"`
	CarpoolType               int64   `json:"carpool_type"`
	GroupKey                  string  `json:"group_key"`
	GuideDiscountForm         int     `json:"guide_discount_form"`
	LevelType                 int     `json:"level_type"`
	CarpoolPriceType          int     `json:"carpool_price_type"`
	CarpoolRequireNum         int     `json:"carpool_require_num"` // 预期拼成人数（影响拼成计算）
	CustomFeature             string  `json:"custom_feature"`      // anycar品类下
	EstimateID                string  `json:"estimate_id"`
	CapPrice                  float64 `json:"cap_price"`
	EstimateFee               float64 `json:"estimate_fee"`
	SceneList                 string  `json:"scene_list"`
	PayType                   int     `json:"pay_type"`
	PassengerBargainRangeInfo string  `json:"passenger_bargain_range_info"`
	PassengerCount            int     `json:"passenger_count"`
	DepartureRange            []int64 `json:"departure_range"`
	PreUpgradeGroupKey        string  `json:"pre_upgrade_group_key"`
	AirportType               int     `json:"airport_type"`
	RailwayType               int     `json:"railway_type"`
	HotelType                 int     `json:"hotel_type"`
	MultiPriceScene           string  `json:"multi_price_scene"`
}

type FormattedOrder struct {
	OrderId      int64
	RequireLevel int32
	ComboType    int32
	Area         int32
}

// ExtendFeatureStruct 数据来源: dos序列化出来的
type ExtendFeatureStruct struct {
	MultiRequiredProduct    map[string]RequiredProductStruct `json:"multi_require_product"`
	AnycarSingleMock        int                              `json:"anycar_single_mock"`
	NewOrderInfo            NewOrderInfoStruct               `json:"neworder_info"`
	TPInfo                  TPFormInfo                       `json:"tp_form_info"`
	EstimateTraceID         string                           `json:"estimate_trace_id"`
	AddrInfo                AddrInfo                         `json:"addr_info"`
	UnderWaterBargainOrder  bool                             `json:"under_water_bargain_order"`
	DepartureRange          []int64                          `json:"departure_range"`
	BusinessTravelOrderType string                           `json:"business_travel_order_type"`
}

type TPFormInfo struct {
	TPInfoList []TPInfo `json:"tp_info"`
}
type TPInfo struct {
	PcID    int64  `json:"product_category"`
	ETPText string `json:"etp_text"`
}

type NewOrderInfoStruct struct {
	PlatformType  int32                 `json:"platform_type"`
	CustomFeature []CustomFeatureStruct `json:"custom_feature"` // 用户上传的附加需求信息  非anycar时存在, anycar时yuying写进去值
}

type AddrInfo struct {
	StartCityName   string `json:"start_city_name"`
	StartCountyName string `json:"start_county_name"`
	DestCityName    string `json:"dest_city_name"`
	DestCountyName  string `json:"dest_county_name"`
}

// CustomFeatureStruct 服务id-服务个数
type CustomFeatureStruct struct {
	Id               int64  `json:"id"`
	Count            int64  `json:"count"`
	ServiceType      *int32 `json:"service_type"`
	WithoutPrivilege *int32 `json:"without_privilege,omitempty"`
}

type SceneItem struct {
	Id    int    `json:"id"`
	Value string `json:"value"`
}

type OrderInfo struct {
	*sdk.OrderInfo
	FormattedInfo       FormattedOrder
	ExtendFeatureParsed ExtendFeatureStruct
}

var client *DosClient.Client

func InitClient() (err error) {
	client, err = DosClient.NewDosClient("disf!common-plat-public-dos-dos_order")
	if err != nil {
		return err
	}
	return nil
}

func GetOrderInfo(ctx context.Context, orderID int64, district string) (*OrderInfo, error) {
	req := sdk.NewGetOrderInfoReq()
	req.OrderId = fmt.Sprint(orderID)
	req.District = district
	productId := fmt.Sprint(0)
	req.ProductId = &productId
	resp, err := client.GetOrderInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	if resp.Errno != 0 {
		return nil, errors.New(fmt.Sprintf("errno : %v", resp.Errno))
	}

	var extendFeatureStruct ExtendFeatureStruct
	err = json.Unmarshal([]byte(resp.OrderInfo.ExtendFeature), &extendFeatureStruct)
	if err != nil {
		return nil, err
	}

	orderInfo := &OrderInfo{
		OrderInfo:           resp.OrderInfo,
		FormattedInfo:       formatOrderInfo(resp.OrderInfo),
		ExtendFeatureParsed: extendFeatureStruct,
	}
	return orderInfo, nil
}

func formatOrderInfo(info *sdk.OrderInfo) FormattedOrder {
	orderId, _ := strconv.ParseInt(info.OrderId, 10, 64)
	requireLevel, _ := strconv.ParseInt(info.RequireLevel, 10, 64)
	comboType, _ := strconv.ParseInt(info.ComboType, 10, 64)
	area, _ := strconv.ParseInt(info.Area, 10, 64)
	return FormattedOrder{
		OrderId:      orderId,
		RequireLevel: int32(requireLevel),
		ComboType:    int32(comboType),
		Area:         int32(area),
	}
}
