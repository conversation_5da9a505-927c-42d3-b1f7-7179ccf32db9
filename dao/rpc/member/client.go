package member

import (
	"context"
	"encoding/json"
	"errors"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/GoMemberClient"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
)

const disfName = "disf!biz-passenger-operation-pios"
const EncryptKey = "ba9A4eF31477dAB2"

var (
	client                   *GoMemberClient.Client
	ErrMultiProductQueryFail = errors.New("fail to get member info")
)

type Product struct {
	BusinessId   int64 `json:"business_id"`
	RequireLevel int64 `json:"require_level"`
	ComboType    int64 `json:"combo_type"`
	CarpoolType  int64 `json:"carpool_type"`
}

func InitClient() (err error) {
	client, err = GoMemberClient.NewGoMemberClient(disfName)
	if err != nil {
		return err
	}

	return nil
}

func MultiProductQuery(ctx context.Context, uid int64, cityId int32, productList []Product, enableNewWaitReply int32) (
	productListRes *GoMember.MultiQueryV2Resp, err error) {

	productListStr, _ := json.Marshal(productList)
	version := int32(3)
	req := &GoMember.MultiQueryV2Req{
		Caller:      "mamba",
		CityId:      &cityId,
		Uid:         &uid,
		ProductList: string(productListStr),
		SwitchFlow:  &version,
	}
	if enableNewWaitReply != 0 {
		req.EnableNewWaitReply = &enableNewWaitReply
	}
	sign, err := getSign(req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to gen sign with err %v", err)
		return nil, ErrMultiProductQueryFail
	}
	req.Sign = sign

	productListRes, err = client.MultiProductsV2Query(ctx, req)
	return
}

// EnableNewWaitReply ...
func EnableNewWaitReply(ctx context.Context, accessKeyID int32) int32 {
	if accessKeyID == 19 {
		return 1
	} else {
		return 0
	}
}

func getSign(params interface{}) (string, error) {
	jsStr, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	paramsMap := make(map[string]interface{})
	err = json.Unmarshal(jsStr, &paramsMap)
	if err != nil {
		return "", err
	}

	return EncryptMapWithoutSign(paramsMap, EncryptKey)
}
