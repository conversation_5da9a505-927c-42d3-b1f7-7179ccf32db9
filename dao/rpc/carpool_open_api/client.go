package carpool_open_api

import (
	"context"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	CarpoolOpenApiClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/carpoolOpenApiClient"
)

var client *CarpoolOpenApiClient.Client

// Init 初始化
func Init() (err error) {
	if client, err = CarpoolOpenApiClient.NewCarpoolOpenApiClient("disf!engine-public-carpool-carpool_open_api"); err != nil {
		return err
	}
	return nil
}

// GetQueryStationAndInventory 获取大车推荐路线、时间片、库存
func GetQueryStationAndInventory(ctx context.Context, req *CarpoolOpenApi.QueryStationAndInventoryRequest, trace *CarpoolOpenApi.Trace) (*CarpoolOpenApi.QueryStationAndInventoryResponse, error) {
	resp, err := client.QueryStationAndInventory(ctx, req, trace)
	return resp, err
}

// GetBatchQueryRouteShiftInventoryInfo 批量获取大车推荐路线、时间片、库存
func GetBatchQueryRouteShiftInventoryInfo(ctx context.Context, req *CarpoolOpenApi.QueryRouteShiftInventoryInfoReq, trace *CarpoolOpenApi.Trace) (*CarpoolOpenApi.BatchQueryRouteShiftInventoryInfoResp, error) {
	resp, err := client.BatchQueryRouteShiftInventoryInfo(ctx, req, trace)
	return resp, err
}

// GetQueryRouteShiftInventoryByDay 获取大巴14天内起终点内的路线以及库存
func GetQueryRouteShiftInventoryByDay(ctx context.Context, req *CarpoolOpenApi.QueryRouteShiftInventoryInfoReq, trace *CarpoolOpenApi.Trace) (*CarpoolOpenApi.QueryRouteShiftInventoryByDayResp, error) {
	resp, err := client.QueryRouteShiftInventoryByDay(ctx, req, trace)
	return resp, err
}

// GetQueryQueryShiftDetail 获取大巴班次维度的信息
func GetQueryShiftDetail(ctx context.Context, req *CarpoolOpenApi.QueryShiftDetailRequest, trace *CarpoolOpenApi.Trace) (*CarpoolOpenApi.QueryShiftDetailResponse, error) {
	resp, err := client.QueryShiftDetail(ctx, req, trace)
	return resp, err
}

// 批量获取大巴班次维度的信息
func GetBatchQueryShiftDetail(ctx context.Context, req *CarpoolOpenApi.BatchQueryShiftDetailRequest, trace *CarpoolOpenApi.Trace) (*CarpoolOpenApi.BatchQueryShiftDetailResponse, error) {
	resp, err := client.BatchQueryShiftDetail(ctx, req, trace)
	return resp, err
}
