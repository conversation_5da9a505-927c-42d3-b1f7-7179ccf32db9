package prfs

import (
	"context"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	PrfsClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/prfsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
)

var (
	client *PrfsClient.Client
)

// InitClient 初始化赔付client
func InitClient() (err error) {
	client, err = PrfsClient.NewPrfsClient("disf!biz-gs-prfs")

	if err != nil {
		return err
	}
	return nil
}

// GetNeedVerified 使用构建的入参数, 去prfs获取客企是否需要实名制字段
func GetNeedVerified(ctx context.Context, req *Prfs.GetNeedVerifiedReq) (rsp *Prfs.GetNeedVerifiedResp) {
	rsp, err := client.GetNeedVerified(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs GetNeedVerified with err %v", err)
		return nil
	}
	return rsp
}

// GetRouteDetail 使用构建的入参数, 去prfs获取大巴路线详细信息
func GetRouteDetail(ctx context.Context, req *Prfs.RouteDetailReq) (rep *Prfs.RouteDetailResp) {
	rsp, err := client.RouteDetail(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs GetRouteDetail with err %v", err)
		return nil
	}
	return rsp
}

// GetBatchRouteDetail 使用构建的入参数, 去prfs获取大巴批量路线详细信息
func GetBatchRouteDetail(ctx context.Context, req *Prfs.BatchRouteDetailReq) (rep *Prfs.BatchRouteDetailResp) {
	rsp, err := client.BatchRouteDetail(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs GetRouteDetail with err %v", err)
		return nil
	}
	return rsp
}

// GetCityStationV2 使用构建的入参数, 去prfs获取大巴城市区县可选站点列表
func GetCityStationV2(ctx context.Context, req *Prfs.PGetOpenCityStationV2Req) (rep *Prfs.PGetOpenCityStationV2Resp) {
	rsp, err := client.PGetOpenCityStationV2(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs GetRouteDetail with err %v", err)
		return nil
	}
	return rsp
}

func GetAirportType(ctx context.Context, productId string, Flat float64, Flng float64, Tlat float64, Tlng float64) (sceneType int32, sceneId int32) {
	var (
		startingFenceInfo []*Prfs.FenceInfo
		destFenceInfo     []*Prfs.FenceInfo
	)
	req := &Prfs.AtMultiFenceReq{
		From:    productId,
		Flat:    Flat,
		Flng:    Flng,
		Tlat:    Tlat,
		Tlng:    Tlng,
		Feature: 1,
	}

	fenceResult, err := client.AtMultiFence(ctx, req)
	if err != nil || fenceResult == nil || fenceResult.Data == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs GetAirportType with err %v", err)
		return
	}

	if fenceResult.Data.StartingInfo != nil {
		startingFenceInfo = fenceResult.Data.StartingInfo.FenceInfo
	}

	if fenceResult.Data.DestInfo != nil {
		destFenceInfo = fenceResult.Data.DestInfo.FenceInfo
	}

	//判断是否是接机场景
	if len(startingFenceInfo) > 0 {
		for _, fenceInfo := range startingFenceInfo {
			if checkOrderTag(ctx, *fenceInfo.Tag, []string{"tag_airport"}) {
				sceneType = AIRPORT_TYPE_FROM
			}

			sceneInfos := fenceInfo.SceneInfo
			if len(sceneInfos) == 0 {
				continue
			}
			for _, sceneInfo := range sceneInfos {
				airportId := *sceneInfo.AirportId
				if airportId == "" {
					continue
				}

				sceneId = cast.ToInt32(airportId)
				return
			}
		}
	}

	//判断是否是送机场景
	if len(destFenceInfo) > 0 {
		for _, fenceInfo := range destFenceInfo {
			if checkOrderTag(ctx, *fenceInfo.Tag, []string{"tag_airport"}) {
				sceneType = AIRPORT_TYPE_TO
			}

			sceneInfos := fenceInfo.SceneInfo
			if len(sceneInfos) == 0 {
				continue
			}
			for _, sceneInfo := range sceneInfos {
				sceneAirportId := *sceneInfo.AirportId
				if sceneAirportId == "" {
					continue
				}

				sceneId = cast.ToInt32(sceneAirportId)
				return
			}
		}
	}

	return
}

// GetBatchStationInfo 批量获取站点信息
func GetBatchStationInfo(ctx context.Context, req *Prfs.PGetStationInfoBatchReq) (rep *Prfs.PGetStationInfoBatchResp) {
	rsp, err := client.PGetStationInfoBatch(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs batch station info with err %v", err)
		return nil
	}
	return rsp
}

func GetCommonRouteDetail(ctx context.Context, req *Prfs.CommonRouteDetailReq) (resp *Prfs.CommonRouteDetailResp) {
	resp, err := client.CommonRouteDetail(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get prfs CommonRouteDetail with err %v", err)
		return nil
	}
	return resp
}
