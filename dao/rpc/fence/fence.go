package fence

import (
	"context"
	"errors"

	geofence "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Geofence"
	"git.xiaojukeji.com/gobiz/idgen"
	GeoFenceNewClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/geofenceClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

var (
	client *GeoFenceNewClient.Client
)

const (
	// TODO 重新申请
	TOKEN = "A64DD758E70D9D477CB3011CB9FA8772"
)

func Init() (err error) {
	client, err = GeoFenceNewClient.NewGeofenceClient("disf!engine-gs-geofence")
	return
}

func MultiInFence(ctx context.Context, cd []*geofence.Coordinate, groupIDs []int32, mapType int) (map[int32][]*geofence.FenceBean, error) {
	var ct geofence.CoordType
	ct = geofence.CoordType(mapType)
	req := &geofence.MultiInfenceRequest{
		LogID:     idgen.GetLogID(ctx),
		Token:     TOKEN,
		GroupIds:  groupIDs,
		CoordType: &ct,
		Coords:    cd,
	}
	ret, err := client.MultiInfence(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "_msg=fence err:%v", err)
		return nil, err
	}
	log.Trace.Infof(ctx, trace.DLTagUndefined, "_msg=fence req %v, ret:%+v", req, ret)
	if ret.RetCode != 10000 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "_msg=fence failed:%+v", ret)
		return nil, errors.New("fence ret failed")
	}
	return ret.Coords, nil
}
