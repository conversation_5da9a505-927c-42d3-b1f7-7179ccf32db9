module git.xiaojukeji.com/gulfstream/mamba

go 1.22

require (
	git.xiaojukeji.com/dirpc/dirpc-go-PriceApi v1.0.213
	git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2 v2.6.0
	git.xiaojukeji.com/dirpc/dirpc-go-http-Adx v1.1.101
	git.xiaojukeji.com/dirpc/dirpc-go-http-AthenaApiHttp v1.0.63
	git.xiaojukeji.com/dirpc/dirpc-go-http-Baichuan v1.9.23
	git.xiaojukeji.com/dirpc/dirpc-go-http-Brick v1.0.40
	git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool v2.1.81+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel v1.1.10
	git.xiaojukeji.com/dirpc/dirpc-go-http-CommonPlatCoupon v1.0.31
	git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation v1.0.53
	git.xiaojukeji.com/dirpc/dirpc-go-http-DaijiaKopService v1.0.5
	git.xiaojukeji.com/dirpc/dirpc-go-http-DitagService v1.0.1
	git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService v1.1.2
	git.xiaojukeji.com/dirpc/dirpc-go-http-Dos v1.0.226
	git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision v1.1.41
	git.xiaojukeji.com/dirpc/dirpc-go-http-Feeds v1.0.13
	git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari v1.0.16-0.20250325023137-57be9bd7b00d
	git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember v10.0.0+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-Guardian v1.0.7
	git.xiaojukeji.com/dirpc/dirpc-go-http-Horae v1.0.25
	git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun v1.0.21
	git.xiaojukeji.com/dirpc/dirpc-go-http-Insurance v2.0.26+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-Kronos v3.2.2+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-Locsvr v2.0.12+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-Mamba v1.0.145
	git.xiaojukeji.com/dirpc/dirpc-go-http-Member v1.0.38
	git.xiaojukeji.com/dirpc/dirpc-go-http-NewtonCommon v1.1.76
	git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi v1.1.21
	git.xiaojukeji.com/dirpc/dirpc-go-http-Passport v2.0.52+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-PetTripApi v1.0.1
	git.xiaojukeji.com/dirpc/dirpc-go-http-Pios v1.1.3
	git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus v2.3.116+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-http-PopePAPService v1.0.34
	git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs v1.0.90
	git.xiaojukeji.com/dirpc/dirpc-go-http-Sps v1.0.76
	git.xiaojukeji.com/dirpc/dirpc-go-http-Ssse v1.3.13
	git.xiaojukeji.com/dirpc/dirpc-go-http-TicketApi v1.0.34
	git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice v1.0.6
	git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind v1.0.53
	git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger v1.4.51
	git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassengerGo v1.1.37
	git.xiaojukeji.com/dirpc/dirpc-go-http-Uranus v1.3.17
	git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard v1.1.8
	git.xiaojukeji.com/dirpc/dirpc-go-ofs v1.0.1
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3 v2.1.34-0.20250606025507-30a24d31c4cb+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi v1.9.52
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi v1.1.37
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-DapeDynamicPrice v0.1.20
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-GCS v1.0.1
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-Geofence v2.1.1+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-HestiaCharge v1.1.14
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-Hotspot v1.2.0
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr v1.0.7
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-RouteBrokerService v2.0.0+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS v0.1.9
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-dfs v0.1.2
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift v0.0.70
	git.xiaojukeji.com/dukang/property-const-go-sdk v1.6.83
	git.xiaojukeji.com/engine/gaia-sdk v0.3.0
	git.xiaojukeji.com/gobiz/ctxutil v1.2.1
	git.xiaojukeji.com/gobiz/idgen v0.0.4
	git.xiaojukeji.com/gobiz/logger v1.5.7
	git.xiaojukeji.com/gobiz/metrics v1.0.3
	git.xiaojukeji.com/gulfstream/beatles-common v0.8.3
	git.xiaojukeji.com/gulfstream/biz-common-go v6.0.10+incompatible
	git.xiaojukeji.com/gulfstream/biz-common-go/v6 v6.13.0
	git.xiaojukeji.com/gulfstream/biz-lib-go v2.0.2-0.20180808050348-b55ca813fd50+incompatible
	git.xiaojukeji.com/gulfstream/bizlib-go v1.90.2
	git.xiaojukeji.com/gulfstream/bizredis v1.0.13
	git.xiaojukeji.com/gulfstream/bronze-door-sdk-go v0.6.3
	git.xiaojukeji.com/gulfstream/carpool-communication-go v0.4.1
	git.xiaojukeji.com/gulfstream/disf-idl v0.0.0-20231011081455-7d017d681be2
	git.xiaojukeji.com/gulfstream/orderid-util v1.0.1
	git.xiaojukeji.com/gulfstream/passenger-common v1.91.1
	git.xiaojukeji.com/gulfstream/tripcloud-common-go v1.52.0
	git.xiaojukeji.com/intercity/biz-api/intercity-common-go v1.29.0
	git.xiaojukeji.com/lego/context-go v3.3.11+incompatible
	git.xiaojukeji.com/lego/diconf-sdk-golang-v2 v1.0.10
	git.xiaojukeji.com/lego/dirpc-go v1.25.7
	git.xiaojukeji.com/nuwa/binding v0.1.4
	git.xiaojukeji.com/nuwa/go-monitor v1.1.7
	git.xiaojukeji.com/nuwa/golibs/ballast v1.0.4
	git.xiaojukeji.com/nuwa/golibs/goutils v0.0.5
	git.xiaojukeji.com/nuwa/golibs/httpserver v0.3.5
	git.xiaojukeji.com/nuwa/golibs/json v0.1.0
	git.xiaojukeji.com/nuwa/golibs/knife v0.0.3
	git.xiaojukeji.com/nuwa/golibs/metrics v0.3.2
	git.xiaojukeji.com/nuwa/golibs/redis v0.7.7
	git.xiaojukeji.com/nuwa/golibs/rpcserver/v2 v2.2.0
	git.xiaojukeji.com/nuwa/golibs/zerolog v1.3.26
	git.xiaojukeji.com/nuwa/gorm v0.1.10
	git.xiaojukeji.com/nuwa/protoc-dirpc v1.3.1
	git.xiaojukeji.com/nuwa/trace v1.3.13-0.20210309065643-d800e50a4abe
	git.xiaojukeji.com/nuwa/trace/v2 v2.0.7
	git.xiaojukeji.com/s3e/pts v0.3.1
	git.xiaojukeji.com/s3e/pts/v2 v2.4.2
	git.xiaojukeji.com/s3e/x-engine v1.12.0
	github.com/agiledragon/gomonkey/v2 v2.13.0
	github.com/bitly/go-simplejson v0.5.1
	github.com/bytedance/mockey v1.2.14
	github.com/golang/mock v1.6.0
	github.com/google/go-cmp v0.6.0
	github.com/grpc-ecosystem/grpc-gateway v1.16.0
	github.com/json-iterator/go v1.1.12
	github.com/mwitkow/go-proto-validators v0.3.2
	github.com/orcaman/concurrent-map v1.0.0
	github.com/samber/lo v1.50.0
	github.com/shopspring/decimal v1.3.1
	github.com/smartystreets/goconvey v1.8.1
	github.com/spf13/cast v1.7.1
	github.com/spf13/viper v1.16.0
	github.com/stretchr/testify v1.10.0
	github.com/thoas/go-funk v0.9.1
	github.com/tidwall/gjson v1.18.0
	go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2 v2.7.7+incompatible
	go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2 v2.9.0
	go.intra.xiaojukeji.com/foundation/carrera-go-sdk v2.1.21+incompatible
	golang.org/x/net v0.37.0
	golang.org/x/sync v0.11.0
	google.golang.org/grpc v1.69.4
	google.golang.org/protobuf v1.36.2
	gopkg.in/yaml.v2 v2.4.0
)

require (
	dario.cat/mergo v1.0.1 // indirect
	git.xiaojukeji.com/devops/statsd v0.3.0 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-Locsvr v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-UfsThrift v1.0.7 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-UserCenter v2.10.0+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-AxbV2 v4.1.7+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-BaocheTurbo v1.0.6 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-BeatlesRoute v1.1.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-BizTransaction v1.0.33 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Bubble v0.0.0-20230704080137-d3f3189c9bc9 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-CarPartnerCenterSdk v1.0.3 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-CarpoolGo v1.0.1 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-CashierTradecenterApi v1.0.25 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-CedarSFCService v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-CommonplatCashier v2.6.1+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-ConsecSFCService v1.0.12 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Coupon v1.1.21 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Dlock v2.0.0+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Dmc v3.1.0+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Doraemon v1.0.17 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-DriverBill v1.0.38 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-DriverStatus v1.1.3 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-EndowmentAuthService v1.1.7 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-FinanceDdpayUsercenter v1.0.15 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Gauss v1.0.52 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-GeofenceUpdater v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-GsBeatlesInvitation v1.1.13 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-GsBeatlesRoute v1.1.19 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-HermesApi v1.0.45 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Hertz v1.0.30 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Iapetos v2.0.45+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-IhapAcceptor v1.1.9 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-IhapDg v0.0.39 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-InnerBroker v2.3.0+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-IntercityForas v1.0.19 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Jackpot v1.0.94 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Kcard v3.3.80+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-MapPointSysAPI v1.1.29 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-NightWatch5 v1.0.66 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Performance v1.0.16 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-PicassoJudge v3.0.6+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Prepay v0.1.0 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Robin v1.0.2 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-TagServiceHttp v1.0.6 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Themis v2.0.28+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-TransactionGo v1.1.8 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudDriver v1.0.14 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-TripcloudDriverGo v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Unicorn v2.0.2+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-VOIPSERVER v0.0.2 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-Vam v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-WebappApi v2.28.5+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-WebappEnterprise v1.3.39 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-WycIntercityBusServiceShift v1.0.0 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-dcmp v0.1.0 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-nereus v1.1.1 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-http-phoenix v2.0.11+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-https-SfcTripCloudHaLuo v1.0.4 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudCommon v1.2.113 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudGuangqi v0.3.16 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudShouqi v1.3.24 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudXiangdao v0.0.21 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolFeature v1.0.14 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-DidiRgeo v1.0.2 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-DuseApi v2.3.37+incompatible // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-MapPickup v0.0.0-20220414035345-824aac776181 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-MartiniProxy v1.3.19 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-OrderRouteFeature v1.1.20 // indirect
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsService v1.0.1 // indirect
	git.xiaojukeji.com/disf/disf-go-spl-v2 v0.1.58 // indirect
	git.xiaojukeji.com/disfv4/commlib v0.0.0-20240920060152-f02e93b0a85e // indirect
	git.xiaojukeji.com/dukang/go-framework v1.1.0 // indirect
	git.xiaojukeji.com/dukang/product-definition-go-sdk v0.1.0 // indirect
	git.xiaojukeji.com/foundation/thrift v0.9.3 // indirect
	git.xiaojukeji.com/gobiz/config v0.0.10 // indirect
	git.xiaojukeji.com/gobiz/discovery v1.3.7 // indirect
	git.xiaojukeji.com/gobiz/utils v1.0.1 // indirect
	git.xiaojukeji.com/golang/dlog v1.1.1 // indirect
	git.xiaojukeji.com/gulfstream/car-partner-center-go-sdk v0.3.1 // indirect
	git.xiaojukeji.com/gulfstream/ddmq-schema v1.18.5 // indirect
	git.xiaojukeji.com/lego/common-go v0.1.28 // indirect
	git.xiaojukeji.com/lego/ruleEngine v1.0.0 // indirect
	git.xiaojukeji.com/lego/sentinel-golang v1.1.3 // indirect
	git.xiaojukeji.com/lego/thrift16 v1.0.2 // indirect
	git.xiaojukeji.com/nuwa/golibs/apcu v1.0.6 // indirect
	git.xiaojukeji.com/nuwa/golibs/apolloutil v0.0.4 // indirect
	git.xiaojukeji.com/nuwa/golibs/dcmp v1.5.0 // indirect
	git.xiaojukeji.com/nuwa/golibs/ddmq v0.0.9 // indirect
	git.xiaojukeji.com/nuwa/golibs/discover v0.0.7 // indirect
	git.xiaojukeji.com/nuwa/golibs/dynamicConf v0.0.6 // indirect
	git.xiaojukeji.com/nuwa/golibs/go-php-serialize v0.0.0-20210713062530-8cc595d53811 // indirect
	git.xiaojukeji.com/nuwa/golibs/httprouter v0.0.11 // indirect
	git.xiaojukeji.com/nuwa/golibs/masking v0.0.18 // indirect
	git.xiaojukeji.com/nuwa/golibs/mockutil v0.0.6 // indirect
	git.xiaojukeji.com/nuwa/golibs/queue v0.0.4 // indirect
	git.xiaojukeji.com/nuwa/golibs/redigo v1.8.9 // indirect
	git.xiaojukeji.com/observe-trace/didi-open-telemetry/opentelemetry-go v1.0.0 // indirect
	git.xiaojukeji.com/pearls/tlog v0.3.0 // indirect
	git.xiaojukeji.com/platform-ha/nodemgr-go v1.0.0 // indirect
	git.xiaojukeji.com/pt-arch/warden-client-go v0.0.2 // indirect
	git.xiaojukeji.com/s3e/common-lib v0.6.9 // indirect
	git.xiaojukeji.com/s3e/djson v0.0.2 // indirect
	git.xiaojukeji.com/sec-public/didi-go-agent-sdk-kms v0.0.1-202110132209-67b7ce9c // indirect
	git.xiaojukeji.com/sec-public/didi-go-sdk-kms v0.0.23-202504251227-e84a43c5 // indirect
	git.xiaojukeji.com/sec-public/didi-go-sdk-kms-disf v0.0.4-202506091620-7ce53ebd // indirect
	git.xiaojukeji.com/sec-public/qiankunbag-gmsm v0.0.2-20211227151920-45c2886d // indirect
	git.xiaojukeji.com/shield-arch/gcslib v0.0.2 // indirect
	git.xiaojukeji.com/sre-ha/chaos-interceptor v0.0.18 // indirect
	git.xiaojukeji.com/sre-ha/chaos-sdk-go v0.1.0 // indirect
	github.com/BurntSushi/toml v1.3.2 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.13.3 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/caio/go-tdigest v3.1.0+incompatible // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/coocood/freecache v1.2.3 // indirect
	github.com/dave/jennifer v1.6.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fogleman/gg v1.3.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-playground/locales v0.14.0 // indirect
	github.com/go-playground/universal-translator v0.18.0 // indirect
	github.com/go-playground/validator/v10 v10.11.1 // indirect
	github.com/go-resty/resty/v2 v2.16.2 // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/groupcache v0.0.0-20241129210726-2c02b8208cf8 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 // indirect
	github.com/hashicorp/go-version v1.6.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/iancoleman/orderedmap v0.3.0 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/cpuid/v2 v2.2.9 // indirect
	github.com/labstack/echo v3.3.10+incompatible // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/lufia/plan9stats v0.0.0-20230326075908-cb1d2100619a // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/moul/http2curl v1.0.0 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/parnurzeal/gorequest v0.3.0 // indirect
	github.com/pelletier/go-toml/v2 v2.0.8 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b // indirect
	github.com/prometheus/client_golang v1.20.5 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.61.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20181016184325-3113b8401b8a // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/shirou/gopsutil/v3 v3.24.5 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spatial-go/geoos v1.1.3 // indirect
	github.com/spf13/afero v1.9.5 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.4.2 // indirect
	github.com/syyongx/php2go v0.9.8 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tidwall/sjson v1.2.5 // indirect
	github.com/tklauser/go-sysconf v0.3.14 // indirect
	github.com/tklauser/numcpus v0.9.0 // indirect
	github.com/toolkits/net v0.0.0-20160910085801-3f39ab6fe3ce // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/unknwon/com v1.0.1 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/wI2L/jsondiff v0.6.1 // indirect
	github.com/yalp/jsonpath v0.0.0-20180802001716-5cc68e5049a0 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	go.intra.xiaojukeji.com/golang/commons v0.0.0-20171110122709-951813e61e9a // indirect
	go.intra.xiaojukeji.com/golang/go.uuid v1.1.0 // indirect
	go.intra.xiaojukeji.com/golang/logrus v1.0.1 // indirect
	go.intra.xiaojukeji.com/golang/lumberjack v0.0.0-20180103065020-539cf067394a // indirect
	go.intra.xiaojukeji.com/golang/thrift-lib v0.0.0-20220901073727-44ee33942dcb // indirect
	go.intra.xiaojukeji.com/platform-ha/onekey-degrade_sdk_go/v3 v3.2.13 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/arch v0.14.0 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/image v0.9.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	google.golang.org/genproto v0.0.0-20230410155749-daa745c078e1 // indirect
	gopkg.in/go-playground/validator.v8 v8.18.2 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/validator.v2 v2.0.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace (
	git.xiaojukeji.com/dirpc/dirpc-go-http-Member => git.xiaojukeji.com/dirpc/dirpc-go-http-Member v1.0.25
	git.xiaojukeji.com/dirpc/dirpc-go-http-ScorpioService => git.xiaojukeji.com/dirpc/dirpc-go-http-ScorpioService v2.0.6+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudShouqi => git.xiaojukeji.com/dirpc/dirpc-go-https-TripCloudShouqi v1.3.24
	//git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi => git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi v1.9.32
	//git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi => git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi v1.1.30
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-DuseApi => git.xiaojukeji.com/dirpc/dirpc-go-thrift-DuseApi v2.3.16+incompatible
	git.xiaojukeji.com/dirpc/dirpc-go-thrift-HestiaCharge => git.xiaojukeji.com/dirpc/dirpc-go-thrift-HestiaCharge v1.1.14
	//git.xiaojukeji.com/gulfstream/beatles-common => git.xiaojukeji.com/gulfstream/beatles-common v0.6.8
	git.xiaojukeji.com/nuwa/golibs/ddmq => git.xiaojukeji.com/nuwa/golibs/ddmq v0.0.9
	github.com/Sirupsen/logrus => github.com/sirupsen/logrus v1.8.1
	golang.org/x/net => golang.org/x/net v0.0.0-20211020060615-d418f374d309
	google.golang.org/grpc => google.golang.org/grpc v1.33.2
)
