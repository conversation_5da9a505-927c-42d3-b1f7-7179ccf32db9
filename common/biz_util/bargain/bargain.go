package bargain

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	UpLimit     = "999"
	LowLimit    = "2"
	LowLowLimit = "1"
)

type FeeMarginFloat64 struct {
	Val float64
}

func (f *FeeMarginFloat64) Float64() float64 {
	return f.Val
}

func (f *FeeMarginFloat64) UnmarshalJSON(data []byte) error {
	var (
		ff  float64
		err error
	)
	s := strings.TrimFunc(string(data), func(r rune) bool {
		if r == '"' || r == '\'' {
			return true
		}
		return false
	})
	if ff, err = strconv.ParseFloat(s, 64); err != nil {
		return err
	}
	f.Val = ff
	return nil
}

type FeeMargin struct {
	CityID                int              `json:"city_id"`
	Price                 FeeMarginFloat64 `json:"price"`
	CeilTimes             FeeMarginFloat64 `json:"ceil_times"`
	CeilMaxPrice          FeeMarginFloat64 `json:"ceil_max_price"`
	Floor1Times           FeeMarginFloat64 `json:"floor1_times"`
	Floor2TimesCPM        FeeMarginFloat64 `json:"floor2_times_cpm"`
	Floor2TimesStartPrice FeeMarginFloat64 `json:"floor2_times_start_price"`
	CeilFunctionPrice     string           `json:"ceil_function_price"` // 1 表示取最大值 2 表示取最小值
	AnyCarCeilTimes       FeeMarginFloat64 `json:"anycar_ceil_times"`
	AnyCarCeilMaxPrice    FeeMarginFloat64 `json:"anycar_ceil_max_price"`
	AnyCarFloorTimes      FeeMarginFloat64 `json:"anycar_floor_times"`
}

// 定价引擎的推荐信息
type RecommendInfo struct {
	BubbleInfo    *RecommendPriceInfo `json:"bubble_info"`     //2.0进入独立页的推荐价和上下限信息
	WaitRepayInfo *RecommendPriceInfo `json:"wait_repay_info"` //等待应答的推荐价和上下限信息
	FastCarFee    *float64            `json:"fast_car_fee"`    //快车券前价
}

type RecommendPriceInfo struct {
	RecommendPrice  []*RecommendPrice `json:"recommend_price"`
	UpperLimit      float64           `json:"upper_limit"`
	LowerLimit      float64           `json:"lower_limit"`
	LowerLowerLimit float64           `json:"lower_lower_limit"`
	RecommendStatus int               `json:"recommend_status"`
}
type RecommendPrice struct {
	Price float64 `json:"price"`
}

// 2.0的上限
func NewCalcCeil(ctx context.Context, extraMap map[string]interface{}) (item *proto.BargainFeeMarginItem) {
	info := GetRecommendInfo(extraMap)
	item = new(proto.BargainFeeMarginItem)
	//兜底使用固定值
	item.Amount = UpLimit
	if info != nil && info.BubbleInfo != nil {
		item.Amount = util.FormatPriceForRound(info.BubbleInfo.UpperLimit, 1)
	}
	item.Notice = dcmp.GetJSONContentWithPath(ctx, "bargain-fee_margin_text", nil, "ceil")
	return
}

// 2.0的下限、下下限
func NewCalcFloor(ctx context.Context, extraMap map[string]interface{}) (f1, f2 *proto.BargainFeeMarginItem) {
	f1, f2 = new(proto.BargainFeeMarginItem), new(proto.BargainFeeMarginItem)
	info := GetRecommendInfo(extraMap)
	//兜底使用固定值
	f1.Amount = LowLimit
	if info != nil && info.BubbleInfo != nil {
		f1.Amount = util.FormatPriceForRound(info.BubbleInfo.LowerLimit, 1)
	}
	f1.Notice = dcmp.GetJSONContentWithPath(ctx, "bargain-fee_margin_text", nil, "floor1")
	f2.Amount = LowLowLimit
	if info != nil && info.BubbleInfo != nil {
		f2.Amount = util.FormatPriceForRound(info.BubbleInfo.LowerLowerLimit, 1)
	}
	f2.Notice = dcmp.GetJSONContentWithPath(ctx, "bargain-fee_margin_text", nil, "floor2")
	return
}
func CalcAnycarCeil(extraMap map[string]interface{}) (item *proto.BargainFeeMarginItem) {
	info := GetRecommendInfo(extraMap)
	item = new(proto.BargainFeeMarginItem)
	//兜底使用固定值
	item.Amount = UpLimit
	if info != nil && info.WaitRepayInfo != nil {
		item.Amount = util.FormatPriceForRound(info.WaitRepayInfo.UpperLimit, 1)
	}
	return
}

func CalcAnyCarFloor(extraMap map[string]interface{}) (item *proto.BargainFeeMarginItem) {
	info := GetRecommendInfo(extraMap)
	item = new(proto.BargainFeeMarginItem)
	//兜底使用固定值
	item.Amount = LowLimit
	if info != nil && info.WaitRepayInfo != nil {
		item.Amount = util.FormatPriceForRound(info.WaitRepayInfo.LowerLimit, 1)
	}
	return
}
func CalcAnyCarLowerFloor(extraMap map[string]interface{}) (item *proto.BargainFeeMarginItem) {
	info := GetRecommendInfo(extraMap)
	item = new(proto.BargainFeeMarginItem)
	//兜底使用固定值
	item.Amount = LowLowLimit
	if info != nil && info.WaitRepayInfo != nil {
		item.Amount = util.FormatPriceForRound(info.WaitRepayInfo.LowerLowerLimit, 1)
	}
	return
}

// 获取推荐价信息
func GetRecommendInfo(extraMap map[string]interface{}) (info *RecommendInfo) {
	recommendInfo := &RecommendInfo{}
	if extraMap != nil && len(fmt.Sprint(extraMap["argue_car_info"])) > 0 {
		err := json.Unmarshal([]byte(fmt.Sprint(extraMap["argue_car_info"])), recommendInfo)
		if err != nil {
			return nil
		}
	}
	return recommendInfo
}
