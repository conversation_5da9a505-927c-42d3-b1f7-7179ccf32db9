package group_id

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewGroupId(t *testing.T) {
	t.Run("valid_group_id_with_two_parts", func(t *testing.T) {
		// 测试正常的groupId格式 "type_subid"
		groupId := NewGroupId("2_123")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")
		assert.Equal(t, int32(2), groupId.GroupType, "GroupType应该为2")
		assert.Equal(t, int32(123), groupId.SubGroupID, "SubGroupID应该为123")
	})

	t.Run("valid_group_id_with_zero_values", func(t *testing.T) {
		// 测试包含0值的groupId
		groupId := NewGroupId("0_0")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")
		assert.Equal(t, int32(0), groupId.GroupType, "GroupType应该为0")
		assert.Equal(t, int32(0), groupId.SubGroupID, "SubGroupID应该为0")
	})

	t.Run("valid_group_id_with_negative_values", func(t *testing.T) {
		// 测试包含负数的groupId（cast.ToInt32会处理）
		groupId := NewGroupId("-1_-2")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")
		assert.Equal(t, int32(-1), groupId.GroupType, "GroupType应该为-1")
		assert.Equal(t, int32(-2), groupId.SubGroupID, "SubGroupID应该为-2")
	})

	t.Run("invalid_group_id_empty_string", func(t *testing.T) {
		// 测试空字符串
		groupId := NewGroupId("")
		assert.Nil(t, groupId, "空字符串应该返回nil")
	})

	t.Run("invalid_group_id_single_part", func(t *testing.T) {
		// 测试只有一个部分的groupId
		groupId := NewGroupId("123")
		assert.Nil(t, groupId, "单个部分的groupId应该返回nil")
	})

	t.Run("invalid_group_id_three_parts", func(t *testing.T) {
		// 测试有三个部分的groupId
		groupId := NewGroupId("1_2_3")
		assert.Nil(t, groupId, "三个部分的groupId应该返回nil")
	})

	t.Run("invalid_group_id_no_underscore", func(t *testing.T) {
		// 测试没有下划线的groupId
		groupId := NewGroupId("123456")
		assert.Nil(t, groupId, "没有下划线的groupId应该返回nil")
	})

	t.Run("valid_group_id_with_non_numeric_values", func(t *testing.T) {
		// 测试非数字值（cast.ToInt32会转换为0）
		groupId := NewGroupId("abc_def")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")
		assert.Equal(t, int32(0), groupId.GroupType, "非数字GroupType应该转换为0")
		assert.Equal(t, int32(0), groupId.SubGroupID, "非数字SubGroupID应该转换为0")
	})
}

func TestGroupId_GetSubGroupIdByGroupId(t *testing.T) {
	t.Run("aggregation_box_type_returns_sub_group_id", func(t *testing.T) {
		// 测试AggregationBoxType类型返回SubGroupID
		groupId := &GroupId{
			GroupType:  AggregationBoxType,
			SubGroupID: 456,
		}
		result := groupId.GetSubGroupIdByGroupId()
		assert.NotNil(t, result, "AggregationBoxType应该返回非nil的SubGroupID指针")
		assert.Equal(t, int32(456), *result, "返回的SubGroupID值应该正确")
	})

	t.Run("single_type_returns_nil", func(t *testing.T) {
		// 测试SINGLETYPE类型返回nil
		groupId := &GroupId{
			GroupType:  SINGLETYPE,
			SubGroupID: 789,
		}
		result := groupId.GetSubGroupIdByGroupId()
		assert.Nil(t, result, "SINGLETYPE应该返回nil")
	})

	t.Run("flow_box_type_returns_nil", func(t *testing.T) {
		// 测试FLOWBOXType类型返回nil
		groupId := &GroupId{
			GroupType:  FLOWBOXType,
			SubGroupID: 101,
		}
		result := groupId.GetSubGroupIdByGroupId()
		assert.Nil(t, result, "FLOWBOXType应该返回nil")
	})

	t.Run("unknown_type_returns_nil", func(t *testing.T) {
		// 测试未知类型返回nil
		groupId := &GroupId{
			GroupType:  999,
			SubGroupID: 202,
		}
		result := groupId.GetSubGroupIdByGroupId()
		assert.Nil(t, result, "未知GroupType应该返回nil")
	})

	t.Run("aggregation_box_type_with_zero_sub_group_id", func(t *testing.T) {
		// 测试AggregationBoxType类型但SubGroupID为0的情况
		groupId := &GroupId{
			GroupType:  AggregationBoxType,
			SubGroupID: 0,
		}
		result := groupId.GetSubGroupIdByGroupId()
		assert.NotNil(t, result, "AggregationBoxType应该返回非nil的SubGroupID指针，即使值为0")
		assert.Equal(t, int32(0), *result, "返回的SubGroupID值应该为0")
	})
}

func TestGroupId_IsFlowBoxByGroupId(t *testing.T) {
	t.Run("flow_box_type_returns_true", func(t *testing.T) {
		// 测试FLOWBOXType返回true
		groupId := &GroupId{
			GroupType:  FLOWBOXType,
			SubGroupID: 123,
		}
		result := groupId.IsFlowBoxByGroupId()
		assert.True(t, result, "FLOWBOXType应该返回true")
	})

	t.Run("single_type_returns_false", func(t *testing.T) {
		// 测试SINGLETYPE返回false
		groupId := &GroupId{
			GroupType:  SINGLETYPE,
			SubGroupID: 456,
		}
		result := groupId.IsFlowBoxByGroupId()
		assert.False(t, result, "SINGLETYPE应该返回false")
	})

	t.Run("aggregation_box_type_returns_false", func(t *testing.T) {
		// 测试AggregationBoxType返回false
		groupId := &GroupId{
			GroupType:  AggregationBoxType,
			SubGroupID: 789,
		}
		result := groupId.IsFlowBoxByGroupId()
		assert.False(t, result, "AggregationBoxType应该返回false")
	})

	t.Run("unknown_type_returns_false", func(t *testing.T) {
		// 测试未知类型返回false
		groupId := &GroupId{
			GroupType:  999,
			SubGroupID: 101,
		}
		result := groupId.IsFlowBoxByGroupId()
		assert.False(t, result, "未知GroupType应该返回false")
	})
}

func TestConstants(t *testing.T) {
	t.Run("verify_constant_values", func(t *testing.T) {
		// 测试常量值的正确性
		assert.Equal(t, 0, SINGLETYPE, "SINGLETYPE常量应该为0")
		assert.Equal(t, 2, AggregationBoxType, "AggregationBoxType常量应该为2")
		assert.Equal(t, 5, FLOWBOXType, "FLOWBOXType常量应该为5")
	})
}

// 集成测试：测试完整的工作流程
func TestGroupIdIntegration(t *testing.T) {
	t.Run("complete_workflow_aggregation_box", func(t *testing.T) {
		// 测试AggregationBoxType的完整工作流程
		groupId := NewGroupId("2_888")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")

		subGroupId := groupId.GetSubGroupIdByGroupId()
		assert.NotNil(t, subGroupId, "AggregationBoxType应该返回SubGroupID")
		assert.Equal(t, int32(888), *subGroupId, "SubGroupID值应该正确")

		isFlowBox := groupId.IsFlowBoxByGroupId()
		assert.False(t, isFlowBox, "AggregationBoxType不应该是FlowBox")
	})

	t.Run("complete_workflow_flow_box", func(t *testing.T) {
		// 测试FLOWBOXType的完整工作流程
		groupId := NewGroupId("5_999")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")

		subGroupId := groupId.GetSubGroupIdByGroupId()
		assert.Nil(t, subGroupId, "FLOWBOXType应该返回nil SubGroupID")

		isFlowBox := groupId.IsFlowBoxByGroupId()
		assert.True(t, isFlowBox, "FLOWBOXType应该是FlowBox")
	})

	t.Run("complete_workflow_single_type", func(t *testing.T) {
		// 测试SINGLETYPE的完整工作流程
		groupId := NewGroupId("0_111")
		assert.NotNil(t, groupId, "应该成功创建GroupId对象")

		subGroupId := groupId.GetSubGroupIdByGroupId()
		assert.Nil(t, subGroupId, "SINGLETYPE应该返回nil SubGroupID")

		isFlowBox := groupId.IsFlowBoxByGroupId()
		assert.False(t, isFlowBox, "SINGLETYPE不应该是FlowBox")
	})
}
