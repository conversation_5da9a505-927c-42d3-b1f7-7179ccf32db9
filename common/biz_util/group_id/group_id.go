package group_id

import (
	"github.com/spf13/cast"
	"strings"
)

const (
	SINGLETYPE         = 0
	AggregationBoxType = 2
	FLOWBOXType        = 5
)

type GroupId struct {
	GroupType  int32
	SubGroupID int32
}

func NewGroupId(groupId string) *GroupId {
	split := strings.Split(groupId, "_")
	if len(split) != 2 {
		return nil
	}
	return &GroupId{
		GroupType:  cast.ToInt32(split[0]),
		SubGroupID: cast.ToInt32(split[1]),
	}
}

func (g *GroupId) GetSubGroupIdByGroupId() *int32 {
	if g.GroupType == AggregationBoxType {
		return &g.SubGroupID
	}

	return nil
}

func (g *GroupId) IsFlowBoxByGroupId() bool {
	return g.GroupType == FLOWBOXType
}
