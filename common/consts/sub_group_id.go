package consts

// athena  sub_group_id  ：   1:特惠三方聚合 2:出租车聚合 3:泛快TP盒子 4:出租车TP盒子 5:出租车计价盒子

const (
	SubGroupIdSingleCar        = 0 // SubGroupIdShortDistance 短途特惠,第三方
	SubGroupIdShortDistance    = 1
	SubGroupIdUnitaxi          = 2  // 出租车盒子
	SubGroupIdTp               = 3  //  TP
	SubGroupIdTaxiTp           = 4  //  出租车TP
	SubGroupIdTaxiPricingBox   = 5  //  出租车计价盒子
	SubGroupIdFarMustCheap     = 6  // 远必省
	SubGroupIdXDiscount        = 7  //  X折特价车
	SubGroupIdDiscountAlliance = 8  // 特惠联盟盒子 https://cooper.didichuxing.com/knowledge/2199455543792/2200197137724
	SubGroupIdChaoZhiDa        = 10 // 超值达（三方实时计价盒子）https://cooper.didichuxing.com/docs2/document/2202057999126
	SubGroupIdSurpriseDiscount = 11 // 惊喜特价盒子https://cooper.didichuxing.com/knowledge/2199626789935/2204653407141
	SubGroupIdHKThirtyNormal   = 21 // 香港-三方-经济型
	SubGroupIdHKThirtyComfort  = 22 // 香港-三方-舒适型
)
