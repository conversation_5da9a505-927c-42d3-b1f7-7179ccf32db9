package redis

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.xiaojukeji.com/gulfstream/bizredis"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	legoTrace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/golibs/redis"
)

var (
	redisClient         Client
	redisEstimateClient Client
	redisCarpoolClient  *bizredis.CarpoolRedisClient
	fusionClient        *redis.Manager
)

// Init 初始化redis
func Init() (err error) {
	var manager *redis.Manager

	// redis参数设置参考:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=521705579
	manager, err = redis.NewManager(conf.Viper.GetStringSlice("redis.addrs"),
		"",
		redis.SetPoolSize(conf.Viper.GetInt64("redis.pool_size")), // 连接池大小, 配置文件大小需要自行评估，重新设置
		redis.SetMaxConn(conf.Viper.GetInt64("redis.max_con")),    // 最多连接数, 配置文件大小需要自行评估，重新设置

		redis.SetConnectTimeout(conf.Viper.GetDuration("redis.conn_timeout")), // 连接超时时间, 配置文件大小需要自行评估，重新设置
		redis.SetReadTimeout(conf.Viper.GetDuration("redis.read_timeout")),    // 设置read超时, 配置文件大小需要自行评估，重新设置
		redis.SetWriteTimeout(conf.Viper.GetDuration("redis.write_timeout")),  // 设置write超时, 配置文件大小需要自行评估，重新设置

		redis.DisfSwitch(conf.Viper.GetBool("redis.disf_enable")), // 设置是否开启disf
		redis.DisfServiceName(conf.Viper.GetString("redis.disf")), // 设置disfName
		redis.SetWhitelistPassword(conf.Viper.GetString("redis.whitelist_password")),
	)

	if err != nil {
		log.Trace.Errorf(context.Background(), legoTrace.DLTagUndefined, "dial redis err %v", err)
	}

	redisClient = &clientWrapper{
		inner: manager,
	}

	return
}

// InitEstimateClient 初始化EstimateRedisClient
func InitEstimateClient() (err error) {
	var manager *redis.Manager

	// redis参数设置参考:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=521705579
	manager, err = redis.NewManager(conf.Viper.GetStringSlice("redis_estimate.addrs"),
		"",
		redis.SetPoolSize(conf.Viper.GetInt64("redis_estimate.pool_size")), // 连接池大小, 配置文件大小需要自行评估，重新设置
		redis.SetMaxConn(conf.Viper.GetInt64("redis_estimate.max_con")),    // 最多连接数, 配置文件大小需要自行评估，重新设置

		redis.SetConnectTimeout(conf.Viper.GetDuration("redis_estimate.conn_timeout")), // 连接超时时间, 配置文件大小需要自行评估，重新设置
		redis.SetReadTimeout(conf.Viper.GetDuration("redis_estimate.read_timeout")),    // 设置read超时, 配置文件大小需要自行评估，重新设置
		redis.SetWriteTimeout(conf.Viper.GetDuration("redis_estimate.write_timeout")),  // 设置write超时, 配置文件大小需要自行评估，重新设置

		redis.DisfSwitch(conf.Viper.GetBool("redis_estimate.disf_enable")), // 设置是否开启disf
		redis.DisfServiceName(conf.Viper.GetString("redis_estimate.disf")), // 设置disfName
		redis.SetWhitelistPassword(conf.Viper.GetString("redis_estimate.whitelist_password")),
	)

	if err != nil {
		log.Trace.Errorf(context.Background(), legoTrace.DLTagUndefined, "dial estimate redis client err %v", err)
	}

	redisEstimateClient = &clientWrapper{
		inner: manager,
	}

	return
}

func InitCarpoolRedisClient() (err error) {
	redisCarpoolClient, err = bizredis.NewCarpoolRedisClient(initPassengerRedisOne("carpool_core"), initPassengerRedisOne("carpool_other"))
	return
}

func InitFusionRedisClient() (err error) {
	fusionClient = initPassengerRedisOne("redis_fusion")
	return
}

func initPassengerRedisOne(name string) *redis.Manager {
	addrs := conf.Viper.GetStringSlice(name + ".addrs")
	connTimeout := time.Duration(conf.Viper.GetInt(name+".conn_timeout")) * time.Millisecond
	readTimeout := time.Duration(conf.Viper.GetInt(name+".read_timeout")) * time.Millisecond
	writeTimeout := time.Duration(conf.Viper.GetInt(name+".write_timeout")) * time.Millisecond
	connLiftTime := time.Duration(conf.Viper.GetInt(name+".max_conn_lifetime")) * time.Second

	// 先初始化golibs/redis，注意必须使用disf
	mgr, initErr := redis.NewManager(addrs,
		conf.Viper.GetString(""),
		redis.SetMaxConn(conf.Viper.GetInt64(name+".max_con")),    //最大连接上限
		redis.SetPoolSize(conf.Viper.GetInt64(name+".pool_size")), //连接池大小
		redis.SetConnectTimeout(connTimeout),
		redis.SetReadTimeout(readTimeout), // 读写超时
		redis.SetWriteTimeout(writeTimeout),
		redis.SetMaxConnLifetime(connLiftTime), //连接生存周期
		redis.DisfSwitch(true),                 // 使用disf 模式需要DisfSwitch true
		redis.SetMetricsSwitch(true),           //默认设置为true，不需要metric上报的请设置为false
		redis.DisfServiceName(conf.Viper.GetString(name+".service_name")),
		redis.SetUseDirpcTimeout(true),                           //默认false，如果设置成true，将能使用dirpc通道超时,可以使用disf通道设置超时
		redis.SetPingCheck(conf.Viper.GetBool(name+".isOnline")), //设置为true,会在初始化时执行ping操作,如果ping失败,初始化返回err;
		redis.SetWhitelistPassword(conf.Viper.GetString(name+".whitelist_password")),
		redis.SetLoggerSwitch(true),
	)

	if initErr != nil {
		fmt.Println(initErr)
		log.Trace.Errorf(context.Background(), legoTrace.DLTagUndefined, "NewPassengerRedisClient err:%v", initErr)
		os.Exit(1)
	}

	return mgr
}

type clientWrapper struct {
	inner *redis.Manager
}

func (wrapper *clientWrapper) SetEx(ctx context.Context, key string, expireTime time.Duration, val interface{}) (reply string, err error) {
	return wrapper.inner.SetEx(ctx, key, int(expireTime/time.Second), val)
}
func (wrapper *clientWrapper) SetNEx(ctx context.Context, key string, expireTime time.Duration, val interface{}) (reply string, err error) {
	return wrapper.inner.SetNEx(ctx, key, int(expireTime/time.Second), val)
}
func (wrapper *clientWrapper) Get(ctx context.Context, key string) (reply string, err error) {
	return wrapper.inner.Get(ctx, key)
}
func (wrapper *clientWrapper) Expire(ctx context.Context, key string, expireTime time.Duration) (reply int, err error) {
	return wrapper.inner.Expire(ctx, key, int64(expireTime/time.Second))
}

func (wrapper *clientWrapper) Del(ctx context.Context, key string) (int, error) {
	return wrapper.inner.Del(ctx, key)
}

// Client redis client
type Client interface {
	SetEx(ctx context.Context, key string, expireTime time.Duration, val interface{}) (reply string, err error)
	SetNEx(ctx context.Context, key string, expireTime time.Duration, val interface{}) (reply string, err error)
	Get(ctx context.Context, key string) (reply string, err error)
	Expire(ctx context.Context, key string, expireTime time.Duration) (reply int, err error)
	Del(ctx context.Context, key string) (int, error)
}

// GetClient 获取客户端 : 用的是老集群，新key不建议使用这个client
func GetClient() Client {
	return redisClient
}

// GetEstimateClient 获取预估redis客户端: 用的是预估独享的redis集群
func GetEstimateClient() Client {
	return redisEstimateClient
}

func GetCarpoolClient() *bizredis.CarpoolRedisClient {
	return redisCarpoolClient
}

func GetFusionClient() *redis.Manager {
	return fusionClient
}
