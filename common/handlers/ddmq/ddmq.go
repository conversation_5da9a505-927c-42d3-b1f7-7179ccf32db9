package ddmq

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"go.intra.xiaojukeji.com/foundation/carrera-go-sdk/producer/src/carrera/CarreraProducer"

	context2 "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"go.intra.xiaojukeji.com/foundation/carrera-go-sdk/common/csd"
	"go.intra.xiaojukeji.com/foundation/carrera-go-sdk/producer/src/carrera"
)

const LogTag = "_kafka_send_msg"
const TopicWanliuMultiEstimate = "wanliu_passenger_multi_estimate_req"

const TopicWanliuOrderEstimate = "wanliu_passenger_order_estimate"
const TopicWanliuPassengerEstimate = "wanliu_passenger_estimate"
const TopicTypeWanliuMultiEstimate = 21080
const PassengerRole = 2

var (
	producer *carrera.PooledProducer // producer 实例
	topics   = []string{TopicWanliuMultiEstimate, TopicWanliuPassengerEstimate, TopicWanliuOrderEstimate}
)
var (
	ErrEmptyTopic  = errors.New("empty topic")
	ErrMarshalFail = errors.New("marshal fail")
	ErrSendFail    = errors.New("send fail")

	errExtraMsgFromBuilder = errors.New("extra msg from builder failed")
)

/*
*
$aBody = [

	    'no'   => $iNo,
	    'type' => $iType,
	    'ct'   => time(),
	    'data' => $aMsg,
	];
*/
type Body struct {
	No   int32       `json:"no"`
	Type int32       `json:"type"`
	Ct   int64       `json:"ct"`
	Data interface{} `json:"data"`
}

func Init() {

	env := conf.Viper.GetString("ddmq.env")
	poolSize := conf.Viper.GetInt("ddmq.poolSize")
	proxyTimeout := conf.Viper.GetInt64("ddmq.proxyTimeout")
	clientTimeout := conf.Viper.GetInt64("ddmq.clientTimeout")
	clientRetry := conf.Viper.GetInt("ddmq.clientRetry")

	config := carrera.NewCsdCarreraConfig(getEnv(env), topics)
	// 连接池的连接数大小，如果连接都被占用，没有放回连接池的话，send会等待获取连接
	config.SetCarreraPoolSize(poolSize)
	// Proxy端处理请求的超时时间。写队列超时之后会尝试Cache。Cache成功后会返回CACHE_OK
	config.SetCarreraProxyTimeout(proxyTimeout)
	// client和proxy server的超时时间，一般不建议设太小。必须大于carreraProxyTimeout的值，建议设置2倍的比例
	config.SetCarreraClientTimeout(clientTimeout)
	// 客户端失败重试次数，总共发送n+1次
	config.SetCarreraClientRetry(clientRetry)

	producer = carrera.NewCarreraPooledProducer(config)
	producer.Start()
}

func getEnv(env string) csd.EnvType {
	switch env {
	case "test":
		return csd.ENV_TEST
	case "product":
		return csd.ENV_PRODUCT
	case "preview":
		return csd.ENV_PREVIEW
	default:
		return csd.ENV_TEST
	}
}

func Send(ctx context.Context, topic string, msg interface{}, hashId int64, topicType int32) error {
	if len(topic) <= 0 {
		return errors.New("empty topic")
	}
	var traceId string
	var isPressureTraffic bool
	traceId = context2.GetTrace(ctx).GetTraceId()
	isPressureTraffic = context2.GetTrace(ctx).GetHintCode() == "1"
	body := Body{
		No:   0,
		Type: topicType,
		Ct:   time.Now().Unix(),
		Data: msg,
	}
	bodyStr, err := json.Marshal(body)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrJsonMarshal, "ddmq msg json marshal fail with err %v", err)
		return errors.New("send msg fail")
	}

	ret := producer.MessageBuilder().SetTopic(topic).SetBody(bodyStr).SetHashId(hashId).SetTraceId(traceId).SetPressureTraffic(isPressureTraffic).Send()
	log.Trace.Infof(ctx, LogTag, "topic=%s||msg=%s||code=%d", topic, string(bodyStr), ret.GetCode())

	if ret.GetCode() <= carrera.CACHE_OK {
		return nil
	}

	return errors.New(ret.GetMsg())
}

type MessageBuilder = carrera.MessageBuilder
type MQOption func(*MessageBuilder)

func SendV2(ctx context.Context, topic string, msg string, opts ...MQOption) error {
	if topic == "" {
		return ErrEmptyTopic
	}

	var traceID string
	var isPressureTraffic bool
	if t := context2.GetTrace(ctx); t != nil {
		traceID = t.GetTraceId()
		isPressureTraffic = t.GetHintCode() == "1"
	}

	bd := producer.MessageBuilder().
		SetTopic(topic).                      // 设置topic, 必选
		SetBody(msg).                         // 设置消息体, 必选
		SetTraceId(traceID).                  // 设置trace-id, 必选
		SetPressureTraffic(isPressureTraffic) // 设置压测表示, 必选

	for _, opt := range opts {
		opt(bd) // 其他可选的参数
	}
	ret := bd.Send()
	if ret.GetCode() == carrera.CACHE_OK || ret.GetCode() == carrera.OK {
		return nil
	}

	return fmt.Errorf("%w %s", ErrSendFail, ret.GetMsg())
}

func BatchSendV2(ctx context.Context, topic string, msgs []string, opts ...MQOption) error {
	if topic == "" {
		return ErrEmptyTopic
	}

	var traceID string
	var isPressureTraffic bool
	if t := context2.GetTrace(ctx); t != nil {
		traceID = t.GetTraceId()
		isPressureTraffic = t.GetHintCode() == "1"
	}

	genMsg := func(body string) (*CarreraProducer.Message, error) {
		bd := producer.MessageBuilder().
			SetTopic(topic).
			SetBody(body).
			SetTraceId(traceID).
			SetPressureTraffic(isPressureTraffic)
		for _, opt := range opts {
			opt(bd) // 其他可选的参数
		}

		// msg := getMsgFromBuilderUnsafe(bd) //MAYBUG
		// if msg == nil {
		// 	return nil, errExtraMsgFromBuilder
		// }
		return bd.Build(), nil
	}

	batchMsg := make([]*CarreraProducer.Message, 0, len(msgs))
	for _, msg := range msgs {
		if wrapMsg, err := genMsg(msg); err != nil {
			log.Trace.Warnf(ctx, consts.TagErrDDmq, "%s", err)
		} else {
			batchMsg = append(batchMsg, wrapMsg)
		}
	}

	ret := producer.SendBatch(batchMsg)

	if ret.GetCode() == carrera.CACHE_OK || ret.GetCode() == carrera.OK {
		return nil
	}

	return fmt.Errorf("%w %s", ErrSendFail, ret.GetMsg())
}
