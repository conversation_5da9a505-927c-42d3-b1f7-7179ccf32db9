package util

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"math"
	"runtime"
	"strconv"
	"strings"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"

	"git.xiaojukeji.com/gobiz/ctxutil"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"github.com/shopspring/decimal"
)

const floatZero = 1e-9 // 精度范围

// 判断是否压测
func IsPressureTraffic(ctx context.Context) bool {
	val, err := ctxutil.GetHintCode(ctx)
	if err == nil && val == consts.PressureHintCode {
		return true
	}

	dtrace, ok := legoTrace.GetCtxTrace(ctx)
	if !ok {
		return false
	}

	return dtrace.IsPressureTraffic()
}

func Float64Sub(x, y float64) float64 {
	return decimal.NewFromFloat(x).Sub(decimal.NewFromFloat(y)).Round(2).InexactFloat64()
}

func Float64Add(x, y float64) float64 {
	return decimal.NewFromFloat(x).Add(decimal.NewFromFloat(y)).Round(2).InexactFloat64()
}

func IsVirtualTraffic(ctx context.Context) bool {
	_trace := legoTrace.GetTrace(ctx)
	hintContent := _trace.GetHintContent()
	if hintContent == "" {
		return false
	}
	var hintContentMap = make(map[string]interface{})
	err := json.Unmarshal([]byte(hintContent), &hintContentMap)
	if err != nil {
		return false
	}
	_, ok := hintContentMap["virtual"]
	return ok
}

var debugFlag = "[DEBUG]"

// DEBUG 调试
func DEBUG(i ...interface{}) {
	now := time.Now().Format("2006-01-02 15:04:05")
	_, file, line, _ := runtime.Caller(1)
	fmt.Printf("\n%s %s [BEGIN] @%s:%d \n", now, debugFlag, file, line) // nolint: forbidigo
	fmt.Printf("%s %s [DATA]:%+v ", now, debugFlag, i)                  // nolint: forbidigo
	fmt.Printf("\n%s %s [END]   @%s:%d \n", now, debugFlag, file, line) // nolint: forbidigo
}

// 根据uid换pid，低48位是pid
func GetPidByUid(uid uint64) uint64 {
	if uid == 0 {
		return 0
	}

	return uid & 0x0000FFFFFFFFFFFF
}

func String2int32(ctx context.Context, iStr string) int32 {
	if iStr == "" {
		return 0
	}
	i32, err := strconv.ParseInt(iStr, 10, 64)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "str2i32：%v", err)
	}
	return int32(i32)
}

func String2int64(ctx context.Context, iStr string) int64 {
	if iStr == "" {
		return 0
	}

	i64, err := strconv.ParseInt(iStr, 10, 64)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "str2i64：%v", err)
	}
	return i64
}

func String2Int(ctx context.Context, iStr string) int {
	if iStr == "" {
		return 0
	}

	i64, err := strconv.ParseInt(iStr, 10, 64)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "str2i64：%v", err)
	}
	return int(i64)
}

func DateStringToUnixTime(ctx context.Context, date string) int64 {
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")                      //重要：获取时区
	theTime, _ := time.ParseInLocation(timeLayout, date, loc) //使用模板在对应时区转化为time.time类型
	return theTime.Unix()
}

func String2float64(ctx context.Context, fStr string) float64 {
	if fStr == "" {
		return 0
	}

	f64, err := strconv.ParseFloat(fStr, 64)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "str2f64：%v", err)
	}
	return f64
}

// CompareAppVersion 比较版本号，1 > v2 返回1；v1 == v2 返回0；v1 < v2 返回-1
func CompareAppVersion(version1, version2 string) int {
	var (
		len1   int = len(version1)
		len2   int = len(version2)
		index1 int = 0
		index2 int = 0
		answer int = 0
		sum1   int
		sum2   int
	)

	for index1 < len1 || index2 < len2 {
		sum1 = 0
		for index1 < len1 && version1[index1] != '.' {
			sum1 = sum1*10 + int(version1[index1]-'0')
			index1++
		}

		sum2 = 0
		for index2 < len2 && version2[index2] != '.' {
			sum2 = sum2*10 + int(version2[index2]-'0')
			index2++
		}

		if sum1 != sum2 {
			if sum1 > sum2 {
				answer = 1
			} else {
				answer = -1
			}

			break
		}

		// 跳过"."
		index1++
		index2++
	}

	return answer
}

// debug打日志用
func JustJsonEncode(i interface{}) string {
	bytes, err := json.Marshal(i)
	if err != nil {
		return ""
	}
	return string(bytes)
}

func GetTraceIDFromCtxWithoutCheck(ctx context.Context) string {
	t := legoTrace.GetTrace(ctx)
	if t == nil {
		return ""
	}
	return t.GetTraceId()
}

func IntParseWithDefault(s string, d int64) int64 {
	i, e := strconv.ParseInt(s, 10, 64)
	if e != nil {
		return d
	}
	return i
}

func ListIntParseByJson(s string) []int64 {
	res := []int64{}
	_ = json.Unmarshal([]byte(s), &res)
	return res
}

// RemoveSuffixZero 去掉小数后的0
func RemoveSuffixZero(s string) string {
	if strings.Contains(s, ".") {
		for strings.HasSuffix(s, "0") {
			s = strings.TrimSuffix(s, "0")
		}
		s = strings.TrimSuffix(s, ".")
	}

	return s
}

// Float64Hundred ...
func Float64Hundred(f float64) int64 {
	tmp := fmt.Sprintf("%.2f", f)
	tmp = tmp[:len(tmp)-3] + tmp[len(tmp)-2:]
	tmp = strings.TrimLeft(tmp, "0")
	if tmp == "" {
		tmp = "0"
	}
	ret, err := strconv.ParseInt(tmp, 10, 64)
	if err != nil {
		log.Trace.Errorf(nil, trace.DLTagUndefined, "src=%v len=%d tmp=%s err:%v", f, len(fmt.Sprintf("%.2f", f)), tmp, err)
		return int64(f * 100)
	}
	return ret
}

func Jiao2YUan(i int32) string {
	d := decimal.New(1, 1)
	result := decimal.NewFromInt(int64(i)).DivRound(d, 4).String()
	return result
}

func Fen2Yuan(i int64) string {
	d := decimal.New(1, 2)
	return decimal.NewFromInt(i).DivRound(d, 4).String()
}

func MaxInt32(i int32, j int32) int32 {
	if i < j {
		return i
	}
	return j
}

func MinInt32(i int32, j int32) int32 {
	if i < j {
		return i
	}
	return j
}

func MaxInt64(i int64, j int64) int64 {
	if i > j {
		return i
	}
	return j
}

// 当天24点时间戳
func GetNowLastTimeStamp() int64 {
	year, month, day := time.Now().Date()
	local, _ := time.LoadLocation("Asia/Shanghai")
	return time.Date(year, month, day, 0, 0, 0, 0, local).Unix() + 24*60*60
}

// CoverStrByStart 给字符串加蒙层
func CoverStrByStart(originStr string, head int, tail int) string {
	headStr := ""
	tailStr := ""
	startStr := ""

	if head+tail > len(originStr) {
		return originStr
	}

	if head < len(originStr) {
		headStr = originStr[0:head]
	}

	if tail < len(originStr) {
		tailStr = originStr[len(originStr)-tail:]
	}

	startNum := len(originStr) - head - tail

	for i := 0; i < startNum; i++ {
		startStr += "*"
	}

	return headStr + startStr + tailStr
}

// 当天0点时间戳
func GetNowEarlyTimeStamp() int64 {
	year, month, day := time.Now().Date()
	local, _ := time.LoadLocation("Asia/Shanghai")
	return time.Date(year, month, day, 0, 0, 0, 0, local).Unix()
}

// 某天0点时间戳
func GetSomeDayEarlyTimeStamp(t int64) int64 {
	year, month, day := time.Unix(t, 0).Date()
	local, _ := time.LoadLocation("Asia/Shanghai")
	return time.Date(year, month, day, 0, 0, 0, 0, local).Unix()
}
func GetCurrentTimeStamp() int64 {
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	return time.Now().In(shanghaiLocation).Unix()
}

func RemoveDuplicates(nums []int64) []int64 {
	result := make([]int64, 0)
	seen := map[int64]bool{}

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}
	return result
}

// 字符串转义
func StringEscape(s string) string {
	return strings.ReplaceAll(s, "|", "\\|")
}

func IntArrHasIntersection(arr1 []int64, arr2 []int64) bool {
	arr1Set := make(map[int64]struct{}, len(arr1))

	for _, i := range arr1 {
		arr1Set[i] = struct{}{}
	}

	for _, i := range arr2 {
		if _, ok := arr1Set[i]; ok {
			return true
		}
	}

	return false
}

func CompareFloat64ByPrec(f1, f2 float64, prec int) int {
	f1Str := strings.TrimRight(strings.TrimRight(strconv.FormatFloat(f1, 'f', prec, 64), "0"), ".")
	f2Str := strings.TrimRight(strings.TrimRight(strconv.FormatFloat(f2, 'f', prec, 64), "0"), ".")
	switch true {
	case f1Str < f2Str:
		return -1
	case f1Str > f2Str:
		return 1
	default:
		return 0
	}
}

// Role ...
type Role int

const (
	//Driver 司机 0
	Driver = iota
	//Passenger 乘客 1
	Passenger
)

// 根据pid换uid
func GetUidByPassengerId(pid int64, role Role) int64 {
	return pid & ^(int64(0xF)<<48) | int64(role)<<48
}

// EarthDistance 获取地球上两个点的直线距离
func EarthDistance(lat1, lng1, lat2, lng2 float64) float64 {
	radius := 6378137.0 // 6378137
	rad := math.Pi / 180.0

	lat1 = lat1 * rad
	lng1 = lng1 * rad
	lat2 = lat2 * rad
	lng2 = lng2 * rad

	theta := lng2 - lng1
	dist := math.Acos(math.Sin(lat1)*math.Sin(lat2) + math.Cos(lat1)*math.Cos(lat2)*math.Cos(theta))

	return dist * radius
}

func LessOrEqualThanZero(f float64) bool {
	return math.Abs(f) <= floatZero
}

func IsFromMain(accessKeyId int32) bool {
	return InArrayInt32(accessKeyId, []int32{access_key_id.AccessKeyIdDidiIosPassengerApp, access_key_id.AccessKeyIdDidiAndroidPassengerApp})
}

func IsFromMiniProgram(accessKeyId int32) bool {
	return InArrayInt32(accessKeyId, []int32{access_key_id.AccessKeyIdDidiWeChatMiniProgram, access_key_id.AccessKeyIdDidiAlipayMiniProgram})
}
