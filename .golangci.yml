linters-settings:
  depguard:
    list-type: blacklist
    packages:
      # logging is allowed only by logutils.Log, logrus
      # is allowed to use only in logutils package
      - github.com/sirupsen/logrus
  dupl:
    threshold: 100
  funlen:
    lines: 100
    statements: 50
  goconst:
    min-len: 2
    min-occurrences: 3
  gocritic:
    enabled-tags:
      - diagnostic
      - performance
      - style
      # 这俩默认就是禁止的，怕大家接受不了，先禁用了
      # - experimental
      # - opinionated
    enable-checks:
      - builtinShadow  # in opinionated group
      - importShadow  # in opinionated group
      - paramTypeCombine  # in opinionated group

    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain  # 可以商量，我觉得没必要启用
      - octalLiteral
      - whyNoLint
      - wrapperFunc
      - yodaStyleExpr  # in experiment group 可以暂不开启
  gocyclo:
    min-complexity: 20
  golint:
    min-confidence: 0
  gomnd:
    settings:
      mnd:
        # don't include the "operation" and "assign"
        checks: argument,case,condition,return
  lll:
    line-length: 150
  misspell:
    locale: US
  govet:
    check-shadowing: true
  maligned:
    suggest-new: true
  nolintlint:
    allow-leading-space: true # don't require machine-readable nolint directives (i.e. with no leading space)
    allow-unused: false # report any unused nolint directives
    require-explanation: false # don't require an explanation for nolint directives
    require-specific: false # don't require nolint directives to be specific about which linter is being skipped

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - errcheck
    - exhaustive
    - goconst
    - gocritic
    - gofmt
    - goimports
    - golint
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - misspell
    - noctx
    - nolintlint
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace

issues:
  exclude:
    - whyNoLint
    - weak random number generator
    - don't use Yoda conditions
    - weak cryptographic primitive
    - should not use built-in type string as key for value
  exclude-rules:
    - linters:
        - gosec
      text: "G101: Potential hardcoded credentials"

    - linters:
        - gocritic
      text: "unnamedResult: consider giving a name to these results"

    - linters:
        - staticcheck
      text: "SA1019"

run:
  skip-files:
    - common/util/example.go
  skip-dirs:
    - idl
