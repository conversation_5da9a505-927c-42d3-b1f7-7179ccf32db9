package get_form_real_data

import (
	"context"
	"encoding/json"
	"math"

	errors2 "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/model"
)

type Render struct {
	service *Service
}

func NewRender(service *Service) *Render {
	return &Render{
		service: service,
	}
}

func (r *Render) Render(ctx context.Context) (*proto.FormRealData, error) {
	if r.service == nil || r.service.req == nil {
		return nil, errors2.NewBizError(argumentErrorWithMultiProduct, errors2.ErrnoInvalidArgument)
	}

	data := &proto.FormRealData{}
	switch r.service.req.GetSourceType() {
	case consts.SourceTypeNormalTab:
		// 主预估（非7.0/8.5
		return r.RenderSourceTypeNormalTab(ctx, data), nil

	case consts.SourceTypeAnyCarEstimate:
		// 追加车型表单
		return r.RenderSourceTypeAnyCarEstimate(ctx, data), nil

	case consts.SourceTypeClassifyTab:
		// 7.0表单
		return r.RenderSourceTypeClassifyTab(ctx, data), nil

	case consts.SourceTypeAnyCarRecBox:
		// 追加车型推荐弹窗（多车型）
		return r.RenderSourceTypeAnyCarRecBox(ctx, data), nil

	case consts.SourceTypeAnyCarRecBoxSingle:
		// 追加车型推荐弹窗（单车型）
		return r.RenderSourceTypeAnyCarRecBoxSingle(ctx, data), nil

	case consts.SourceTypeOneStop85:
		// 8.5表单
		return r.RenderSourceTypeOneStop85(ctx, data), nil

	case consts.SourceTypeBargainRangeBubble:
		// 自选车价格range冒泡独立页
		return r.RenderSourceTypeBargainRangeBubble(ctx, data), nil

	case consts.SourceTypeBargainRangeAnyCar:
		// 自选车价格range等待应答
		return r.RenderSourceTypeBargainRangeAnyCar(ctx, data), nil

	default:
		return r.RenderDefault(ctx, data), nil
	}
}

func (r *Render) RenderSourceTypeNormalTab(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	if r.isNewFilterTitle(ctx) {
		data.FilterTitle = handler.GetButtonTitle(ctx, r.service.req, r.service.V3GlobalSceneExpect, r.service.IsToEtp)
	} else {
		data.FilterTitle = r.getDefaultTitle(ctx)
	}

	// 大字版屏蔽
	if r.service.req.GetFontScaleType() != 0 {
		data.FilterTitle = ""
	}

	data.HeadInfo, _ = r.service.getFromHeadRule(ctx, consts.SourceTypeNormalTab)
	data.ProductInfo = r.getProductExpectInfo(ctx)
	data.BargainRangeAnswerRateInfo = r.service.getBargainRangeAnswerRateInfoByBubble(ctx)

	return data
}

func (r *Render) RenderSourceTypeAnyCarEstimate(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.PriceDiffInfo = r.service.buildDiffPriceTag(ctx, "anycar_guide")
	data.MatchCardTitle, data.MatchCardLeftIcon = r.service.buildAppendMatchCardTitle(ctx, consts.SourceTypeAnyCarEstimate)
	data.HeadInfo, _ = r.service.getFromHeadRule(ctx, consts.SourceTypeAnyCarEstimate)
	data.RecBubble = r.service.getRecBubble(ctx, consts.SourceTypeAnyCarEstimate)

	return data
}

func (r *Render) RenderSourceTypeClassifyTab(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	var (
		headRuleResult int32
	)
	data.FilterTitle = r.service.getClassifyTabFilterTitle(ctx)
	data.HeadInfo, headRuleResult = r.service.getFromHeadRule(ctx, consts.SourceTypeClassifyTab)
	data.BargainRangeAnswerRateInfo = r.service.getBargainRangeAnswerRateInfoByBubble(ctx)

	if data.ExtraParams == nil {
		data.ExtraParams = make(map[string]int32)
	}
	data.ExtraParams["rec_type"] = headRuleResult

	data.ProductInfo = r.getProductExpectInfo(ctx)
	data.BoxEtpInfo = r.getBoxExpectInfo(ctx, data.ProductInfo)
	return data
}

func (r *Render) RenderSourceTypeAnyCarRecBox(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.PriceDiffInfo = r.service.buildDiffPriceTag(ctx, "rec_pop")
	if data.PriceDiffInfo != nil && len(data.PriceDiffInfo.Text) > 0 {
		data.PriceDiffInfo.Icon = dcmp.GetJSONContentWithPath(ctx, "config_text-anycar_rec_pop_form_real_data", nil, "bubble_icon_url")
	}
	data.MatchCardTitle = r.service.buildBoxRecMatchCardTitle(ctx, consts.SourceTypeAnyCarRecBox)
	data.HeadInfo, _ = r.service.getFromHeadRule(ctx, consts.SourceTypeAnyCarRecBox)
	data.RecBubble = r.service.getRecBubble(ctx, consts.SourceTypeAnyCarRecBox)
	return data
}

func (r *Render) RenderSourceTypeAnyCarRecBoxSingle(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.MatchCardTitle = r.service.appendNormalCompensationInfo(ctx, "", consts.SourceTypeAnyCarRecBoxSingle)
	data.HeadInfo, _ = r.service.getFromHeadRule(ctx, consts.SourceTypeAnyCarRecBoxSingle)
	data.RecBubble = r.service.getRecBubble(ctx, consts.SourceTypeAnyCarRecBoxSingle)
	return data
}

func (r *Render) RenderSourceTypeOneStop85(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.EstimateDurationInfo = r.service.buildEstimateDurationInfo(ctx)
	data.ProductInfo = r.getProductExpectInfo(ctx)
	return data
}

func (r *Render) RenderSourceTypeBargainRangeBubble(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.BargainRangeAnswerRateInfo = r.service.getBargainRangeAnswerRateInfoByBubble(ctx)
	return data
}

func (r *Render) RenderSourceTypeBargainRangeAnyCar(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.BargainRangeAnswerRateInfo = r.service.getBargainRangeAnswerRateInfoByAnycar()
	return data
}

func (r *Render) RenderDefault(ctx context.Context, data *proto.FormRealData) *proto.FormRealData {
	data.FilterTitle = r.getDefaultTitle(ctx)
	return data
}

func (r *Render) isNewFilterTitle(ctx context.Context) bool {
	user := model.NewUser("phone").With("phone", r.service.UserInfo.Phone).With("app_version", r.service.req.Appversion).With("access_key_id", util.ToString(r.service.req.AccessKeyId))
	toggle, err := apollo.FeatureToggle("gs_filter_ets_feature_delete", user)
	if toggle == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "isNewFilterTitle failed with err %+v", err)
		return false
	}

	return toggle.IsAllow()
}

func (r *Render) getDefaultTitle(ctx context.Context) string {
	var title string
	title = handler.GetNewTitle(ctx, r.service.req, r.service.V3GlobalSceneExpect)
	return title
}

func (r *Render) getProductExpectInfo(ctx context.Context) map[string]*proto.FormRealProductInfo {
	var (
		productInfoMap = make(map[string]*proto.FormRealProductInfo)
	)

	if r.service.req.Lang != consts.LangZhCN {
		return productInfoMap
	}
	// 预约单不展示
	if r.service.req.OrderType != nil && *r.service.req.OrderType == consts.BookOrder {
		return productInfoMap
	}

	if r.service.ProductExpectInfo == nil || len(r.service.ProductExpectInfo.ProductInfos) == 0 {
		return productInfoMap
	}
	etpTrackingMap := make(map[string]*proto.FormRealProductInfo)

	defer func() {
		if r.service.IsToEtp {
			util.Go(ctx, func() {
				logPublic(ctx, r.service, etpTrackingMap)
			})
		}
	}()

	for _, productItem := range r.service.ProductExpectInfo.ProductInfos {
		if productItem == nil || productItem.ProductCategory == nil || productItem.ExpectInfo == nil {
			continue
		}

		pcID := *productItem.ProductCategory
		expectInfo := productItem.ExpectInfo

		var (
			ets         int32
			etp         int32
			sceneFlag   int32
			queueLen    int32
			productInfo *proto.FormRealProductInfo
		)

		if expectInfo.Ets != nil {
			ets = *expectInfo.Ets
		}

		if expectInfo.Etp != nil {
			etp = *expectInfo.Etp
		}

		sceneFlag = productItem.GetSceneFlag()
		if expectInfo.GetQueueInfo() != nil {
			queueLen = expectInfo.GetQueueInfo().GetQueueLen()
		}

		if r.service.IsToEtp {
			value, content := r.buildProductExpectETPContent(ctx, etp, sceneFlag, queueLen, pcID)
			productInfo = &proto.FormRealProductInfo{
				ProductExpectText:  &content,
				ProductExpectValue: &etp,
			}
			etpTrackingMap[util.ToString(pcID)] = &proto.FormRealProductInfo{
				ProductExpectText:  &content,
				ProductExpectValue: &value,
			}
		} else {
			content := buildProductExpectContent(ctx, ets, sceneFlag, queueLen)
			productInfo = &proto.FormRealProductInfo{
				ProductExpectText:  &content,
				ProductExpectValue: &ets,
			}
		}

		productInfoMap[util.ToString(pcID)] = productInfo
	}

	// 全局缺B不展示
	if r.isLackBSceneEnabled() && r.service.GlobalSceneExpect != nil && r.service.GlobalSceneExpect.GetGlobalSceneFlag() == 1 {
		return make(map[string]*proto.FormRealProductInfo)
	}

	return productInfoMap
}

func (r *Render) getBoxExpectInfo(ctx context.Context, productInfoMap map[string]*proto.FormRealProductInfo) map[string]*proto.FormRealProductInfo {
	if r.service == nil || r.service.reqBoxInfo == nil || (!r.service.IsToEtp) {
		return nil
	}

	boxInfoMap := make(map[string]*proto.FormRealProductInfo)
	if r.service.Cfg == nil || (!r.service.Cfg.FeatureConfigAllow("is_box_show_etp")) {
		return boxInfoMap
	}

	for groupId, pcids := range r.service.reqBoxInfo {
		if r.shouldHideETPForBox(groupId) {
			boxInfoMap[groupId] = nil
			continue
		}

		minProductInfo := r.findMinETPProduct(pcids, productInfoMap)
		boxInfoMap[groupId] = minProductInfo
	}

	return boxInfoMap
}

// findMinETPProduct 查找具有最小ETP值的产品信息
func (r *Render) findMinETPProduct(pcIds []string, productInfoMap map[string]*proto.FormRealProductInfo) *proto.FormRealProductInfo {
	var (
		minProductInfo   *proto.FormRealProductInfo
		minEtp           = math.MaxInt32
		minSelectProduct *proto.FormRealProductInfo
		minSelectEtp     = math.MaxInt32
		hasSelect        = false
	)

	selectPcIdSet := r.service.selectPcIdSet

	// 抽取查找逻辑
	checkAndUpdate := func(pcId string) {
		if value, ok := productInfoMap[pcId]; ok && value.ProductExpectValue != nil {
			etp := cast.ToInt(*value.ProductExpectValue)
			if _, selected := selectPcIdSet[cast.ToInt64(pcId)]; selected {
				hasSelect = true
			}

			if etp <= 0 || value.ProductExpectText == nil || *value.ProductExpectText == "" {
				return
			}

			if etp < minEtp {
				minEtp = etp
				minProductInfo = value
			}
			if _, selected := selectPcIdSet[cast.ToInt64(pcId)]; selected && etp < minSelectEtp {
				minSelectEtp = etp
				minSelectProduct = value
			}
		}
	}

	for _, pcId := range pcIds {
		checkAndUpdate(pcId)
		if nestedPcIds, ok := r.service.reqBoxInfo[pcId]; ok {
			for _, nestedPcId := range nestedPcIds {
				checkAndUpdate(nestedPcId)
			}
		}
	}

	if hasSelect {
		return minSelectProduct
	}
	return minProductInfo
}

func logPublic(ctx context.Context, service *Service, productInfoMap map[string]*proto.FormRealProductInfo) {
	if len(productInfoMap) == 0 {
		return
	}
	etps := make(map[string]any, len(productInfoMap))
	rmp := service.reqMultiProduct
	for _, mp := range rmp {
		etpInfo, ok := productInfoMap[cast.ToString(mp.ProductCategory)]
		if !ok {
			continue
		}
		every := make(map[string]any)
		every["estimate_id"] = mp.EstimateID
		every["etp"] = etpInfo.ProductExpectValue
		every["etp_content"] = etpInfo.ProductExpectText
		every["product_category"] = mp.ProductCategory
		etps[mp.EstimateID] = every
	}
	logInfo := make(map[string]any)

	logInfo["access_key_id"] = service.req.AccessKeyId
	logInfo["area"] = service.req.FromArea
	logInfo["trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
	logInfo["page_type"] = 0
	logInfo["app_version"] = service.req.Appversion
	logInfo["estimate_trace_id"] = service.req.EstimateTraceId
	logInfo["etps"] = etps
	logInfo["box_info"] = service.req.GetBoxInfo()
	log.Public.Public(ctx, "g_estimate_real_data_etp", logInfo)

}

func buildProductExpectContent(ctx context.Context, ets int32, sceneFlag int32, queueLen int32) string {
	const (
		ProductExpectDcmpKey = "expect_scene-product_expect_config"
	)

	var (
		expectText string
	)

	if ets <= 0 {
		return expectText
	}

	tag := map[string]string{
		"ets_sec":   util.ToString(ets),      // 秒单位
		"ets_min":   util.ToString(ets / 60), // 分钟单位
		"queue_len": util.ToString(queueLen), // 排队人数
	}

	if ets > 0 && ets <= 30 {
		// xx秒
		expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_between_0_30")
	} else if ets > 30 && ets <= 60 {
		// 1分钟内
		expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_between_30_60")
	} else if ets > 60 && ets <= 180 {
		// xx分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_between_60_180")
	} else if ets > 180 && ets <= 300 {
		// 最快3分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_between_180_300")
	} else if ets > 300 && ets <= 600 {
		// 最快5分钟
		expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_between_300_600")
	} else if ets > 600 {
		if consts.AthenaSceneFlag(sceneFlag) == consts.QueueSceneFlag {
			if queueLen <= 0 {
				// 多勾更快
				expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_over_600_not_queue_car")
			} else if queueLen > 0 && queueLen <= 200 {
				// 排队xx人
				expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_over_600_queue_0_200")
			} else if queueLen > 200 {
				// 排队超过200人
				expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_over_600_queue_over_200")
			}
		} else {
			// 附近车少
			expectText = dcmp.GetJSONContentWithPath(ctx, ProductExpectDcmpKey, tag, "expect_scene_over_600_not_queue_scene")
		}
	}

	return expectText
}

type FormDataETPConfig struct {
	ETPThreshold             []handler.FormDataETPThresholdConfig `json:"less_than_threshold"`
	ExpectThreshold          int32                                `json:"expect_threshold"`
	QueueThreshold           int32                                `json:"queue_threshold"`
	OverTimeNoQueueContent   string                               `json:"over_expect_threshold_no_queue_content"`
	OverTimeInQueueContent   string                               `json:"over_expect_threshold_in_queue_content"`
	OverTimeOverQueueContent string                               `json:"over_expect_threshold_over_queue_content"`
	LackB360To600            string                               `json:"lack_b_360_600"`
	LackBOver600             string                               `json:"lack_b_over_600"`
}

func (r *Render) buildProductExpectETPContent(ctx context.Context, etp int32, sceneFlag int32, queueLen int32, pcID int32) (int32, string) {
	if etp <= 0 {
		return 0, ""
	}

	// 检查产品类别是否应该不显示ETP
	if r.shouldHideETPForProduct(pcID) {
		return 0, ""
	}

	var etpConfig FormDataETPConfig
	// 务必保准etp区间配置有序，区间配置左开右闭
	_ = json.Unmarshal([]byte(dcmp.GetDcmpPlainContent(ctx, "expect_scene-product_expect_config_etp_v2")), &etpConfig)

	// 处理缺B场景
	if lackBContent := r.getLackBSceneContent(ctx, etp, sceneFlag, etpConfig); lackBContent != "" {
		return etp, lackBContent
	}

	config := handler.GetETPThresholdConfig(etp, etpConfig.ETPThreshold)
	if config != nil {
		return config.FixValue, config.Content
	}

	if consts.AthenaSceneFlag(sceneFlag) == consts.QueueSceneFlag {
		if queueLen <= 0 {
			return etp, etpConfig.OverTimeNoQueueContent
		} else if queueLen <= 200 {
			return etp, util.ReplaceTag(ctx, etpConfig.OverTimeInQueueContent, map[string]string{
				"queue_len": util.ToString(queueLen), // 排队人数
			})
		} else {
			return etp, etpConfig.OverTimeOverQueueContent
		}
	} else {
		return etp, etpConfig.OverTimeNoQueueContent
	}
}

// shouldHideETPForProduct 检查指定产品类别是否应该隐藏ETP显示
// 通过实验配置控制特定品类不展示etp的能力
func (r *Render) shouldHideETPForProduct(pcID int32) bool {
	if r.service == nil || r.service.Cfg == nil {
		return false
	}

	return r.service.Cfg.FeatureInConfigArray("pc_not_show_etp", pcID)
}

func (r *Render) shouldHideETPForBox(groupId string) bool {
	if r.service == nil || r.service.Cfg == nil {
		return false
	}

	return r.service.Cfg.FeatureInConfigArray("box_not_show_etp", groupId)
}

// getLackBSceneContent 获取缺B场景的内容，如果不是缺B场景则返回空字符串
func (r *Render) getLackBSceneContent(ctx context.Context, etp int32, sceneFlag int32, etpConfig FormDataETPConfig) string {
	if !r.isLackBSceneEnabled() {
		return ""
	}

	if etp > 360 && etp <= 600 {
		return etpConfig.LackB360To600
	}

	if etp > 600 && consts.AthenaSceneFlag(sceneFlag) != consts.QueueSceneFlag {
		return etpConfig.LackBOver600
	}

	return ""
}

// isLackBSceneEnabled 检查是否启用缺B场景
func (r *Render) isLackBSceneEnabled() bool {
	if r.service == nil || r.service.Cfg == nil {
		return false
	}

	return r.service.Cfg.FeatureConfigAllow("lack_b_scene")
}
