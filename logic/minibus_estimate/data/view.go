package data

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/notice_info"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	minib "git.xiaojukeji.com/gulfstream/mamba/render/private/minibus"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/minibus"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

const (
	SeatInfo       = "minibus-carpool_seat_module"
	ShowLeftImage  = 1
	ShowEmptyImage = 0
	ShowBackButton = 1

	MinibusModelTypeLongTrip = 1 // 小巴长单（专线）
	MinibusModelTypeNormal   = 2 // 小巴普通
	MinibusModelTypeHot      = 3 //小巴热线
)

type Render struct {
}

type ExtraInfo struct {
	Title                 string `json:"title"`
	Subtitle              string `json:"subtitle"`
	FeeDetailUrl          string `json:"fee_detail_url"`
	ShowH5                string `json:"show_h5"`
	ConfirmH5             string `json:"confirm_h5"`
	MarkIcon              string `json:"mark_icon"`
	ConfirmButtonTitle    string `json:"confirm_button_title"`
	ConfirmButtonSubtitle string `json:"confirm_button_subtitle"`
	BackgroundIcon        string `json:"back_ground_icon"`
	DefaultIcon           string `json:"default_icon"`
	DefaultText           string `json:"default_text"`
	BackIcon              string `json:"back_icon"`
	JumpLink              string `json:"jump_link"`
	RetryButtonText       string `json:"retry_button_text"`
	MarkIconNotp          string `json:"mark_icon_notp"`
}

type DcmpConfig struct {
	JumpUrl    string `json:"jump_url"`
	BubbleText string `json:"bubble_text"`
}

func NewRender(ctx context.Context) (r *Render) {
	// 初始化配置
	r = &Render{}
	return
}

const (
	SelfPaymentId       = "2"
	BusinessConstSet    = "0"
	MiniBusFeeDetailKey = "minibus-fee_detail"
)

func (r *Render) PreBuilder(ctx context.Context, products []*biz_runtime.ProductInfoFull) {

}

func buildExtraMap(ctx context.Context, product *MiniBusAdapter) *proto.MiniBusExtraMap {
	resp := &proto.MiniBusExtraMap{
		ProductId:       product.GetProductId(),
		BusinessId:      product.GetBusinessID(),
		ComboType:       product.GetComboType(),
		LevelType:       product.GetLevelType(),
		CarpoolType:     product.GetCarpoolType(),
		Etp:             product.GetEtp(),
		ProductCategory: product.GetProductCategory(),
	}

	RequireLevel, err := strconv.ParseInt(product.GetRequireLevel(), 10, 64)
	if err == nil {
		resp.RequireLevel = RequireLevel
	}
	return resp
}

func formatTime(etd int64) string {
	t1 := time.Now().Unix() + etd
	res := time.Unix(t1, 0).Format("15:04")
	return res
}

func getPriceInfoDescList(ctx context.Context, prov *MiniBusAdapter) []*proto.NewFormFeeDesc {

	return minibus.GetPriceInfoDescList(ctx, prov)
}

func AdjustSelected(estimateDatas []*proto.EstimateFromData) {
	if len(estimateDatas) > 0 {
		estimateDatas[0].IsSelected = 1
	}
}

func getFeeMsg(ctx context.Context, prov *MiniBusAdapter) string {
	priceFormated := carpool3.FormatPrice(prov.GetEstimateFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), -1)
	return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
		"amount": priceFormated.StringVal}, "fee_msg")
}

func getNoticeTitle(ctx context.Context, prov *MiniBusAdapter, candidate int) string {
	preMatchInfo := prov.GetMiniBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	// 非候补
	if candidate == 0 {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
			"amount": strconv.Itoa(int(math.Ceil(((float64)(prov.GetEtp())) / 60)))}, "notice_title")
	}

	// 候补状态命中小巴快线
	if _, ok := preMatchInfo.ExtMap["long_trip_id"]; ok {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "notice_title_long_trip")
	}

	//候补状态 非小巴长单 但是命中派空车
	if v, ok := preMatchInfo.ExtMap["minibus_dispatch_empty_driver_time"]; ok && 0 < util.ToFloat64(v) {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "notice_title_hot_line")

	}

	// 候补状态非快线
	return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "notice_title_notp")
}

func getNoticeSubtitle(ctx context.Context, prov *MiniBusAdapter, candidate int) string {
	preMatchInfo := prov.GetMiniBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	// 非候补
	if candidate == 0 {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "notice_subtitle")
	}

	// 候补状态命中小巴快线（命中小巴快线一定会同时有long_trip_id和minibus_dispatch_empty_driver_time俩字段）
	if _, ok := preMatchInfo.ExtMap["long_trip_id"]; ok {
		driverTime := preMatchInfo.ExtMap["minibus_dispatch_empty_driver_time"]
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
			"driver_time": strconv.Itoa(int(math.Ceil(util.ToFloat64(driverTime) / 60)))}, "notice_subtitle_driver_time")
	}

	//候补状态 非小巴长单 但是命中派空车
	if v, ok := preMatchInfo.ExtMap["minibus_dispatch_empty_driver_time"]; ok {
		emptyDriverSec := util.ToFloat64(v)
		//小于一分钟展示秒
		if 0 < emptyDriverSec && emptyDriverSec < 60 {
			return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
				"driver_time_sec": fmt.Sprintf("%d", int(math.Ceil(emptyDriverSec)))}, "notice_subtitle_driver_time_sec")
		}
		//大于一分钟展示分钟
		if emptyDriverSec >= 60 {
			return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
				"driver_time": fmt.Sprintf("%d", int(math.Ceil(emptyDriverSec/60)))}, "notice_subtitle_driver_time")
		}
	}

	// 候补状态命中ets
	if candidateEts, ok := preMatchInfo.ExtMap["wait_answer_time"]; ok {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
			"response_time": strconv.Itoa(int(math.Ceil(util.ToFloat64(candidateEts) / 60)))}, "notice_subtitle_ets")
	}

	return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "notice_subtitle_notp")
}

func getTag(ctx context.Context, prov *MiniBusAdapter, candidate int) string {
	preMatchInfo := prov.GetMiniBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtdInfo == nil {
		return ""
	}

	if candidate == 0 {
		return dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
			"amount1": formatTime(preMatchInfo.EtdInfo.EtdLeftMargin), "amount2": formatTime(preMatchInfo.EtdInfo.EtdRightMargin)}, "tag")
	}

	tag := dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, nil, "tag_notp")
	// 比快车省x元  快车综合预估价 - 小巴券前价
	diff := prov.GetTotalFeeWithoutDiscount() - prov.GetDynamicTotalFee()
	if diff > 0 {
		tag = dcmp.GetJSONContentWithPath(ctx, MiniBusFeeDetailKey, map[string]string{
			"diff": carpool3.FormatPrice(diff, carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), -1).StringVal},
			"tag_price_diff")
	}

	return tag
}

func (r *Render) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData {
	if product == nil {
		return nil
	}

	prov := &MiniBusAdapter{ProductInfoFull: product}

	// 是否为候补订单  0:非候补 1:候补
	var candidate int
	if product.GetEtp() == -1 {
		candidate = 1
	}

	resp := &proto.EstimateFromData{
		EstimateId: prov.GetEstimateID(),
		UserPayInfo: &proto.NewFormUserPayInfo{
			PaymentId:        SelfPaymentId,
			BusinessConstSet: BusinessConstSet,
		},
		ExtraMap: buildExtraMap(ctx, prov),
		Tp: &proto.TP{
			FeeAmount:      carpool3.FormatPrice(prov.GetEstimateFee(), carpool3.PageDefault, util.ToString(prov.GetCityID()), util.Int642String(prov.GetUserPID()), -1).FloatVal,
			FeeMsg:         getFeeMsg(ctx, prov),
			NoticeTitle:    getNoticeTitle(ctx, prov, candidate),
			NoticeSubtitle: getNoticeSubtitle(ctx, prov, candidate),
			FeeDescList:    getPriceInfoDescList(ctx, prov),
			Tag:            getTag(ctx, prov, candidate),
		},
	}

	return resp
}

func buildCarpoolSeatModule(ctx context.Context, baseReqData *models.BaseReqData) *proto.MiniBusCarpoolSeatModule {
	res := &proto.MiniBusCarpoolSeatModule{}
	dcmpConf := dcmp.GetDcmpContent(ctx, "minibus-carpool_seat_module", nil)

	err := json.Unmarshal([]byte(dcmpConf), &res)
	if err != nil {
		return nil
	}
	res.SelectCount = 1
	if baseReqData.CommonBizInfo.CarpoolSeatNum > 0 {
		res.SelectCount = baseReqData.CommonBizInfo.CarpoolSeatNum
	}
	seatNumInfo := baseReqData.CommonBizInfo.SeatNumInfo
	if seatNumInfo != nil && len(seatNumInfo.PassengerCountOption) > 0 {
		res.MaxCount = seatNumInfo.PassengerCountOption[len(seatNumInfo.PassengerCountOption)-1]
	}
	return res
}

func DefaultSelected(resp *proto.MiniBusEstimateData) {
	isSelected := int16(0)
	if len(resp.EstimateData) > 0 {
		for i := 0; i < len(resp.EstimateData); i++ {
			isSelected = isSelected | resp.EstimateData[i].IsSelected
		}
		if isSelected == 0 {
			resp.EstimateData[0].IsSelected = 1
		}
	}
}

func getModelType(ctx context.Context, products []*biz_runtime.ProductInfoFull) int {
	if len(products) == 0 {
		return -1
	}
	prov := &MiniBusAdapter{ProductInfoFull: products[0]}
	preMatch := prov.GetMiniBusPreMatch()
	if preMatch == nil || preMatch.ExtMap == nil {
		return MinibusModelTypeNormal
	}
	//长单
	if v, ok := preMatch.ExtMap["long_trip_id"]; ok && v != "" {
		return MinibusModelTypeLongTrip
	}
	//热线
	if v, ok := preMatch.ExtMap["minibus_dispatch_empty_driver_time"]; ok && v != "" {
		emptyDriverSec := util.ToFloat64(v)
		if emptyDriverSec > 0 {
			return MinibusModelTypeHot
		}
	}

	return MinibusModelTypeNormal
}

func getOmegaEstimateIds(ctx context.Context, resp *proto.MiniBusEstimateData) string {
	omegaEstimateIds := make([]string, 0)
	if resp == nil || len(resp.EstimateData) == 0 {
		return ""
	}
	for _, v := range resp.EstimateData {
		omegaEstimateIds = append(omegaEstimateIds, v.GetEstimateId())

	}
	val, err := json.Marshal(omegaEstimateIds)
	if err != nil {
		return ""
	}
	return string(val)
}

// buildOmegaData 构建omega数据
func buildOmegaData(ctx context.Context, baseReqData *models.BaseReqData, resp *proto.MiniBusEstimateData, products []*biz_runtime.ProductInfoFull) *proto.MiniBusOmegaInfo {

	params := map[string]interface{}{
		"from":         baseReqData.CommonInfo.FromType,
		"model_type":   getModelType(ctx, products),
		"estimate_ids": getOmegaEstimateIds(ctx, resp),
	}
	showOmega := &proto.ShowOmega{
		Key:    "wyc_lcar_pas_bubble_sw",
		Params: params,
	}
	return &proto.MiniBusOmegaInfo{
		ShowOmega: showOmega,
	}
}

func (r *Render) ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) {
	dcmpConf := dcmp.GetDcmpContent(ctx, "minibus-separate_page_conf", nil)
	conf := &ExtraInfo{}
	err := json.Unmarshal([]byte(dcmpConf), &conf)
	if err != nil {
		return
	}

	toggle := apollo.FeatureToggle(ctx, "minibus_long_trip_open_city_switch", util.ToString(baseReqData.PassengerInfo.PID), map[string]string{
		"pid":           util.ToString(baseReqData.PassengerInfo.PID),
		"access_key_id": util.ToString(baseReqData.CommonInfo.AccessKeyID),
		"app_version":   baseReqData.CommonInfo.AppVersion,
		"page_type":     util.ToString(baseReqData.CommonInfo.PageType),
	})
	if len(products) > 0 && toggle {
		dcmpInfo := &DcmpConfig{}
		dcmpConf = dcmp.GetDcmpContent(ctx, "estimate_form_v3-long_trip_notice_info", nil)
		if err = json.Unmarshal([]byte(dcmpConf), dcmpInfo); err != nil {
			return
		}

		prov := &MiniBusAdapter{ProductInfoFull: products[0]}
		preMatch := prov.GetMiniBusPreMatch()
		resp.MapCurveInfo = &proto.MapCurveInfo{
			//na 7.0.6以下，wx 6.10.25以下，继续用此字段不展示曲线
			IsDrawCurve: 0,
			// 新增的曲线类型，除快线外均展示曲线
			CurveType: proto.Int32Ptr(2),
		}
		resp.BestViewType = 1
		if preMatch != nil && preMatch.ExtMap != nil {
			if _, ok := preMatch.ExtMap["long_trip_id"]; ok {
				resp.BestViewType = 2
				resp.MapCurveInfo.BubbleText = dcmpInfo.BubbleText
				resp.MapCurveInfo.IsDrawCurve = 1
				resp.MapCurveInfo.CurveType = proto.Int32Ptr(1)
				resp.MapCurveInfo.JumpUrl = notice_info.UrlAppendParams(dcmpInfo.JumpUrl, map[string]string{
					"uid":           util.ToString(prov.BaseReqData.PassengerInfo.UID),
					"lang":          prov.BaseReqData.CommonInfo.Lang,
					"app_version":   prov.BaseReqData.CommonInfo.AppVersion,
					"access_key_id": util.ToString(prov.BaseReqData.CommonInfo.AccessKeyID),
					"city_id":       util.ToString(prov.BaseReqData.AreaInfo.City),
					"long_trip_id":  preMatch.ExtMap["long_trip_id"],
					"page_from":     "4",
				})
			}
		}
	}

	resp.CarpoolSeatModule = buildCarpoolSeatModule(ctx, baseReqData)
	resp.PageType = 40
	resp.ConfirmButton = &proto.ConfirmButton{
		Title:    conf.ConfirmButtonTitle,
		Subtitle: conf.ConfirmButtonSubtitle,
	}
	if baseReqData.CommonInfo.FromType == ShowBackButton {
		resp.BackButton = &proto.BackButton{
			LeftIcon: conf.BackIcon,
			JumpTo:   conf.JumpLink,
		}
	}

	resp.DynamicEffectParams = &proto.DynamicEffectParams{}
	if len(resp.EstimateData) > 0 {
		resp.Title = conf.Title
		resp.Subtitle = conf.Subtitle
		resp.IsSupportMultiSelection = 1
		resp.FeeDetailUrl = conf.FeeDetailUrl
		resp.PluginPageInfo = &proto.MiniBusPluginPageInfo{
			ShowH5:    conf.ShowH5,
			ConfirmH5: conf.ConfirmH5,
		}
		if len(resp.EstimateData) == 1 && resp.EstimateData[0] != nil && resp.EstimateData[0].ExtraMap != nil && resp.EstimateData[0].ExtraMap.Etp == -1 {
			resp.DynamicEffectParams.MarkIcon = conf.MarkIconNotp
		} else {
			resp.DynamicEffectParams.MarkIcon = conf.MarkIcon
		}
		DefaultSelected(resp)
	}
	if len(resp.EstimateData) == ShowLeftImage {
		resp.DynamicEffectParams.BackgroundIcon = conf.BackgroundIcon
	} else if len(resp.EstimateData) == ShowEmptyImage {
		param := ApolloModel.NewUser("").
			With("access_key_id", strconv.FormatInt(int64(baseReqData.CommonInfo.AccessKeyID), 10)).
			With("app_version", baseReqData.CommonInfo.AppVersion)

		toggle, err := ApolloSDK.FeatureToggle("minibus_retry_button", param)
		if err == nil && toggle.IsAllow() {
			resp.DynamicEffectParams.RetryButtonText = conf.RetryButtonText
		}
		resp.DynamicEffectParams.DefaultIcon = conf.DefaultIcon
		resp.DynamicEffectParams.DefaultText = conf.DefaultText
	}

	if minib.JudgeNewMinibus(baseReqData.CommonInfo.AccessKeyID, baseReqData.CommonInfo.AppVersion) {
		resp.TokenInfo = &proto.TokenInfo{
			MapinfoStartCacheToken: baseReqData.CommonBizInfo.TokenInfo.MapinfoStartCacheToken,
			MapinfoDestCacheToken:  baseReqData.CommonBizInfo.TokenInfo.MapinfoDestCacheToken,
		}
	}

	resp.OmegaInfo = buildOmegaData(ctx, baseReqData, resp, products)
}
