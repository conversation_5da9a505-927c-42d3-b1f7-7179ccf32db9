package predict_manage_card

import (
	"context"
	"encoding/json"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/consts"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestBargainBuildFeeInfo(t *testing.T) {

	quation := biz_runtime.Quotation{
		ProductCategory: 327,
	}

	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerBargainRangeInfo: "2",
	}, &RenderBaseData{})
	adapter.RequiredProductStruct = dos.RequiredProductStruct{
		PassengerBargainRangeInfo: "2",
	}

	pmCard := PredictManageCard{}
	pmCard.buildFeeInfo(context.TODO(), adapter)
}

func TestFastRangeBuildFeeInfo(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 328,
	}

	OrderedFeeInfo := map[string]float64{}
	OrderedFeeInfo["min_price"] = 2
	OrderedFeeInfo["max_price"] = 5
	marshal, _ := json.Marshal(OrderedFeeInfo)

	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerBargainRangeInfo: string(marshal),
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	pmCard.buildFeeInfo(context.TODO(), adapter)
}

func TestCarpoolLowPriceBuildFeeInfo1(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 46,
		CarpoolSceneBill: &PriceApi.CarpoolSceneBill{
			ScenePrice: nil,
		},
	}

	scenePrice := make([]*PriceApi.CarpoolScenePrice, 0)
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option:      &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 1, SeatNum: 1},
		EstimateFee: 20.0,
	})
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option:      &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 2, SeatNum: 1},
		EstimateFee: 18.0,
	})

	quation.CarpoolSceneBill.ScenePrice = scenePrice

	// Mock DCMP.GetDcmpContent
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return("\"seat_1\": \"拼2人{{carpool_2}}元 | 拼1人{{carpool_1}}元\",\n  \"seat_2\": \"拼1人{{carpool_1}}元\",\n  \"seat_1_same\": \"拼成{{carpool_1}}元\"").Build()
	defer dcmpMocker.UnPatch()
	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerCount: 1,
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	_, feeAmount, _ := pmCard.buildFeeInfo(context.TODO(), adapter)
	assert.Equal(t, "18", feeAmount)
}

func TestCarpoolLowPriceBuildFeeInfo2(t *testing.T) {
	quation := biz_runtime.Quotation{
		ProductCategory: 46,
		CarpoolSceneBill: &PriceApi.CarpoolSceneBill{
			ScenePrice: nil,
		},
	}

	scenePrice := make([]*PriceApi.CarpoolScenePrice, 0)
	scenePrice = append(scenePrice, &PriceApi.CarpoolScenePrice{
		Option:      &PriceApi.CarpoolPriceSceneOption{IsCarpoolSuccess: true, PoolNum: 1, SeatNum: 2},
		EstimateFee: 20.0,
	})

	quation.CarpoolSceneBill.ScenePrice = scenePrice
	// 模拟OrderInfoService
	orderInfoService := &order_info.OrderInfoService{}

	// 创建一个实际的 card.Input 对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	adapter := NewAdapter(quation, input, dos.RequiredProductStruct{
		PassengerCount: 2,
	}, &RenderBaseData{})

	pmCard := PredictManageCard{}
	_, feeAmount, _ := pmCard.buildFeeInfo(context.TODO(), adapter)
	assert.Equal(t, "20", feeAmount)
}

// 测试RegisterRPC方法
func TestRegisterRPC(t *testing.T) {
	pmCard := &PredictManageCard{}

	// 执行测试
	result := pmCard.RegisterRPC()

	// 验证结果
	assert.NotNil(t, result, "返回的RPC列表不应为nil")
	assert.Contains(t, result, consts.RPCOrderInfo, "应包含OrderInfo RPC")
}

// 测试IsCanRender方法 - 无OrderInfo的情况
func TestIsCanRenderNoOrderInfo(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 使用mockey来mock IsCanRender方法内部对order_info.OrderInfoService的依赖
	isCanRenderMocker := mockey.Mock((*PredictManageCard).IsCanRender).To(func(_ *PredictManageCard, ctx context.Context, input *card.Input) bool {
		if input.OrderInfoService.GetOrderInfo() == nil {
			return false
		}
		return true
	}).Build()
	defer isCanRenderMocker.UnPatch()

	// 创建一个测试用的input
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: order_info.NewOrderInfoService(nil),
	}

	// 模拟GetOrderInfo返回nil
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(nil).Build()
	defer getOrderInfoMocker.UnPatch()

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "没有OrderInfo应返回false")
}

// 测试IsCanRender方法 - 空QuotationMap的情况
func TestIsCanRenderEmptyQuotationMap(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	orderInfo := &order_info.SimpleOrderInfo{}
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建一个空的 map[string]PriceApi.EstimateQuotation
	emptyQuotations := make(map[string]PriceApi.EstimateQuotation)
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(emptyQuotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "空QuotationMap应返回false")
}

// 测试IsCanRender方法 - IsAnycar="1"但数量不一致的情况
func TestIsCanRenderAnycarCountMismatch(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "1",
		ExtendFeatureParsed: dos.ExtendFeatureStruct{
			MultiRequiredProduct: map[string]dos.RequiredProductStruct{
				"key1": {},
				"key2": {},
			},
		},
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建不一致数量的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "IsAnycar='1'但数量不一致应返回false")
}

// 测试IsCanRender方法 - 非IsAnycar但QuotationMap数量不为1的情况
func TestIsCanRenderNonAnycarQuotationCountMismatch(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建多于1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
		"key2": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "非IsAnycar但QuotationMap数量不为1应返回false")
}

// 测试IsCanRender方法 - DCMP配置解析失败的情况
func TestIsCanRenderDCMPError(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建只有1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// Mock DCMP.GetDcmpContent
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return("{invalid json}").Build()
	defer dcmpMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.False(t, result, "DCMP配置解析失败应返回false")
}

// 测试IsCanRender方法 - 满足所有条件的情况
func TestIsCanRenderSuccess(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	pmCard := &PredictManageCard{}

	// 模拟OrderInfoService
	orderInfoService := order_info.NewOrderInfoService(nil)

	// 创建模拟订单信息
	orderInfo := &order_info.SimpleOrderInfo{
		IsAnycar: "0",
	}

	// 模拟GetOrderInfo和GetEstimateQuotationMap方法
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// 创建只有1个元素的 map[string]PriceApi.EstimateQuotation
	quotations := map[string]PriceApi.EstimateQuotation{
		"key1": {},
	}
	getQuotationMapMocker := mockey.Mock((*order_info.OrderInfoService).GetEstimateQuotationMap).Return(quotations).Build()
	defer getQuotationMapMocker.UnPatch()

	// Mock DCMP获取
	config := &PredictManagerCardConfig{
		MainTitle: "正在同时呼叫{{carCount}}种车型",
	}
	configJSON, _ := json.Marshal(config)
	dcmpMocker := mockey.Mock(dcmp.GetDcmpContent).Return(string(configJSON)).Build()
	defer dcmpMocker.UnPatch()

	// 创建Input对象
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: orderInfoService,
	}

	// 执行测试
	result := pmCard.IsCanRender(ctx, input)

	// 验证结果
	assert.True(t, result, "满足所有条件应返回true")
	assert.Equal(t, config.MainTitle, pmCard.DCMPConfig.MainTitle, "DCMPConfig应被正确设置")
}

//func TestDealWaitFreeUpgradeScene(t *testing.T) {
//
//	convey.Convey("测试 dealWaitFreeUpgradeScene 函数", t, func() {
//		// 创建测试数据
//		carList := []*proto.CarData{
//			{GroupKey: "GroupKeyHandpickedFastCar", LeftInfo: &proto.LeftInfo{FeeMsg: "Premier Fee Msg", FeeAmount: "100"}},
//			{GroupKey: "GroupKeyFast", LeftInfo: &proto.LeftInfo{FeeMsg: "Fast Fee Msg", FeeAmount: "50"}},
//			{GroupKey: "GroupKeySpecialRate", LeftInfo: &proto.LeftInfo{FeeMsg: "Special Rate Fee Msg", FeeAmount: "200"}},
//		}
//
//		extendFeatureParsed := dos.ExtendFeatureStruct{
//			MultiRequiredProduct: map[string]dos.RequiredProductStruct{
//				"1": {SceneList: string([]int32{1, 2, 3})},
//			},
//		}
//
//		orderInfo := &order_info.SimpleOrderInfo{
//			OrderId:             "12345",
//			ExtendFeatureParsed: extendFeatureParsed,
//		}
//
//		renderBaseData := &RenderBaseData{}
//
//		// 模拟 scene.GetWaitUpgradeSceneForPlutus 方法
//		//mockGetWaitUpgradeScene := mockey.Mock(scene.GetWaitUpgradeSceneForPlutus).To(func(sceneList string) int {
//		//	return ptsConsts.WaitFastUpgradePremierSceneForPlutus
//		//}).Build()
//		//defer mockGetWaitUpgradeScene.UnPatch()
//
//		// 测试另一个分支
//		mockGetWaitUpgradeScene := mockey.Mock(scene.GetWaitUpgradeSceneForPlutus).To(func(sceneList string) int {
//			return 8
//		}).Build()
//		defer mockGetWaitUpgradeScene.UnPatch()
//		// 调用被测试函数
//		dealWaitFreeUpgradeScene(carList, orderInfo, renderBaseData)
//
//		// 验证 carList 是否被正确修改
//		convey.So(carList[0].LeftInfo.FeeMsg, convey.ShouldEqual, "Fast Fee Msg")
//		convey.So(carList[0].LeftInfo.FeeAmount, convey.ShouldEqual, "50")
//
//		// 验证 renderBaseData 是否被正确修改
//		convey.So(renderBaseData.estimateMax, convey.ShouldEqual, cast.ToFloat64("200"))
//		convey.So(renderBaseData.estimateMin, convey.ShouldEqual, cast.ToFloat64("50"))
//
//
//
//		//// 重置 carList 中的 HandpickedFastCar 的 LeftInfo
//		//carList[3].LeftInfo = &proto.LeftInfo{FeeMsg: "Handpicked Fast Fee Msg", FeeAmount: "150"}
//		//
//		//// 调用被测试函数
//		//dealWaitFreeUpgradeScene(carList, orderInfo, renderBaseData)
//		//
//		//// 验证 carList 是否被正确修改
//		//convey.So(carList[3].LeftInfo.FeeMsg, convey.ShouldEqual, "Fast Fee Msg")
//		//convey.So(carList[3].LeftInfo.FeeAmount, convey.ShouldEqual, "50")
//	})
//
//
//}

func createMockInput(pid int64) *card.Input {
	return &card.Input{
		Req: &proto.PGetGuideInfoReq{
			Pid:          pid,
			FromCityId:   1,
			FromCountyId: 2,
			AppVersion:   "1.0.0",
			AccessKeyId:  123,
		},
	}
}

func TestIsHitSurpriseDiscountBox_True(t *testing.T) {
	patch := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, name, userID string, params map[string]string) bool {
		assert.Equal(t, "surprise_discount_box_toggle", name)
		return true
	}).Build()
	defer patch.UnPatch()

	ctx := context.Background()
	input := createMockInput(10001)
	result := isHitSurpriseDiscountBox(ctx, input)
	assert.True(t, result)
}

func TestIsHitSurpriseDiscountBox_False(t *testing.T) {
	patch := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, name, userID string, params map[string]string) bool {
		assert.Equal(t, "surprise_discount_box_toggle", name)
		return false
	}).Build()
	defer patch.UnPatch()

	ctx := context.Background()
	input := createMockInput(10002)
	result := isHitSurpriseDiscountBox(ctx, input)
	assert.False(t, result)
}

func TestRenderResponse_SurpriseDiscountBox(t *testing.T) {
	pmCard := &PredictManageCard{
		DCMPConfig: &PredictManagerCardConfig{},
	}
	ctx := context.Background()

	// mock input
	input := &card.Input{
		Req:              &proto.PGetGuideInfoReq{},
		OrderInfoService: &order_info.OrderInfoService{},
	}

	orderInfo := &order_info.SimpleOrderInfo{}
	getOrderInfoMocker := mockey.Mock((*order_info.OrderInfoService).GetOrderInfo).Return(orderInfo).Build()
	defer getOrderInfoMocker.UnPatch()

	// patch NewRenderBaseData 返回空 renderBaseData
	patch := mockey.Mock(NewRenderBaseData).To(func(ctx context.Context, p *PredictManageCard, input *card.Input) *RenderBaseData {
		return &RenderBaseData{}
	}).Build()
	defer patch.UnPatch()

	// patch isHitSurpriseDiscountBox 返回 true
	patch2 := mockey.Mock(isHitSurpriseDiscountBox).Return(true).Build()
	defer patch2.UnPatch()

	// patch provider, 使 GetLevelType 返回 8（LimitedTimeOfferLevelType）
	adapter := &Adapter{}
	mockey.Mock(NewAdapter).Return(adapter).Build()
	mockey.Mock((*Adapter).GetProductCategory).Return(int64(7)).Build()
	mockey.Mock((*Adapter).GetLevelType).Return(int32(8)).Build()
	mockey.Mock((*PredictManageCard).buildFeeInfo).Return("msg", "amt", []string{}).Build()
	mockey.Mock((*PredictManageCard).buildMultiPriceDesc).Return([]*proto.NewFormMultiPrice{}).Build()
	mockey.Mock((*PredictManageCard).buildFeeDescList).Return([]string{}).Build()
	mockey.Mock((*PredictManageCard).buildCarName).Return("出租车").Build()
	mockey.Mock((*PredictManageCard).buildCarIcon).Return("icon").Build()

	guideInfoRes := &proto.GuideInfoData{}

	pmCard.RenderResponse(ctx, input, guideInfoRes)

	// 断言 SubGroupId 被赋值为 11（SurpriseDiscount）
	carList := guideInfoRes.PredictManageCardV2.CarList[0].CarList
	assert.NotEmpty(t, carList)
	for _, car := range carList {
		if car.SubGroupId != nil {
			assert.Equal(t, int32(11), *car.SubGroupId, "SubGroupId 应为 11（SurpriseDiscount）")
		}
	}
	mockey.UnPatchAll()
}

// mockProviderLimitedTimeOffer 用于返回 LimitedTimeOfferLevelType

func TestDealSubGroupIdMerge_SurpriseDiscountMerged(t *testing.T) {
	pmCard := &PredictManageCard{
		bizData: &PredictManageCardBizData{},
	}
	ctx := context.Background()

	// mock dcmp.GetJSONContentWithPath
	patch := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, key string, tags map[string]string, path string) string {
		if path == "car_title" {
			return "惊喜特价车"
		}
		if path == "car_icon" {
			return "icon-surprise"
		}
		return ""
	}).Build()
	defer patch.UnPatch()

	// 构造 carMap，包含 SubGroupIdSurpriseDiscount
	surpriseDiscount := int32(11)
	productCategory := int64(1001)
	carData := &proto.CarData{
		SubGroupId: &surpriseDiscount,
		LeftInfo:   &proto.LeftInfo{},
	}
	carMap := map[int64]*proto.CarData{
		productCategory: carData,
	}

	carMapRes := pmCard.dealSubGroupIdMerge(ctx, carMap)

	// 断言 carData.LeftInfo 被赋值
	res, ok := carMapRes[productCategory]
	assert.True(t, ok)
	assert.Equal(t, "惊喜特价车", res.LeftInfo.CarName)
	assert.Equal(t, "icon-surprise", res.LeftInfo.CarIcon)
}
