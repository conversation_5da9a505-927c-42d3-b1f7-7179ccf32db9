package predict_manage_card

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"
	"math"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gobiz/logger"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	taxi_util "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	ptsConsts "git.xiaojukeji.com/s3e/pts/v2/common/consts"
	"git.xiaojukeji.com/s3e/pts/v2/scene"
	"github.com/spf13/cast"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type PredictManageCard struct {
	Res        *proto.PredictManageCard
	DCMPConfig *PredictManagerCardConfig
	ufsRPC     *rpc_process.UfsAlwaysPoolFeature
	bizData    *PredictManageCardBizData
}

type PredictManagerCardConfig struct {
	MainTitle                string            `json:"main_title"`          // 正在同时呼叫{{carCount}}种车型
	SubTitle                 string            `json:"sub_title"`           // （价格仅供参考，以最终实付为准）
	PriceRange               string            `json:"price_range"`         // {{min}}-{{max}}
	PopSubTitle              string            `json:"pop_sub_title"`       // "部分车型含节假日服务费{{price_range}}元",
	SubTitleSelectInfo       string            `json:"select_info"`         // 已选{{carCount}}种车型
	SubTitleSelectInfoByName string            `json:"select_info_by_name"` // 已选{{carName}}
	SubTitleEstimateInfo     string            `json:"estimate_info"`       // 预估{{price_range}}
	SubTitleEstimateInfoV2   string            `json:"estimate_info_v2"`    // 预估{{price_range}}{{unit}}
	MixPersonal              string            `json:"mix_personal"`        // 个人付约{{price_range}}元
	AlwaysCarpool            string            `json:"always_carpool"`      //拼成一口价{{price_range}}元
	TPName                   map[string]string `json:"tp_name"`             //
}

const (
	GroupKeyPremier           = "1_100_0"
	GroupKeyFast              = "3_600_0"
	GroupKeyAplus             = "3_600_0_68"
	GroupKeySpecialRate       = "800_40600_0"
	GroupKeyLimitSpecial      = "800_40600_0_191"
	GroupKeyHandpickedFastCar = "3_690_0"
)

type PredictManageCardBizData struct {
	farMustCheapNum int32 // 远必省车型数量
}

func (p *PredictManageCard) RegisterRPC() []string {

	return []string{consts.RPCOrderInfo}
}
func (p *PredictManageCard) IsCanRender(ctx context.Context, input *card.Input) bool {
	if input.OrderInfoService.GetOrderInfo() == nil {
		return false
	}

	quotationMap := input.OrderInfoService.GetEstimateQuotationMap()
	qCount := len(quotationMap)
	if qCount <= 0 {
		return false
	}

	// 数量不一致
	if input.OrderInfoService.GetOrderInfo().IsAnycar == "1" {
		if qCount != len(input.OrderInfoService.GetOrderInfo().ExtendFeatureParsed.MultiRequiredProduct) {
			logger.Warnf(ctx, "PredictManageCard", "not CanRender||qCount=%d||MultiRequiredProduct=%d", qCount,
				len(input.OrderInfoService.GetOrderInfo().ExtendFeatureParsed.MultiRequiredProduct))
			return false
		}
	} else {
		if qCount != 1 {
			return false
		}
	}

	// 初始化dcmp
	p.DCMPConfig = new(PredictManagerCardConfig)
	str := dcmp.GetDcmpContent(ctx, "guide_info-predict_manage_card", nil)
	err := json.Unmarshal([]byte(str), p.DCMPConfig)
	if err != nil || p.DCMPConfig == nil {
		return false
	}

	//拼友推荐需要初始化ufs
	if UFSRpc := rpc_process.NewAlwaysPoolRPC(ctx, ufs.DomainOrder, input.OrderInfoService.GetOrderInfo()); UFSRpc != nil {
		p.ufsRPC = UFSRpc
	}

	return true
}

func (p *PredictManageCard) RenderResponse(ctx context.Context, input *card.Input, guideInfoRes *proto.GuideInfoData) {
	quotationMap := input.OrderInfoService.GetEstimateQuotationMap()
	orderInfo := input.OrderInfoService.GetOrderInfo()
	renderBaseData := NewRenderBaseData(ctx, p, input)
	p.bizData = &PredictManageCardBizData{}

	var carMap = make(map[int64]*proto.CarData)
	var hitSurpriseDiscountBox = isHitSurpriseDiscountBox(ctx, input)

	if input.OrderInfoService.GetOrderInfo().IsAnycar == "1" {
		for groupKey, ProductInfo := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {

			q := biz_runtime.Quotation(quotationMap[ProductInfo.EstimateID])
			provider := NewAdapter(q, input, ProductInfo, renderBaseData)

			var multiFee []string
			renderBaseData.FeeMsg, renderBaseData.FeeAmount, multiFee = p.buildFeeInfo(ctx, provider)
			multiPrice := p.buildMultiPriceDesc(ctx, provider)
			feeDescList := p.buildFeeDescList(ctx, provider)

			carData := &proto.CarData{
				GroupKey: groupKey,
				LeftInfo: &proto.LeftInfo{
					CarName:        p.buildCarName(ctx, provider, renderBaseData),
					CarIcon:        p.buildCarIcon(ctx, provider),
					FeeAmount:      renderBaseData.FeeAmount,
					FeeMsg:         renderBaseData.FeeMsg,
					MultiPriceList: multiPrice,
					FeeDescList:    feeDescList,
				},
				ProductCategory: provider.GetProductCategory(),
			}

			if provider.GetLevelType() == level_type.FarMustCheaperLevelType {
				farCheap := int32(commonConsts.SubGroupIdFarMustCheap)
				carData.SubGroupId = &farCheap
				p.bizData.farMustCheapNum += 1
			}
			if provider.GetLevelType() == level_type.LimitedTimeOfferLevelType && hitSurpriseDiscountBox {
				surpriseDiscount := int32(commonConsts.SubGroupIdSurpriseDiscount)
				carData.SubGroupId = &surpriseDiscount
			}

			carMap[provider.GetProductCategory()] = carData

			p.calculateInfo(q, provider, renderBaseData, carData, multiPrice, multiFee)
		}

		// 处理车型合并情况
		carMap = p.dealSubGroupIdMerge(ctx, carMap)

	} else {
		q := biz_runtime.Quotation(quotationMap[input.OrderInfoService.GetOrderInfo().EstimateId])

		provider := NewAdapter(q, input, dos.RequiredProductStruct{}, renderBaseData)

		var multiFee []string
		renderBaseData.FeeMsg, renderBaseData.FeeAmount, multiFee = p.buildFeeInfo(ctx, provider)
		multiPrice := p.buildMultiPriceDesc(ctx, provider)
		carData := &proto.CarData{
			GroupKey: input.OrderInfoService.GetOrderInfo().ProductId + "_" + input.OrderInfoService.GetOrderInfo().RequireLevel + "_" +
				input.OrderInfoService.GetOrderInfo().ComboType,
			LeftInfo: &proto.LeftInfo{
				CarName:        p.buildCarName(ctx, provider, renderBaseData),
				CarIcon:        p.buildCarIcon(ctx, provider),
				FeeAmount:      renderBaseData.FeeAmount,
				FeeMsg:         renderBaseData.FeeMsg,
				MultiPriceList: multiPrice,
			},
			ProductCategory: provider.GetProductCategory(),
		}
		carMap[provider.GetProductCategory()] = carData

		p.calculateInfo(q, provider, renderBaseData, carData, multiPrice, multiFee)
	}

	if renderBaseData.PricingBoxData != nil && carMap[estimate_pc_id.EstimatePcIdUnione] != nil &&
		!renderBaseData.PricingBoxData.EnableRangeStyle && carMap[estimate_pc_id.EstimatePcIdTaxiMarketisationPutong] != nil {
		carMap[estimate_pc_id.EstimatePcIdUnione].LeftInfo.CarName = renderBaseData.PricingBoxData.Conf.V3LayoutConf.Title
		carMap[estimate_pc_id.EstimatePcIdUnione].LeftInfo.FeeAmount = carMap[estimate_pc_id.EstimatePcIdTaxiMarketisationPutong].LeftInfo.FeeAmount
		carMap[estimate_pc_id.EstimatePcIdUnione].LeftInfo.FeeMsg = carMap[estimate_pc_id.EstimatePcIdTaxiMarketisationPutong].LeftInfo.FeeMsg

		delete(carMap, estimate_pc_id.EstimatePcIdTaxiMarketisationPutong)
		renderBaseData.carCount--
	} else if renderBaseData.PricingBoxData != nil && carMap[estimate_pc_id.EstimatePcIdFastTaxi] != nil && input.Req.Lang == "zh-CN" {
		// 计价盒子有补天出租车时，194改名：盒子名-在线计价，7改名：盒子名-打表计价
		prefix := car_info.GetCarNameByCondition(ctx, estimate_pc_id.EstimatePcIdFastTaxi, util.ToInt(input.Req.FromCityId), input.Req.Lang)
		if prefix != "" {
			fastTaxiSuffix := renderBaseData.PricingBoxData.GetCarTitleByPcId(estimate_pc_id.EstimatePcIdFastTaxi)
			carMap[estimate_pc_id.EstimatePcIdFastTaxi].LeftInfo.CarName = prefix + "-" + fastTaxiSuffix
			if carMap[estimate_pc_id.EstimatePcIdUnione] != nil {
				taxiSuffix := renderBaseData.PricingBoxData.GetCarTitleByPcId(estimate_pc_id.EstimatePcIdUnione)
				carMap[estimate_pc_id.EstimatePcIdUnione].LeftInfo.CarName = prefix + "-" + taxiSuffix
			}
		}
	} else if renderBaseData.PricingBoxData != nil && carMap[7] != nil && !renderBaseData.PricingBoxData.EnableRangeStyle {
		carMap[estimate_pc_id.EstimatePcIdUnione].LeftInfo.CarName = renderBaseData.PricingBoxData.Conf.V3LayoutConf.Title

		if carMap[estimate_pc_id.EstimatePcIdTaxiMarketisationPutong] != nil {
			delete(carMap, estimate_pc_id.EstimatePcIdTaxiMarketisationPutong)
			renderBaseData.carCount--
		}
	}

	var carList []*proto.CarData
	for _, carData := range carMap {
		carList = append(carList, carData)
	}

	sort.Slice(carList, func(i, j int) bool {
		if carList[i].LeftInfo != nil && carList[j].LeftInfo != nil &&
			carList[i].LeftInfo.FeeAmount != "" && carList[j].LeftInfo.FeeAmount != "" {
			iAmount := cast.ToFloat64(carList[i].LeftInfo.FeeAmount)
			jAmount := cast.ToFloat64(carList[j].LeftInfo.FeeAmount)
			return iAmount < jAmount
		}
		return false
	})

	dealWaitFreeUpgradeScene(carList, orderInfo, renderBaseData)

	popData := &proto.PopData{
		Type:      2,
		Tag:       "all_list",
		MainTitle: p.buildMainTitle(ctx, renderBaseData.carCount),
		SubTitle:  p.buildSubTile(ctx, input, renderBaseData),
		CarList:   carList,
	}

	guideInfoRes.PredictManageCardV2 = &proto.PredictManageCard{
		SubTitleList1: p.buildSubTitleList1(ctx, renderBaseData, carList, input),
		CarList:       []*proto.PopData{popData},
	}
}

func NewRenderBaseData(ctx context.Context, p *PredictManageCard, input *card.Input) *RenderBaseData {

	baseData := &RenderBaseData{
		estimateMin: math.MaxFloat64,
		tpInfo:      p.initTp(input.OrderInfoService.GetOrderInfo()),
	}
	apolloParam := ApolloModel.NewUser(strconv.FormatInt(input.Req.GetUid(), 10)).
		With("pid", strconv.FormatInt(input.Req.GetPid(), 10)).
		With("city", strconv.FormatInt(int64(input.Req.GetFromCityId()), 10)).
		With("phone", input.Req.GetPhone()).
		With("access_key_id", strconv.FormatInt(int64(input.Req.GetAccessKeyId()), 10)).
		With("county_id", strconv.FormatInt(input.Req.GetFromCountyId(), 10)). //todo ???
		With("lang", input.Req.GetLang()).
		With("app_version", input.Req.GetAppVersion())
	pricingBoxData, _ := taxi_util.Init(ctx, apolloParam, "predict_card")
	if pricingBoxData != nil {
		baseData.PricingBoxData = pricingBoxData
	}

	return baseData
}

func (p *PredictManageCard) GetCardLog(ctx context.Context, input *card.Input, data *proto.GuideInfoData) map[string]interface{} {
	logInfo := make(map[string]interface{})

	logInfo["car_name"] = "PredictManageCard"
	logInfo["access_key_id"] = input.Req.AccessKeyId
	logInfo["app_version"] = input.Req.AppVersion
	logInfo["order_id"] = input.Req.OrderId
	logInfo["uid"] = input.Req.Uid
	logInfo["pid"] = input.Req.Pid

	return logInfo
}

func (p *PredictManageCard) calculateInfo(q biz_runtime.Quotation, req *Adapter, baseData *RenderBaseData,
	carData *proto.CarData, multiPrice []*proto.NewFormMultiPrice, multiFee []string) {
	baseData.carCount++

	estimateFee := cast.ToFloat64(carData.LeftInfo.FeeAmount)
	if req.GetProductCategory() == 327 {
		estimateFee = util.Float64Add(req.RequiredProductStruct.CapPrice, req.RedPacket)
	}

	if estimateFee < baseData.estimateMin {
		baseData.estimateMin = estimateFee
	}
	if estimateFee > baseData.estimateMax {
		baseData.estimateMax = estimateFee
	}

	// 价格区间参与排序
	for _, feeStr := range multiFee {
		estimateFee = cast.ToFloat64(feeStr)
		if estimateFee < baseData.estimateMin {
			baseData.estimateMin = estimateFee
		}
		if estimateFee > baseData.estimateMax {
			baseData.estimateMax = estimateFee
		}
	}

	if q.RedPacket > 0 {
		baseData.redPacketFee = q.RedPacket
	}

	if q.IsBusinessPay() {
		baseData.IsBusinessPay = q.IsBusinessPay()
	}

	if len(multiPrice) > 0 {
		baseData.MultiPrice = multiPrice
	}

	return
}

func (p *PredictManageCard) dealSubGroupIdMerge(ctx context.Context, carMap map[int64]*proto.CarData) map[int64]*proto.CarData {
	var (
		carMapRes              = make(map[int64]*proto.CarData)
		farCheapMerged         bool
		surpriseDiscountMerged bool
	)

	for pcID, carData := range carMap {
		if carData.SubGroupId != nil && *carData.SubGroupId == int32(commonConsts.SubGroupIdFarMustCheap) {
			if !farCheapMerged {
				carData.LeftInfo.CarName = dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-far_must_cheap", nil, "car_title")
				carData.LeftInfo.CarIcon = dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-far_must_cheap", nil, "car_icon")
				carData.ProductNums = &p.bizData.farMustCheapNum
				carMapRes[pcID] = carData
				farCheapMerged = true
			}
		} else if carData.SubGroupId != nil && *carData.SubGroupId == int32(commonConsts.SubGroupIdSurpriseDiscount) {
			if !surpriseDiscountMerged {
				carData.LeftInfo.CarName = dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-surprise_discount_box", nil, "car_title")
				carData.LeftInfo.CarIcon = dcmp.GetJSONContentWithPath(ctx, "predict_manage_card-surprise_discount_box", nil, "car_icon")
				carMapRes[pcID] = carData
				surpriseDiscountMerged = true
			}
		} else {
			carMapRes[pcID] = carData
		}
	}

	return carMapRes
}

func dealWaitFreeUpgradeScene(carList []*proto.CarData, orderInfo *order_info.SimpleOrderInfo, renderBaseData *RenderBaseData) {
	if len(carList) == 0 {
		return
	}
	if len(orderInfo.ExtendFeatureParsed.MultiRequiredProduct) == 0 {
		return
	}
	upgradeType := 0
	for _, product := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		if upgradeType = scene.GetWaitUpgradeSceneForPlutus(product.SceneList); upgradeType != 0 {
			break
		}
	}

	if upgradeType == 0 {
		return
	}

	groupKey2CarData := make(map[string]*proto.CarData)
	for _, car := range carList {
		groupKey2CarData[car.GroupKey] = car
	}

	for i := range carList {
		switch upgradeType {
		case ptsConsts.WaitFastUpgradePremierSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeyFast]; carList[i].GroupKey == GroupKeyPremier && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		case ptsConsts.WaitSpecialRateUpgradeFastSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeySpecialRate]; carList[i].GroupKey == GroupKeyFast && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		case ptsConsts.WaitAPlusUpgradePremierSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeyAplus]; carList[i].GroupKey == GroupKeyPremier && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		case ptsConsts.WaitLimitSpecialUpgradeSpecialRateSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeyLimitSpecial]; carList[i].GroupKey == GroupKeySpecialRate && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		case ptsConsts.WaitSpecialRateUpgradeHandpickedFastSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeySpecialRate]; carList[i].GroupKey == GroupKeyHandpickedFastCar && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		case ptsConsts.WaitFastUpgradeHandpickedFastSceneForPlutus:
			if carData, ok := groupKey2CarData[GroupKeyFast]; carList[i].GroupKey == GroupKeyHandpickedFastCar && ok {
				carList[i].LeftInfo.FeeMsg = carData.LeftInfo.FeeMsg
				carList[i].LeftInfo.FeeAmount = carData.LeftInfo.FeeAmount
			}
		}
	}

	sort.Slice(carList, func(i, j int) bool {
		if carList[i].LeftInfo != nil && carList[j].LeftInfo != nil &&
			carList[i].LeftInfo.FeeAmount != "" && carList[j].LeftInfo.FeeAmount != "" {
			iAmount := cast.ToFloat64(carList[i].LeftInfo.FeeAmount)
			jAmount := cast.ToFloat64(carList[j].LeftInfo.FeeAmount)
			return iAmount < jAmount
		}
		return false
	})

	if carList[0].LeftInfo != nil && carList[len(carList)-1].LeftInfo != nil &&
		carList[0].LeftInfo.FeeAmount != "" && carList[len(carList)-1].LeftInfo.FeeAmount != "" {

		renderBaseData.estimateMax = cast.ToFloat64(carList[len(carList)-1].LeftInfo.FeeAmount)
		renderBaseData.estimateMin = cast.ToFloat64(carList[0].LeftInfo.FeeAmount)
	}
}

func isHitSurpriseDiscountBox(ctx context.Context, input *card.Input) bool {
	apolloParam := map[string]string{
		"key":           cast.ToString(input.Req.Pid),
		"pid":           cast.ToString(input.Req.Pid),
		"city":          cast.ToString(input.Req.FromCityId),
		"county":        cast.ToString(input.Req.FromCountyId),
		"app_version":   input.Req.AppVersion,
		"access_key_id": cast.ToString(input.Req.AccessKeyId),
	}
	return apollo.FeatureToggle(ctx, "surprise_discount_box_toggle", cast.ToString(input.Req.Pid), apolloParam)
}
