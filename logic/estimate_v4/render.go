package estimate_v4

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/map_curve_info"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/map_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/notice_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/order_button_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/seat_module"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/toast_tip"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/prefer_data"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_num"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_bargain"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic/fee_desc_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/hit_dynamic_price"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/is_hide_price"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/pay_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/radio_setting"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/route_id_list"

	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type CarEstimateData struct {
	ProductCategory int64 `json:"product_category"`
	IsSelected      int32 `json:"is_selected"`

	FeeAmount   float64                        `json:"fee_amount"` // 价格信息
	EtpInfo     *AthenaApiv3.EstimatedTimeInfo `json:"etp_info"`
	DriverMeter int64                          `json:"driver_meter"` // 打车距离
}

type Render struct {
	req          *models.BaseReqData
	products     []*biz_runtime.ProductInfoFull
	isImbalanced int32
}

func NewRender(list []*biz_runtime.ProductInfoFull, req *models.BaseReqData, isImbalanced int32) (r *Render) {
	// 初始化配置
	r = &Render{
		req:          req,
		products:     list,
		isImbalanced: isImbalanced,
	}
	r.preBuilder()
	return
}

func (r *Render) preBuilder() {
	var (
		fastPrice           float64
		pricingByMeterPrice float64
	)

	for _, pFull := range r.products {
		if pFull.GetProductCategory() == ProductCategory.ProductCategoryFast {
			fastPrice = pFull.GetEstimateFee()
		} else if pFull.GetProductCategory() == ProductCategory.ProductCategoryUnione {
			pricingByMeterPrice = pFull.GetEstimateFee()
		}
	}

	for _, pFull := range r.products {
		pFull.BaseReqData.CommonBizInfo.FastCarPrice = fastPrice
		pFull.BaseReqData.CommonBizInfo.PricingByMeterPrice = pricingByMeterPrice
	}

}

func (r *Render) Render(ctx context.Context) (resp *proto.V4Data, carList []*CarEstimateData, err error) {
	var (
		estimateData = make(map[int64]*proto.V4EstimateData)
		productMap   = make(map[int64]*biz_runtime.ProductInfoFull) // 存储已渲染的品类数据
	)

	defer func() {
		util.Go(ctx, func() {
			AddPublicLog(ctx, r.products, resp)
		})
	}()

	resp = new(proto.V4Data)

	resp = &proto.V4Data{
		EstimateData: make(map[int64]*proto.V4EstimateData),
	}

	// 渲染单品类
	pool := pool2.NewPoolByTimeout(ctx, conf.Viper.GetDuration("estimate_v4.render_timeout"))
	for _, full := range r.products {
		pool.AddTask(&pool2.Task{
			Req:     full,
			RunTask: RenderByProduct,
		})
		prov := &data.EstimateV4Adapter{ProductInfoFull: full}

		carList = append(carList, &CarEstimateData{
			ProductCategory: prov.GetProductCategory(),
			FeeAmount:       prov.GetEstimateFee(),
			EtpInfo:         full.BaseReqData.CommonBizInfo.ETInfoMap[int32(prov.GetProductCategory())],
			DriverMeter:     prov.GetBillDriverMetre(),
			IsSelected:      estimate_v4.GetSelection(ctx, prov),
		})
	}
	// 并行渲染
	_, err = pool.MultiProc()
	if err != nil {
		return resp, carList, err
	}

	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		pFull, ok1 := task.Req.(*biz_runtime.ProductInfoFull)
		if !ok1 {
			continue
		}

		if product, ok := task.Resp.(*proto.V4EstimateData); ok {
			pFull.Product.BizInfo.CheckStatus = int(product.IsSelected)
			productMap[product.ProductCategory] = pFull
			estimateData[product.ProductCategory] = product
		}
	}
	resp.EstimateData = estimateData

	// 渲染layout
	resp.Layout = New(r.req, estimateData, productMap).BuildLayout(ctx)
	resp.RealParams = map[string]int32{
		"is_imbalanced": r.isImbalanced,
	}

	// 渲染额外数据
	BuildExtraInfo(ctx, r.products, resp)

	return resp, carList, nil
}
func RenderByProduct(ctx context.Context, input interface{}) (interface{}, error) {

	product, ok := input.(*biz_runtime.ProductInfoFull)
	if !ok {
		return nil, errors.New("product req error ok")
	}

	renderProvider := &data.EstimateV4Adapter{ProductInfoFull: product}

	// 基础信息
	productAfterRender := &proto.V4EstimateData{
		EstimateId:      product.GetEstimateID(),
		ProductCategory: product.GetProductCategory(),

		HitDynamicPrice: hit_dynamic_price.IsHitDynamicPrice(ctx, renderProvider),
		HitShowH5Type:   estimate_extra.GetHitShowH5Type(ctx, product),
		IsTripcloud:     estimate_extra.GetIsTripcloud(ctx, product),

		PreferData:             prefer_data.GetPreferData(ctx, renderProvider),
		CarpoolSeatList:        carpool_seat_num.GetCarpoolSeatNumV3(ctx, renderProvider),
		RouteIdList:            route_id_list.GetRouteIdList(ctx, renderProvider),
		ExtraMap:               estimate_v4.GetExtraMap(ctx, renderProvider),
		ExtraEstimateData:      nil,
		AutoDrivingAddressInfo: estimate_v4.GetAutoDrivingAddressInfo(ctx, renderProvider),
		IsSelected:             estimate_v4.GetSelection(ctx, renderProvider),
		// DepartTag:              depart_tag.GetDepartTag(ctx, renderProvider),
		SubIntroIcon: nil,
		RadioSetting: radio_setting.GetOrderOption(ctx, renderProvider),
		OrderOption:  nil,
		IsHidePrice:  is_hide_price.GetResult(ctx, renderProvider),
	}

	productAfterRender.CarTitle = car_info.GetCarNameV2(ctx, renderProvider)
	productAfterRender.CarIcon = car_info.GetCarIconV2(ctx, renderProvider)
	productAfterRender.CarTag = nil
	productAfterRender.SubTitle = nil
	productAfterRender.CarSubTitle = nil
	productAfterRender.TipsIcon = nil

	feeInfo := fee_info_render.GetEstimateFeeInfo(ctx, renderProvider)
	productAfterRender.FeeAmount = feeInfo.FeeAmount
	productAfterRender.FeeMsg = feeInfo.FeeMsg
	productAfterRender.FeeRangeTemplate = &feeInfo.FeeRangeTemplate
	productAfterRender.MinFeeAmount = &feeInfo.MinFeeAmount
	productAfterRender.FeeDescList = fee_desc_list.GetEstimateFeeDescList(ctx, renderProvider, renderProvider.GetPageType())
	productAfterRender.MultiPriceList = category_carpool.GetMultiPriceList(ctx, renderProvider)
	productAfterRender.UserPayInfo = pay_info.GetPayInfoV3(ctx, renderProvider)
	productAfterRender.NeedPayFeeAmount = category_bargain.GetNeedPayFeeAmount(ctx, renderProvider)
	productAfterRender.UnselectedData = estimate_v4.GetUnselectedData(ctx, renderProvider)

	productAfterRender.CarpoolSeatModule = seat_module.GetSeatModule(ctx, renderProvider)
	productAfterRender.NoticeInfo = notice_info.GetNoticeInfo(ctx, renderProvider)
	productAfterRender.MapInfo = map_info.GetMapInfo(ctx, renderProvider)
	productAfterRender.MapCurveInfo = map_curve_info.GetMapCurveInfo(ctx, renderProvider)

	return productAfterRender, nil
}

func BuildExtraInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull, res *proto.V4Data) {
	res.FeeDetailUrl = fee_detail_info.GetDetailUrl(ctx)
	res.IsSupportMultiSelection = 1
	res.PluginPageInfo = estimate_extra.GetFullPluginPageInfo(ctx, products)
	res.ToastTip = toast_tip.GetToastTip(ctx, products)
	res.OrderButtonInfo = order_button_info.GetOrderButtonInfo(ctx, products)
}
