package estimate_v4

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/uranus"
	"strconv"
	"time"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const tag = "EstimateV4Params"

func CheckParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*EstimateV4Req)
		if !ok {
			return BizError.ErrInvalidArgument
		}
		if req.OriReq.WycFromLat == 0 || req.OriReq.WycFromLng == 0 || req.OriReq.WycToLat == 0 || req.OriReq.WycToLng == 0 ||
			req.OriReq.MapType == "" || req.OriReq.WycFromPoiId == "" || req.AreaInfo == nil {
			log.Trace.Warnf(ctx, tag, "area params err")
			return BizError.ErrInvalidArgument
		}

		if len(req.OriReq.GetAppVersion()) == 0 {
			log.Trace.Warnf(ctx, tag, "GetAppVersion err")
			return BizError.ErrInvalidArgument
		}

		return BizError.Success
	}
}

func BuildParams(ctx context.Context, req *EstimateV4Req) biz_runtime.GeneratorOption {
	request := req.OriReq
	return func(pg *biz_runtime.ProductsGenerator) error {

		areaInfo := req.AreaInfo
		var departureTime int64
		if request.GetOrderType() == 0 {
			departureTime = time.Now().Unix()
		} else {
			departureTime = util.ToInt64(request.GetDepartureTime())
		}
		builder := models.NewBaseReqDataBuilder().
			SetClientInfo(&models.ClientInfo{
				AppVersion:        request.GetAppVersion(),
				AccessKeyID:       request.GetAccessKeyId(),
				Channel:           request.GetChannel(),
				SourceID:          source_id.SourceIDCompositeTravel85,
				Lang:              request.GetLang(),
				MenuID:            consts.MenuIDDaCheAnyCar,
				PageType:          page_type.PageTypeUndefined,
				ClientType:        request.GetClientType(),
				OriginID:          1, // 滴滴
				PlatformType:      request.GetPlatformType(),
				ScreenPixels:      request.GetScreenPixels(),
				ScreenScale:       request.GetScreenScale(),
				TerminalID:        request.GetTerminalId(),
				EstimateStyleType: request.GetEstimateStyleType(),
				TabList:           request.GetTabList(),
				TabId:             request.GetTabId(),
				Xpsid:             request.GetXpsid(),
				XpsidRoot:         request.GetXpsidRoot(),
			}).
			SetFullAreaInfo(models.AreaInfo{
				MapType: request.MapType,

				CurLat: request.Lat,
				CurLng: request.Lng,

				FromLat:     request.WycFromLat,
				FromLng:     request.WycFromLng,
				FromPoiID:   request.WycFromPoiId,
				FromPoiType: request.WycFromPoiType,
				FromAddress: request.WycFromAddress,
				FromName:    request.WycFromName,
				District:    areaInfo.FromDistrict,
				FromCounty:  areaInfo.FromCounty,

				Area: areaInfo.FromCityId,
				City: areaInfo.FromCityId,

				ToLat:     request.WycToLat,
				ToLng:     request.WycToLng,
				ToPoiID:   request.WycToPoiId,
				ToPoiType: request.WycToPoiType,
				ToAddress: request.WycToAddress,
				ToName:    request.WycToName,
				ToCounty:  areaInfo.ToCounty,
				ToArea:    areaInfo.ToCityId,

				StopoverPointInfo:      nil,
				MapInfoCacheToken:      "",
				MapInfoStartCacheToken: "",
				MapInfoDestCacheToken:  "",
				AbstractDistrict:       areaInfo.AbstractDistrict,
				ChooseFSearchid:        request.WycFSearchid,
				ChooseTSearchid:        request.WycTSearchid,
			}).
			SetUserOption(&models.UserOption{
				CallCarType:           0,  // 不支持代叫
				CallCarPhone:          "", // 不支持代叫
				CarpoolSeatNum:        request.GetCarpoolSeatNum(),
				OrderType:             request.GetOrderType(),
				DepartureTime:         departureTime,
				DepartureRange:        nil,
				PaymentsType:          0,
				StopoverPoints:        nil, // 途经点不支持
				MultiRequireProduct:   request.GetMultiRequireProduct(),
				IsGuide:               nil,
				LuxurySelectCarlevels: request.GetLuxurySelectCarlevels(),
				LuxurySelectDriver:    request.GetLuxurySelectDriver(), // 豪华车偏好设置
				PreferredRouteId:      request.GetPreferredRouteId(),
			})

		if user := req.UserInfo; req.UserInfo != nil {
			builder.SetPassengerInfoV2(&models.PassengerInfoV2{
				UID:      int64(user.UID),
				PID:      int64(user.PID),
				Phone:    user.Phone,
				Role:     user.Role,
				Channel:  user.Channel,
				UserType: request.GetUserType(),
				OriginID: user.OriginId,
				AppID:    user.AppID,
				Token:    req.OriReq.Token,
			})
		}

		pg.BaseReqData = builder.GetBaseReqData()

		if request.MultiRequireProduct != nil {
			multiRequiredProduct := *request.MultiRequireProduct

			if len(multiRequiredProduct) > 0 {

				var multiRequireProduct []models.RequireProduct
				_ = json.Unmarshal([]byte(multiRequiredProduct), &multiRequireProduct)
				pg.BaseReqData.CommonBizInfo.PcId2CustomFeature = make(map[int64][]dos.CustomFeatureStruct)

				for _, product := range multiRequireProduct {
					var res []dos.CustomFeatureStruct
					if len(product.CustomFeature) == 0 {
						continue
					}
					err := json.Unmarshal([]byte(product.CustomFeature), &res)
					if err != nil {
						continue
					}

					pg.BaseReqData.CommonBizInfo.PcId2CustomFeature[product.ProductCategory] = res
				}
			}
		}

		buildLuxRequireProducts(ctx, pg.BaseReqData)

		pg.IsBaseReqInit = true
		return nil
	}
}

func buildLuxRequireProducts(ctx context.Context, data *models.BaseReqData) {
	multiRequireProducts := data.CommonBizInfo.MultiRequireProduct
	if len(multiRequireProducts) == 0 {
		return
	}

	luxuryConfig := apollo.GetConfig(ctx, "luxury_carlevel_product_config", "luxury_carlevel_product_config")
	if luxuryConfig == nil {
		return
	}

	productInfoRaw := luxuryConfig["product_category"]
	carLevelInfoRaw := luxuryConfig["car_level"]
	productInfos := make(map[string]int)
	carLevelInfos := make(map[string]map[string]int64)

	err := json.Unmarshal([]byte(productInfoRaw), &productInfos)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(carLevelInfoRaw), &carLevelInfos)
	if err != nil {
		return
	}

	for i := 0; i < len(multiRequireProducts); i++ {
		requireProduct := multiRequireProducts[i]
		if productId, ok := productInfos[strconv.FormatInt(requireProduct.ProductCategory, 10)]; ok {
			carLevels := carLevelInfos[strconv.Itoa(productId)]
			luxurySelectCarlevels := getLuxSelectInfos(ctx, data, &requireProduct)
			for carLevel, pcid := range carLevels {
				if len(luxurySelectCarlevels) > 0 && carLevel == luxurySelectCarlevels[0] {
					requireProduct.ProductCategory = pcid
					data.CommonBizInfo.MultiRequireProduct[i] = requireProduct
					break
				}
			}
		}
	}

}

func getLuxSelectInfos(ctx context.Context, data *models.BaseReqData, product *models.RequireProduct) []string {
	var luxSelectCarlevel []string
	var selectedDriver string
	var selectCarlevel string

	// 老版本 从入参获取选项
	if !isSupport(ctx, data) {
		selectedDriver = data.CommonInfo.LuxurySelectDriver
		selectCarlevel = data.CommonInfo.LuxurySelectCarlevels
	} else {
		// 新版 豪华车
		selectedDriver = product.LuxurySelectDriver
		selectCarlevel = product.LuxurySelectCarlevel
	}

	// 任意司务员 传给下游会计费 需置空
	if selectedDriver == "0" {
		data.CommonInfo.LuxurySelectDriver = ""
	}

	if !isSupport(ctx, data) || !isSixLux(ctx, product.ProductCategory) {
		// 有选择司务员 且不是任意司务员（“0”）
		if "" != selectedDriver && "0" != selectedDriver {
			if isSixLux(ctx, product.ProductCategory) {
				data.CommonInfo.SixSeatDesignatedDriver = selectedDriver
			} else {
				data.CommonInfo.DesignatedDriver = selectedDriver
			}
			// driverId小于0是通用司务员，不用在意其车型
			if driverId, err := strconv.ParseInt(selectedDriver, 10, 64); err == nil && driverId > 0 {
				driverBizInfo, err := uranus.GetDriverBizInfo(ctx, driverId)
				if err == nil && driverBizInfo != nil {
					if "" != driverBizInfo.CarLevel {
						data.CommonInfo.SelectedCarlevel = driverBizInfo.CarLevel
						luxSelectCarlevel = []string{driverBizInfo.CarLevel}
						// 使用司务员车型 更新 已选车型
						if level, err := strconv.Atoi(driverBizInfo.CarLevel); err == nil {
							if carlevel, err := json.Marshal([]int{level}); err == nil {
								data.CommonInfo.LuxurySelectCarlevels = string(carlevel)
							}
						}
						return luxSelectCarlevel
					}

				}
			}
		}
		// 没有选择司务员 使用勾选车型level
		if !isSupport(ctx, data) {
			// 老版本是数组 "[1405]"
			var tmp []int
			_ = json.Unmarshal([]byte(selectCarlevel), &tmp)
			if len(tmp) > 0 {
				luxSelectCarlevel = []string{strconv.Itoa(tmp[0])}
			}

		} else {
			// 新版是字符串 "1405"
			luxSelectCarlevel = []string{selectCarlevel}
		}

		if len(luxSelectCarlevel) > 0 {
			data.CommonInfo.SelectedCarlevel = luxSelectCarlevel[0]
		}

		return luxSelectCarlevel

	} else {
		// 如果新版六座豪华车
		// 有选择司务员 且不是任意司务员（“0”）
		if "" != selectedDriver && "0" != selectedDriver {
			data.CommonInfo.SixSeatDesignatedDriver = selectedDriver
			// driverId小于0是通用司务员，不用在意其车型
			if driverId, err := strconv.ParseInt(selectedDriver, 10, 64); err == nil && driverId > 0 {
				driverBizInfo, err := uranus.GetDriverBizInfo(ctx, driverId)
				if err == nil && driverBizInfo != nil {
					if "" != driverBizInfo.CarLevel {
						data.CommonInfo.SixSeatSelectedCarlevel = driverBizInfo.CarLevel
						luxSelectCarlevel = []string{driverBizInfo.CarLevel}
						return luxSelectCarlevel
					}
				}
			}
		}
		// 没有选择司务员 使用勾选车型level
		data.CommonInfo.SixSeatSelectedCarlevel = selectCarlevel
		luxSelectCarlevel = []string{selectCarlevel}

		return luxSelectCarlevel
	}

}

func isSupport(ctx context.Context, data *models.BaseReqData) bool {
	// 参数未完全初始化，拿不全，只拿到（city, appversion, accesskeyid, lang）此处够用
	ABparam := data.GetApolloParam()
	return apollo.FeatureToggle(ctx, "six_seat_lux_toggle", "", ABparam)
}

func isSixLux(ctx context.Context, pcid int64) bool {
	return apollo.FeatureToggle(ctx, "six_seat_lux_judge", "", map[string]string{"product_category": strconv.FormatInt(pcid, 10)})
}
