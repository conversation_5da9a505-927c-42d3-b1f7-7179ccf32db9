package data

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/minibus"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data/build_func/map_station"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/smart_bus"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
)

const (
	SmartBusFeeDetailUnifiedPriceKey        = "smart_bus-fee_detail_unified_price"
	SmartBusSeparatePageConfUnifiedPriceKey = "smart_bus-separate_page_conf_unified_price"

	SmartBusPageVersionUnifiedPrice = "smart_bus-unified_price"

	LogTag = "smart_bus"

	EventId = "wyc_zncar_send_card_sw"
)

const (
	DefaultEtp = 60 // etp最小兜底，以second为单位
)

type UnifiedPriceRender struct {
	FeeDetail    *UnifiedPriceFeeDetail
	SeparatePage *UnifiedPriceSeparatePage
	BaseRender
}

type UnifiedPriceFeeDetail struct {
	FeeMsg                  string `json:"fee_msg"`
	NoticeTitle             string `json:"notice_title"`
	NoticeTitleNotp         string `json:"notice_title_notp"`
	NoticeTitleScanInCar    string `json:"notice_title_scan_in_car"`
	NoticeSubtitle          string `json:"notice_subtitle"`
	NoticeSubtitleNotp      string `json:"notice_subtitle_notp"`
	NoticeSubtitleScanInCar string `json:"notice_subtitle_scan_in_car"`
	ConfirmSubtitle         string `json:"confirm_subtitle"`
	NoticeTextNotp          string `json:"notice_text_notp"`
	NoticeSubtext           string `json:"notice_subtext"`
	WalkDistLeftThreshold   int32  `json:"walk_dist_left_threshold"`
	WalkDistRightThreshold  int32  `json:"walk_dist_right_threshold"`
	EtpFormatThreshold      int64  `json:"etp_format_threshold"`
	ExpressLineTag          string `json:"express_line_tag"`
}

type UnifiedPriceSeparatePage struct {
	BaseExtraInfo
}

func (u *UnifiedPriceRender) Init(ctx context.Context) {
	dcmpFeeDetail := dcmp.GetDcmpContent(ctx, SmartBusFeeDetailUnifiedPriceKey, nil)
	feeDetail := &UnifiedPriceFeeDetail{}
	err := json.Unmarshal([]byte(dcmpFeeDetail), &feeDetail)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "Unmarshal dcmp smart_bus-fee_detail_unified_price fail, err:[%v]", err)
		return
	}
	u.FeeDetail = feeDetail
	dcmpSeparatePage := dcmp.GetDcmpContent(ctx, SmartBusSeparatePageConfUnifiedPriceKey, nil)
	separatePage := &UnifiedPriceSeparatePage{}
	err = json.Unmarshal([]byte(dcmpSeparatePage), &separatePage)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "Unmarshal dcmp smart_bus-separate_page_conf_unified_price fail, err:[%v]", err)
		return
	}
	u.SeparatePage = separatePage
}

func getTpPreMatchType(product *biz_runtime.ProductInfoFull) int32 {
	// 是否为候补订单 0:非候补 1:候补 3:快线
	if product.GetSmartBusEtp() == -1 {
		return PreMatchTypeNoCar
	}
	if level_type.IsSmartBusExpressLine(product.GetLevelType()) {
		return PreMatchTypeExpressLine
	}
	return PreMatchTypePreMatch
}

func (u *UnifiedPriceRender) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData {
	if product == nil || u.FeeDetail == nil {
		return nil
	}
	u.adapter = &SmartBusAdapter{ProductInfoFull: product}

	// 是否为候补订单 0:非候补 1:候补
	var preMatchType int32
	if product.GetSmartBusEtp() == -1 {
		preMatchType = PreMatchTypeNoCar
	}
	u.preMatchType = preMatchType

	u.tpPreMatchType = getTpPreMatchType(product)

	if PreMatchTypePreMatch == preMatchType {
		preMatchInfo := u.adapter.GetSmartBusPreMatch()
		if preMatchInfo != nil && preMatchInfo.ExtMap != nil && preMatchInfo.ExtMap["chosen_station_info"] != "" {
			err := json.Unmarshal([]byte(preMatchInfo.ExtMap["chosen_station_info"]), &u.startStationInfo)
			if err != nil {
				u.startStationInfo = nil
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "smart bus prematch unmarshal duse start station failed:%v", err)
			}
		} else {
			u.startStationInfo = nil
		}
	}

	resp := &proto.EstimateFromData{
		EstimateId: u.adapter.GetEstimateID(),
		ExtraMap:   u.buildExtraMap(ctx),
		Tp: &proto.TP{
			FeeAmount:      u.adapter.GetEstimateFee(),
			FeeMsg:         u.getFeeMsg(ctx),
			NoticeTitle:    u.getNoticeTitle(ctx),
			NoticeSubtitle: u.getNoticeSubtitle(ctx),
			FeeDescList:    u.getPriceInfoDescList(ctx),
			Tag:            "",
			NoticeText:     u.getNoticeText(ctx),
			NoticeSubtext:  u.getNoticeSubtext(ctx),
			SubTag:         u.getSubTag(ctx),
		},
		PreMatchType:      u.getTpPreMatchType(ctx),
		BestViewType:      u.getBestViewType(ctx),
		StartStationInfo:  u.buildStartStationInfo(ctx),
		DestStationInfo:   u.buildEndStationInfo(ctx),
		ConfirmSubtitle:   u.getConfirmSubtitle(ctx),
		SideParams:        u.buildSideParams(ctx),
		ApproachPointList: u.buildApproachStationInfo(ctx),
		OmegaInfo:         u.buildTpOmegaInfo(ctx),
	}
	return resp
}

func (u *UnifiedPriceRender) getBestViewType(_ context.Context) int32 {
	//车上扫码场景：全览
	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return BestViewTypeOverview
	}
	// 快线模式: 全览
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		return BestViewTypeOverview
	}

	if rpc_process.ErrPreMatchStartDestOverlap == u.adapter.BaseReqData.CommonBizInfo.PreMatchTPErrNo {
		return BestViewTypeOverview
	}

	return BestViewTypeStartPoint
}

func (u *UnifiedPriceRender) getTpPreMatchType(_ context.Context) int32 {
	return u.tpPreMatchType
}

func (u *UnifiedPriceRender) buildTpOmegaInfo(_ context.Context) *proto.TPShowOmega {
	params := map[string]interface{}{
		"pre_match_type": u.tpPreMatchType,
		"carpool_type":   u.adapter.GetCarpoolType(),
		"dchn":           u.adapter.BaseReqData.CommonInfo.Dchn,
		"estimate_id":    u.adapter.GetEstimateID(),
		"level_type":     u.adapter.GetLevelType(),
	}

	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		if u.adapter.GetSmartBusPreMatch() != nil && u.adapter.GetSmartBusPreMatch().ShiftInfo != nil {
			shiftInfo := u.adapter.GetSmartBusPreMatch().ShiftInfo
			params["shift_id"] = shiftInfo.ShiftID
			if shiftInfo.StartStation != nil {
				params["start_station_id"] = shiftInfo.StartStation.StationID
			}
			if shiftInfo.EndStation != nil {
				params["end_station_id"] = shiftInfo.EndStation.StationID
			}
		}
	}
	return &proto.TPShowOmega{
		ShowOmega: &proto.ShowOmega{
			Key:    EventId,
			Params: params,
		},
	}
}

func (u *UnifiedPriceRender) buildExtraMap(_ context.Context) *proto.MiniBusExtraMap {
	resp := &proto.MiniBusExtraMap{
		ProductId:       u.adapter.GetProductId(),
		BusinessId:      u.adapter.GetBusinessID(),
		ComboType:       u.adapter.GetComboType(),
		LevelType:       u.adapter.GetLevelType(),
		CarpoolType:     u.adapter.GetCarpoolType(),
		Etp:             u.adapter.GetSmartBusEtp(),
		ProductCategory: u.adapter.GetProductCategory(),
	}
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		realEtp := u.adapter.GetSmartBusEtp() - time.Now().Unix()
		if realEtp < DefaultEtp {
			realEtp = DefaultEtp
		}
		resp.SetEtp(realEtp)
		resp.SetDepartureTime(u.adapter.GetSmartBusEtp())
		if u.adapter.GetSmartBusPreMatch() != nil && u.adapter.GetSmartBusPreMatch().ShiftInfo != nil {
			resp.SetBusServiceShiftId(u.adapter.GetSmartBusPreMatch().ShiftInfo.ShiftID)
			if u.adapter.GetSmartBusPreMatch().ShiftInfo.RouteID != nil {
				comboId := *u.adapter.GetSmartBusPreMatch().ShiftInfo.RouteID
				resp.SetComboId(cast.ToInt64(comboId))
			}
		}
	}
	requireLevel, err := strconv.ParseInt(u.adapter.GetRequireLevel(), 10, 64)
	if err == nil {
		resp.RequireLevel = requireLevel
	}
	return resp
}

func (u *UnifiedPriceRender) getFeeMsg(_ context.Context) string {
	return dcmp.TranslateTemplate(u.FeeDetail.FeeMsg, map[string]string{
		"amount": strconv.FormatFloat(u.adapter.GetEstimateFee(), 'f', 2, 32),
	})
}

func (u *UnifiedPriceRender) getNoticeTitle(ctx context.Context) string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return u.FeeDetail.NoticeTitleScanInCar
	}

	// 非候补
	if PreMatchTypePreMatch == u.preMatchType {
		if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
			realEtp := preMatchInfo.EtpInfo.EtpTimeDuration - time.Now().Unix()
			if realEtp < DefaultEtp {
				realEtp = DefaultEtp
			}
			if realEtp < u.FeeDetail.EtpFormatThreshold {
				return dcmp.TranslateTemplate(u.FeeDetail.NoticeTitle, map[string]string{
					"amount": strconv.Itoa(int(math.Ceil(((float64)(realEtp)) / 60))),
				})
			} else {
				return time.Unix(preMatchInfo.EtpInfo.EtpTimeDuration, 0).Format("15:04")
			}
		}
		return dcmp.TranslateTemplate(u.FeeDetail.NoticeTitle, map[string]string{
			"amount": strconv.Itoa(int(math.Ceil(((float64)(u.adapter.GetSmartBusEtp())) / 60))),
		})
	}

	// 候补状态
	return u.FeeDetail.NoticeTitleNotp
}

func (u *UnifiedPriceRender) getNoticeSubtitle(ctx context.Context) string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return u.FeeDetail.NoticeSubtitleScanInCar
	}

	// 非候补
	if PreMatchTypePreMatch == u.preMatchType {
		return u.FeeDetail.NoticeSubtitle
	}

	//候补
	return u.FeeDetail.NoticeSubtitleNotp
}

func (u *UnifiedPriceRender) getPriceInfoDescList(ctx context.Context) []*proto.NewFormFeeDesc {
	return smart_bus.GetPriceInfoDescList(ctx, u.adapter)
}

func (u *UnifiedPriceRender) getNoticeText(ctx context.Context) *string {
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		preMatchData := u.adapter.GetSmartBusPreMatch()
		if preMatchData == nil || preMatchData.ShiftInfo == nil ||
			preMatchData.ShiftInfo.StartStation == nil || preMatchData.ShiftInfo.RouteID == nil {
			return nil
		}
		if val, ok := u.adapter.GetSmartBusExpressLineStationDetail()[*preMatchData.ShiftInfo.RouteID]; val != nil && ok {
			if val.RouteBasicInfo != nil {
				for _, station := range val.RouteBasicInfo.StationList {
					if station == nil {
						continue
					}
					if station.StationId == preMatchData.ShiftInfo.StartStation.StationID {
						return station.StationName
					}
				}
			}
		}
	}
	if PreMatchTypePreMatch == u.preMatchType {
		return proto.StrPtr(cast.ToString(u.startStationInfo["displayname"]))
	}
	return proto.StrPtr(u.FeeDetail.NoticeTextNotp)
}

func (u *UnifiedPriceRender) getNoticeSubtext(ctx context.Context) *string {
	var walkDist *int32
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		preMatchData := u.adapter.GetSmartBusPreMatch()
		if preMatchData == nil || preMatchData.ShiftInfo == nil || preMatchData.ShiftInfo.StartStation == nil {
			return nil
		}
		walkDist = &u.adapter.GetSmartBusPreMatch().ShiftInfo.StartStation.WalkDistance
	} else {
		walkDist = u.adapter.GetWalkDist()
	}

	// 判空操作
	if walkDist == nil || u.FeeDetail.WalkDistLeftThreshold == 0 || u.FeeDetail.WalkDistRightThreshold == 0 {
		return proto.StrPtr("")
	}
	if *walkDist < u.FeeDetail.WalkDistLeftThreshold || *walkDist >= u.FeeDetail.WalkDistRightThreshold {
		return proto.StrPtr("")
	}
	return proto.StrPtr(dcmp.TranslateTemplate(u.FeeDetail.NoticeSubtext, map[string]string{
		"walk_dist": cast.ToString(walkDist),
	}))
}

func (u *UnifiedPriceRender) getSubTag(ctx context.Context) *string {
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		return &u.FeeDetail.ExpressLineTag
	}
	return nil
}

func (u *UnifiedPriceRender) getConfirmSubtitle(ctx context.Context) *string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtdInfo == nil {
		return proto.StrPtr("")
	}
	if PreMatchTypePreMatch == u.preMatchType {
		if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
			tmpEtdLeftMargin := max(preMatchInfo.EtdInfo.EtdLeftMargin-time.Now().Unix(), 0)
			tmpEtdRightMargin := max(preMatchInfo.EtdInfo.EtdRightMargin-time.Now().Unix(), 0)
			return proto.StrPtr(dcmp.TranslateTemplate(u.FeeDetail.ConfirmSubtitle, map[string]string{
				"amount1": formatTime(tmpEtdLeftMargin),
				"amount2": formatTime(tmpEtdRightMargin),
			}))
		}
		return proto.StrPtr(dcmp.TranslateTemplate(u.FeeDetail.ConfirmSubtitle, map[string]string{
			"amount1": formatTime(preMatchInfo.EtdInfo.EtdLeftMargin),
			"amount2": formatTime(preMatchInfo.EtdInfo.EtdRightMargin),
		}))
	}
	return proto.StrPtr("")
}

func (u *UnifiedPriceRender) buildSmartBusStationInfo(station *CarpoolApi.BusBubbleStation, routeDetail *Prfs.CommonRouteInfo) *proto.SmartBusStationInfo {
	if routeDetail == nil || routeDetail.RouteBasicInfo == nil {
		return nil
	}
	for _, val := range routeDetail.RouteBasicInfo.StationList {
		if val == nil {
			continue
		}
		if station.StationID == val.StationId {
			res := &proto.SmartBusStationInfo{
				PoiId:        val.StationId,
				Lng:          cast.ToFloat64(val.Lng),
				Lat:          cast.ToFloat64(val.Lat),
				DisplayName:  cast.ToString(val.StationName),
				WalkDistance: &station.WalkDistance,
				WalkTime:     &station.WalkTime,
			}
			if val.ExtendInfo != nil && val.ExtendInfo.LinkId != nil {
				res.LinkId = *val.ExtendInfo.LinkId
			}
			return res
		}
	}
	return nil
}

func (u *UnifiedPriceRender) buildStartStationInfo(_ context.Context) (startStationInfo *proto.SmartBusStationInfo) {
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		preMatchData := u.adapter.GetSmartBusPreMatch()
		if preMatchData == nil || preMatchData.ShiftInfo == nil ||
			preMatchData.ShiftInfo.StartStation == nil || preMatchData.ShiftInfo.RouteID == nil {
			return nil
		}
		if val, ok := u.adapter.GetSmartBusExpressLineStationDetail()[*preMatchData.ShiftInfo.RouteID]; val != nil && ok {
			return u.buildSmartBusStationInfo(preMatchData.ShiftInfo.StartStation, val)
		}
	}

	//车上扫码场景
	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return map_station.ScanInCarWithTPStartStation(u.adapter.ProductInfoFull, u.adapter.BaseReqData)
	}

	//default strategy
	if PreMatchTypeNoCar == u.preMatchType || nil == u.startStationInfo {
		return
	}
	startStationInfo = &proto.SmartBusStationInfo{
		PoiId:       cast.ToString(u.startStationInfo["poi_id"]),
		Lat:         cast.ToFloat64(u.startStationInfo["lat"]),
		Lng:         cast.ToFloat64(u.startStationInfo["lng"]),
		DisplayName: cast.ToString(u.startStationInfo["displayname"]),
	}
	return startStationInfo
}
func (u *UnifiedPriceRender) buildEndStationInfo(_ context.Context) (startStationInfo *proto.SmartBusStationInfo) {
	if level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		preMatchData := u.adapter.GetSmartBusPreMatch()
		if preMatchData == nil || preMatchData.ShiftInfo == nil ||
			preMatchData.ShiftInfo.EndStation == nil || preMatchData.ShiftInfo.RouteID == nil {
			return nil
		}
		if val, ok := u.adapter.GetSmartBusExpressLineStationDetail()[*preMatchData.ShiftInfo.RouteID]; val != nil && ok {
			return u.buildSmartBusStationInfo(preMatchData.ShiftInfo.EndStation, val)
		}
	}
	return map_station.ScanInCarWithTPDestStation(u.adapter.ProductInfoFull, u.adapter.BaseReqData)
}

func (u *UnifiedPriceRender) buildSideParams(_ context.Context) (sideParams *proto.SideParam) {
	walkDist := u.adapter.GetWalkDist()
	return &proto.SideParam{
		WalkDist:     walkDist,
		PreMatchType: &u.preMatchType,
	}
}

func (u *UnifiedPriceRender) buildApproachStationInfo(_ context.Context) []*proto.SmartBusStationInfo {
	var res []*proto.SmartBusStationInfo
	var left, right int

	if !level_type.IsSmartBusExpressLine(u.adapter.GetLevelType()) {
		return nil
	}
	preMatchData := u.adapter.GetSmartBusPreMatch()
	if preMatchData == nil || preMatchData.ShiftInfo == nil || preMatchData.ShiftInfo.StartStation == nil ||
		preMatchData.ShiftInfo.EndStation == nil || preMatchData.ShiftInfo.RouteID == nil ||
		len(preMatchData.ShiftInfo.StationList) == 0 {
		return nil
	}
	if len(u.adapter.BaseReqData.CommonBizInfo.ExpressLineStationDetail) == 0 {
		return nil
	}

	comboId := *preMatchData.ShiftInfo.RouteID
	if val, ok := u.adapter.BaseReqData.CommonBizInfo.ExpressLineStationDetail[comboId]; ok && val != nil {
		if val.RouteBasicInfo == nil {
			return nil
		}
		// 用双指针找到起终点下标
		for index, stationInfo := range val.RouteBasicInfo.StationList {
			if stationInfo == nil {
				continue
			}
			if preMatchData.ShiftInfo.StartStation.StationID == stationInfo.StationId {
				left = index
			}
			if preMatchData.ShiftInfo.EndStation.StationID == stationInfo.StationId {
				right = index
			}
		}
		// 取起终点之间的作为途径点
		for index := left + 1; index < right; index++ {
			stationDetail := val.RouteBasicInfo.StationList[index]
			if stationDetail == nil {
				continue
			}
			newStation := &proto.SmartBusStationInfo{
				PoiId: stationDetail.StationId,
				Lat:   cast.ToFloat64(stationDetail.Lat),
				Lng:   cast.ToFloat64(stationDetail.Lng),
			}
			if stationDetail.StationName != nil {
				newStation.DisplayName = *stationDetail.StationName
			}
			if stationDetail.ExtendInfo != nil && stationDetail.ExtendInfo.LinkId != nil {
				newStation.LinkId = *stationDetail.ExtendInfo.LinkId
			}
			res = append(res, newStation)
		}
	}
	return res
}

func (u *UnifiedPriceRender) ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) {
	companyName, estimateTitle, seatRes := getCompanyNameAndSeat(ctx, baseReqData, products, u.SeparatePage.DefaultCompanyName, u.SeparatePage.Title)
	// 本次小巴需要画曲线，直接赋值1，最好加一个枚举
	resp.MapCurveInfo = &proto.MapCurveInfo{
		IsDrawCurve: 1,
		CurveType:   proto.Int32Ptr(2),
	}
	//地图视图类型
	BuildBestViewType(resp, baseReqData)

	//地图视图切换按钮
	BuildBestViewTypeSwitch(resp, baseReqData)

	resp.CarpoolSeatModule = seatRes
	resp.PageType = int16(baseReqData.CommonInfo.PageType)

	//发单按钮构建
	BuildNewOrderButton(resp, baseReqData, &u.SeparatePage.BaseExtraInfo, estimateTitle)

	if baseReqData.CommonInfo.FromType == ShowBackButton {
		resp.BackButton = &proto.BackButton{
			LeftIcon: u.SeparatePage.BackIcon,
			JumpTo:   u.SeparatePage.JumpLink,
		}
	}

	resp.DynamicEffectParams = &proto.DynamicEffectParams{}
	if len(resp.EstimateData) > 0 {
		resp.Subtitle = fmt.Sprintf(u.SeparatePage.Subtitle, companyName)
		resp.IsSupportMultiSelection = 0
		resp.FeeDetailUrl = u.SeparatePage.FeeDetailUrl
		if len(resp.EstimateData) == 1 && resp.EstimateData[0] != nil && resp.EstimateData[0].ExtraMap != nil && resp.EstimateData[0].ExtraMap.Etp == -1 {
			resp.DynamicEffectParams.MarkIcon = u.SeparatePage.MarkIconNotp
		} else {
			resp.DynamicEffectParams.MarkIcon = u.SeparatePage.MarkIcon
		}
		defaultSelected(resp)
		resp.DynamicEffectParams.SelectBgColor = &u.SeparatePage.SelectBgColor
		resp.PreMatchType = &u.preMatchType
	}

	if len(resp.EstimateData) == ShowEmptyImage {
		buildRespWithNoTP(resp, baseReqData, &u.SeparatePage.BaseExtraInfo)
	}

	//构建地图站牌：for 无TP
	FillMapStationWithoutTP(resp, baseReqData)

	//地图token
	BuildMapToken(resp, baseReqData)

	resp.OmegaInfo = buildOmegaData(ctx, baseReqData, resp)
	resp.PneworderParams = &proto.SmartBusNewOrderParam{Dchn: &baseReqData.CommonInfo.Dchn}
	resp.SmartBusPageVersion = proto.StrPtr(SmartBusPageVersionUnifiedPrice)
	resp.MinibusType = proto.Int32Ptr(minibus.SmartBus)
}

// GetRefreshInterval 获取刷新时间
func (u *UnifiedPriceRender) GetRefreshInterval() *int32 {
	refreshTime := apollo.GetConfigItem(consts.ApolloPassengerTransportFeatureConfig, consts.SmartBusEstimateAutoRefreshFile, consts.RefreshIntervalField)
	if refreshTime != "" && refreshTime != "0" {
		return util.Int32Ptr(cast.ToInt32(refreshTime))
	}

	return nil
}
