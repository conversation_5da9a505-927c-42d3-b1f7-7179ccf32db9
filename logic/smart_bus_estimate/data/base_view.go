package data

import (
	"context"
	"encoding/json"
	"time"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data/build_func/map_station"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/passenger-common/common/language"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"github.com/spf13/cast"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
)

type Render interface {
	Init(ctx context.Context)
	RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData
	ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull)
	GetRefreshInterval() *int32
}

const (
	ShowLeftImage  = 1
	ShowEmptyImage = 0
	ShowBackButton = 1

	ApolloNsSmartBusCommonConfig     = "smart_bus_common_config"
	ApolloNameProductToCompanyConfig = "product_to_company_config"

	PreMatchTypePreMatch         = 0
	PreMatchTypeNoCar            = 1
	PreMatchTypeStartDestOverlap = 2
	PreMatchTypeExpressLine      = 3

	//BestViewTypeStartPoint 地图区展示 起终点模式，1-起点模式
	BestViewTypeStartPoint = 1
	//BestViewTypeOverview 地图区展示 起终点模式，2-全览模式（端上逻辑，非2就是起点模式）
	BestViewTypeOverview = 2

	//EnableBestViewSelect 地图区按钮 是否展示起点-全览切换按钮，0-展示
	EnableBestViewSelect = 0
	//DisableBestViewSelect 地图区按钮 是否展示起点-全览切换按钮，1-不展示
	DisableBestViewSelect = 1
)

type BaseRender struct {
	adapter          *SmartBusAdapter
	preMatchType     int32
	tpPreMatchType   int32
	startStationInfo map[string]interface{}
}

type BaseExtraInfo struct {
	Title                                    string `json:"title"`
	Subtitle                                 string `json:"subtitle"`                                          //副标题，某某公司承运
	ConfirmButtonTitle                       string `json:"confirm_button_title"`                              //确认按钮标题
	ConfirmButtonTitleScanInCarNoneTpNeedPay string `json:"confirm_button_title_scan_in_car_none_tp_need_pay"` //扫码上车无TP，确认按钮标题
	MarkIcon                                 string `json:"mark_icon"`                                         //TP卡片上图标
	MarkIconNotp                             string `json:"mark_icon_notp"`                                    //无车时TP卡片上图标
	DefaultIcon                              string `json:"default_icon"`                                      //兜底图
	DefaultIconScanInCar                     string `json:"default_icon_scan_in_car"`                          //扫码上车兜底图
	DefaultText                              string `json:"default_text"`                                      //兜底文案
	DefaultTextScanInCar                     string `json:"default_text_scan_in_car"`                          //扫码上车兜底文案
	FeeDetailUrl                             string `json:"fee_detail_url"`                                    //费用详情链接
	BackIcon                                 string `json:"back_icon"`                                         //返回按钮图
	JumpLink                                 string `json:"jump_link"`                                         //跳转类型
	RetryButtonText                          string `json:"retry_button_text"`                                 //重试按钮文案
	SelectBgColor                            string `json:"select_bg_color"`                                   //选中背景颜色
	DefaultCompanyName                       string `json:"default_company_name"`                              //兜底公司名称
	ScanCodePayLink                          string `json:"scan_code_pay_link"`                                //扫码支付链接

	RouteNotMatchIcon                  string `json:"route_no_match_icon"`
	RouteNotMatchText                  string `json:"route_not_match_text"`
	StartDestOverlapIcon               string `json:"start_dest_overlap_icon"`
	StartDestOverlapText               string `json:"start_dest_overlap_text"`
	StartDestOverlapConfirmButtonTitle string `json:"start_dest_overlap_confirm_button_title"` //开始站点与目的站点重叠，确认按钮标题
}

type CompanyConfig struct {
	CompanyName   string `json:"company_name"`
	SelectSeatNum int32  `json:"select_seat_num"`
	EstimateTitle string `json:"estimate_title"`
}

func (b *BaseRender) Init() {}

func (b *BaseRender) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData {
	return nil
}

func (b *BaseRender) ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) {

}

func NewRender(ctx context.Context, baseReqData *models.BaseReqData) Render {
	pid := cast.ToString(baseReqData.PassengerInfo.PID)
	param := baseReqData.GetApolloParam()
	if ok, assign := apollo.FeatureExp(ctx, "wangyue_bubble", pid, param); ok {
		if assign.GetGroupName() == consts.TreatmentGroup {
			r := &UnifiedPriceRender{}
			r.Init(ctx)
			return r
		}
	}
	r := &OldVersionRender{}
	r.Init(ctx)
	return r
}

// 一些公共的方法
func formatTime(etd int64) string {
	t1 := time.Now().Unix() + etd
	res := time.Unix(t1, 0).Format("15:04")
	return res
}

// 根据第一个product获取商户名和座位数（因为配置在一个apollo就放一起读了）
func getCompanyNameAndSeat(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull, defaultName, defaultTitle string) (string, string, *proto.MiniBusCarpoolSeatModule) {
	// 座位文案配置和兜底
	seatRes := &proto.MiniBusCarpoolSeatModule{}
	seatDcmpConf := dcmp.GetDcmpContent(ctx, "smart_bus-carpool_seat_module", nil)
	err := json.Unmarshal([]byte(seatDcmpConf), &seatRes)
	if err != nil {
		return defaultName, defaultTitle, seatRes
	}
	// 从入参获取座位数
	if baseReqData.CommonBizInfo.CarpoolSeatNum > 0 {
		seatRes.SelectCount = baseReqData.CommonBizInfo.CarpoolSeatNum
	}
	// 如果没有配置返回兜底
	if len(products) == 0 {
		return defaultName, defaultTitle, checkSeatResult(seatRes)
	}
	config, err := ApolloSDK.GetConfigItem(ApolloNsSmartBusCommonConfig, ApolloNameProductToCompanyConfig, cast.ToString(products[0].Product.GetProductId()))
	if err != nil {
		return defaultName, defaultTitle, checkSeatResult(seatRes)
	}
	companyConfig := &CompanyConfig{}
	err = json.Unmarshal([]byte(config), &companyConfig)
	if err != nil {
		return defaultName, defaultTitle, checkSeatResult(seatRes)
	}
	seatRes.MaxCount = companyConfig.SelectSeatNum
	return companyConfig.CompanyName, companyConfig.EstimateTitle, checkSeatResult(seatRes)
}

func checkSeatResult(seatRes *proto.MiniBusCarpoolSeatModule) *proto.MiniBusCarpoolSeatModule {
	if seatRes.SelectCount > seatRes.MaxCount {
		seatRes.SelectCount = seatRes.MaxCount
	}
	return seatRes
}

func defaultSelected(resp *proto.MiniBusEstimateData) {
	isSelected := int16(0)
	if len(resp.EstimateData) > 0 {
		for i := 0; i < len(resp.EstimateData); i++ {
			isSelected = isSelected | resp.EstimateData[i].IsSelected
		}
		if isSelected == 0 {
			resp.EstimateData[0].IsSelected = 1
		}
	}
}

// buildOmegaData 构建omega埋点数据
func buildOmegaData(ctx context.Context, baseReqData *models.BaseReqData, resp *proto.MiniBusEstimateData) *proto.MiniBusOmegaInfo {
	params := map[string]interface{}{
		"from":         baseReqData.CommonInfo.FromType,
		"estimate_ids": getOmegaEstimateIds(ctx, resp),
		"dchn":         baseReqData.CommonInfo.Dchn,
		"card_number":  len(resp.EstimateData),
	}

	//车上扫码omega
	fillScanInCarOmega(baseReqData, resp, params)

	showOmega := &proto.ShowOmega{
		Key:    "wyc_lcar_pas_bubble_sw",
		Params: params,
	}
	return &proto.MiniBusOmegaInfo{
		ShowOmega: showOmega,
	}
}

// 从resp里面获取eid
func getOmegaEstimateIds(_ context.Context, resp *proto.MiniBusEstimateData) string {
	omegaEstimateIds := make([]string, 0)
	if resp == nil || len(resp.EstimateData) == 0 {
		return ""
	}
	for _, v := range resp.EstimateData {
		omegaEstimateIds = append(omegaEstimateIds, v.GetEstimateId())

	}
	val, err := json.Marshal(omegaEstimateIds)
	if err != nil {
		return ""
	}
	return string(val)
}

// AdjustSelected 默认勾选第一个
func AdjustSelected(estimateDatas []*proto.EstimateFromData) {
	if len(estimateDatas) > 0 {
		estimateDatas[0].IsSelected = 1
	}
}

func fillScanInCarOmega(baseReqData *models.BaseReqData, resp *proto.MiniBusEstimateData, params map[string]interface{}) {
	if page_type.PageTypeSmartBusEstimate != baseReqData.CommonInfo.PageType {
		return
	}

	if from_type.FromTypeFromTypeScanInCar != baseReqData.CommonInfo.FromType {
		return
	}

	params["match_results"] = util2.Ternary(len(resp.EstimateData) == 0, 2, 1).(int) //1- 匹配成功、 2-不顺路（无TP）

	if len(resp.EstimateData) == 0 {
		params["is_endstation"] = util2.Ternary(nil == resp.DestStationInfo, 0, 1).(int)
	}

	if len(resp.EstimateData) != 0 {
		params["is_endstation"] = util2.Ternary(nil == resp.EstimateData[0].DestStationInfo, 0, 1).(int)
	}

}

func BuildBestViewType(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData) {
	//公交小巴不展示全览，直接赋值0，最好加一个枚举
	resp.BestViewType = BestViewTypeStartPoint
	//车上扫码场景：全览
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType || rpc_process.ErrPreMatchStartDestOverlap == baseReqData.CommonBizInfo.PreMatchTPErrNo {
		resp.BestViewType = BestViewTypeOverview
	}
	// 快线模式: 全览
	if len(resp.EstimateData) > 0 {
		if resp.EstimateData[0] == nil {
			return
		}
		if resp.EstimateData[0].PreMatchType == PreMatchTypeExpressLine {
			resp.BestViewType = BestViewTypeOverview
		}
	}
}

func BuildBestViewTypeSwitch(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData) {
	//正常场景 展示全览按钮
	resp.DisableBestViewSelect = EnableBestViewSelect
	//车上扫码场景 和 起终点过近：屏蔽全览按钮
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType || rpc_process.ErrPreMatchStartDestOverlap == baseReqData.CommonBizInfo.PreMatchTPErrNo {
		resp.DisableBestViewSelect = DisableBestViewSelect
	}
}

// buildRespWithNoTP 无TP场景构建兜底返回值
func buildRespWithNoTP(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, conf *BaseExtraInfo) {
	// 1.起终点过近（包含补票&非补票）
	if rpc_process.ErrPreMatchStartDestOverlap == baseReqData.CommonBizInfo.PreMatchTPErrNo {
		resp.DynamicEffectParams.DefaultIcon = conf.StartDestOverlapIcon
		resp.DynamicEffectParams.DefaultText = conf.StartDestOverlapText
		resp.PreMatchType = proto.Int32Ptr(PreMatchTypeStartDestOverlap)
		return
	}
	// 2.不顺路&补票
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		resp.DynamicEffectParams.DefaultIcon = conf.DefaultIconScanInCar
		resp.DynamicEffectParams.DefaultText = conf.DefaultTextScanInCar
		return
	}
	// 3.默认兜底场景
	resp.DynamicEffectParams.RetryButtonText = conf.RetryButtonText
	resp.DynamicEffectParams.DefaultIcon = conf.DefaultIcon
	resp.DynamicEffectParams.DefaultText = conf.DefaultText
}

// BuildMapToken ..
func BuildMapToken(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData) {
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		resp.TokenInfo = &proto.TokenInfo{
			MapinfoStartCacheToken: baseReqData.AreaInfo.MapInfoStartCacheToken, //从预估参数回传
			MapinfoDestCacheToken:  baseReqData.AreaInfo.MapInfoDestCacheToken,  //从预估参数回传
		}

		return
	}

	resp.TokenInfo = &proto.TokenInfo{
		MapinfoStartCacheToken: baseReqData.CommonBizInfo.TokenInfo.MapinfoStartCacheToken,
		MapinfoDestCacheToken:  baseReqData.CommonBizInfo.TokenInfo.MapinfoDestCacheToken,
	}
}

// FillMapStationWithoutTP 无TP时 地图站牌信息
func FillMapStationWithoutTP(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData) {
	if 0 != len(resp.EstimateData) {
		return
	}

	//车上扫码场景 & 不存在tp「即：当前行程不顺路」
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		map_station.FillScanInCarWithoutTPStartDestStation(resp, baseReqData)
	}

}

func BuildNewOrderButton(resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, conf *BaseExtraInfo, estimateTitle string) {
	resp.ConfirmButton = &proto.ConfirmButton{
		Title: conf.ConfirmButtonTitle + estimateTitle,
	}

	//车上扫码场景 & 不存在tp「即：当前行程不顺路」，下发补款h5 link
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType && 0 == len(resp.EstimateData) && nil != baseReqData.CommonBizInfo.SmartBusData.SmartBusExtData {
		driverId := baseReqData.DriverInfo.DriverId                                   //no check
		productId := baseReqData.CommonBizInfo.SmartBusData.SmartBusExtData.ProductID //may be empty
		plateId := baseReqData.CommonBizInfo.SmartBusData.SmartBusExtData.PlateNo     //may be empty
		city := cast.ToString(baseReqData.AreaInfo.Area)

		ShowPayButton := 0 != len(productId) && 0 != len(plateId)

		if ShowPayButton {
			linkUrl := language.ReplaceTags(conf.ScanCodePayLink, map[string]string{
				"driver_id":  cast.ToString(driverId),
				"product_id": productId,
				"plate_id":   plateId,
				"city_id":    city,
			})

			resp.ConfirmButton.LinkUrl = util.StringPtr(linkUrl)
			resp.ConfirmButton.Title = conf.ConfirmButtonTitleScanInCarNoneTpNeedPay
		}

		if !ShowPayButton {
			resp.ConfirmButton.Title = conf.ConfirmButtonTitleScanInCarNoneTpNeedPay
		}

	}
}
