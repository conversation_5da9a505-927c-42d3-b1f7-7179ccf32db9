package data

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/smart_bus"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func Test_RenderByProduct_old(t *testing.T) {
	convey.Convey("Test_RenderByProduct", t, func() {
		// 初始化测试所需的数据
		ctx := context.Background()

		// 创建 UnifiedPriceRender 实例
		oldVersionRender := &OldVersionRender{}

		// 创建 ProductInfoFull 实例
		product := &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					City:                   1,
					District:               "010",
					FromCounty:             110108,
					ToCounty:               110114,
					FromCityName:           "北京市",
					ToCityName:             "北京市",
					FromLat:                40.06770268197495,
					FromLng:                116.27487162642947,
					FromAddress:            "北京市海淀区唐家岭北环路6号院1号楼",
					FromName:               "滴滴新橙海大厦-北门(便利店旁)",
					FromPoiID:              "1701534285283987456_wx4evyher0_90002504934100_100",
					FromPoiType:            "",
					ToLat:                  40.07132,
					ToLng:                  116.31932743377136,
					ToAddress:              "地铁13B号线;地铁13号线",
					ToName:                 "龙泽地铁站A口",
					ToPoiID:                "2000000000000019690_3886879904103399424_9000180146096_200",
					ToPoiType:              "",
					CurLng:                 116.274712566,
					CurLat:                 40.067380885,
					Lng:                    0,
					Lat:                    0,
					Area:                   1,
					ToArea:                 1,
					MapType:                "",
					AbstractDistrict:       "010,110108",
					StartingName:           "滴滴新橙海大厦-北门(便利店旁)",
					DestName:               "龙泽地铁站A口",
					FromCountyName:         "海淀区",
					ToCountyName:           "昌平区",
					StopoverPointInfo:      nil,
					TripCountry:            "CN",
					MetroFenceID:           0,
					MapInfoCacheToken:      "",
					MapInfoStartCacheToken: "70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1",
					MapInfoDestCacheToken:  "70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2",
					ChooseFSearchid:        "65d5fe8567d78c630000455d180639d3",
					ChooseTSearchid:        "6a040d0b67d78c860000455d180639ea",
					FromCityInfo:           "",
					DistrictV2:             nil,
				},
				CommonInfo: models.CommonInfo{
					AppVersion:              "7.0.6",
					MenuID:                  "dache_anycar",
					PageType:                51,
					SourceID:                0,
					AccessKeyID:             2,
					OriginID:                0,
					CallCarType:             0,
					Channel:                 20,
					ScreenPixels:            "",
					ScreenScale:             0,
					OrderType:               0,
					PlatformType:            0,
					Lang:                    "zh-CN",
					ClientType:              1,
					Imei:                    "",
					AgentType:               "",
					TerminalID:              0,
					RouteID:                 0,
					From:                    "",
					DepartureTime:           **********,
					DepartureRange:          nil,
					ActivityID:              0,
					PaymentsType:            2,
					LuxurySelectCarlevels:   "",
					LuxurySelectDriver:      "",
					AirportType:             0,
					CallCarPhone:            "",
					UserType:                0,
					BusinessOption:          "",
					TrafficNumber:           "",
					TrafficDepTime:          "",
					AirportId:               0,
					FlightDepCode:           "",
					FlightDepTerminal:       "",
					CompareEstimateTraceId:  "",
					CompareEstimateId:       "",
					TabList:                 "",
					TabId:                   "",
					StopoverPoints:          nil,
					PreferenceFilterId:      0,
					ChooseFSearchID:         "",
					ChooseTSearchID:         "",
					SourceChannel:           "",
					PreferredRouteId:        "",
					Xpsid:                   "",
					XpsidRoot:               "",
					EstimateStyleType:       0,
					PricingBoxData:          nil,
					EventKey:                nil,
					IsScanCode:              "",
					ScanCodeShiftId:         "",
					MainEstimateTraceId:     "",
					Dchn:                    "adp3m5j",
					FromType:                2,
					DisableUnione:           false,
					SelectedCarlevel:        "",
					SixSeatSelectedCarlevel: "",
					DesignatedDriver:        "",
					SixSeatDesignatedDriver: "",
					GuideTraceId:            "",
					FontScaleType:           0,
					IsEstimateV2:            false,
					IsDRN:                   false,
					RiskCode:                0,
				},
				PassengerInfo:       models.PassengerInfo{},
				DriverInfo:          models.DriverInfo{},
				CommonBizInfo:       models.CommonBizInfo{},
				SendOrder:           models.SendOrder{},
				RawUserSelectOption: &models.UserOption{},
			},
			Product: &models.Product{
				EstimateID:            "a82a1221e8d9676145f2635e3dd1c410",
				ProductCategory:       1510,
				OrderType:             0,
				ProductID:             8999,
				BusinessID:            8999,
				RequireLevel:          "23999",
				RequireLevelInt:       23999,
				CarpoolType:           12,
				LevelType:             110,
				SpaciousCarAlliance:   0,
				ComboType:             4,
				SceneType:             0,
				IsSpecialPrice:        false,
				CarpoolPriceType:      0,
				IsDualCarpoolPrice:    false,
				AirportType:           0,
				RailwayType:           0,
				HotelType:             0,
				StationServiceControl: 0,
				OType:                 0,
				PaymentsType:          0,
				SubGroupId:            0,
				LongRentType:          0,
				CommercialtType:       0,
				EmergencyServiceType:  0,
				RouteType:             0,
				ExamType:              0,
				IsPickOnTime:          0,
				RouteID:               "",
				ShiftID:               "",
				BizInfo: &models.PrivateBizInfo{
					FormShowType:               0,
					ComboID:                    0,
					CarpoolSeatNum:             1,
					MaxCarpoolSeatNum:          0,
					TimeSpan:                   nil,
					DepartureRange:             nil,
					DepartureTime:              0,
					MatchRoutes:                nil,
					RouteType:                  0,
					RouteInfo:                  nil,
					Ranking:                    0,
					WaitTime:                   0,
					CompensationInfo:           nil,
					CarpoolRouteInfo:           nil,
					CarpoolCommuteCard:         nil,
					CustomFeatureList:          nil,
					TaxiSps:                    nil,
					IsTaxiPeakFeeUserSelected:  false,
					IsSwitchIntel:              false,
					IsHoliday:                  false,
					PinchecheEtsInfo:           nil,
					InviteInfo:                 nil,
					CheckStatus:                0,
					VcardData:                  nil,
					UfsFeatures:                nil,
					UfsTripCloudAuthBusinessID: nil,
					UfsWycAuthBusinessID:       nil,
					IsButianNeedAuth:           false,
					IsNeedDefaultAuth:          false,
					NeedAuthList:               nil,
					AthenaEstimateEtpEtdInfo:   nil,
					MiniBusDataPrivate: models.MiniBusDataPrivate{
						MiniBusPreMatch:  nil,
						MiniBusDistLimit: 0,
					},
					WaitMinuteSendEmptyCarTime: 0,
					IsFission:                  false,
					PetsInfo:                   nil,
					SendOrderId:                0,
					SmartBusDataPrivate: models.SmartBusDataPrivate{
						SmartBusPreMatch: &CarpoolApi.PrematchMiniBusRes{
							ErrorCode: 0,
							ErrorMsg:  "OK",
							ProductInfo: &CarpoolApi.ProductType{
								ProductID:    proto.StrPtr("8999"),
								ComboType:    proto.Int16Ptr(4),
								RequireLevel: proto.StrPtr("23999"),
								OrderNTuple: map[string]string{
									"estimate_id":           "a82a1221e8d9676145f2635e3dd1c410",
									"invitation_type":       "0",
									"level_type":            "110",
									"require_level":         "23999",
									"combo_type":            "4",
									"route_type":            "0",
									"carpool_type":          "12",
									"order_type":            "0",
									"product_id":            "8999",
									"menu_id":               "dache_anycar",
									"is_dual_carpool_price": "",
									"carpool_price_type":    "0",
								},
							},
							EtdInfo: &CarpoolApi.CarpoolEtdInfo{
								EtdLeftMargin:  1521,
								EtdRightMargin: 1881,
							},
							EtpInfo: &CarpoolApi.CarpoolEtpInfo{
								EtpTimeDuration: 60,
							},
							MapinfoCacheToken:      proto.StrPtr("70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1-70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2"),
							MapinfoStartCacheToken: proto.StrPtr("70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1"),
							MapinfoDestCacheToken:  proto.StrPtr("70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2"),
						},
						WalkDist: nil,
					},
					GuideStationBusData: models.GuideStationBusData{
						IsBestShift:              0,
						OptimalShiftDistance:     0,
						IsIntercitySurpriseAlone: false,
						DepartureRangeStr:        "",
					},
					ServiceFeeInfo:   "",
					OverseaExtraInfo: nil,
				},
			},
		}
		product.SetHu(&PriceApi.EstimateNewFormData{
			EstimateId:      "a82a1221e8d9676145f2635e3dd1c410",
			ProductCategory: 1510,
			PaymentInfo: &PriceApi.EstimateNewFormPaymentInfo{
				DefaultPayType: 2,
				Payment: []*PriceApi.PaymentElem{
					&PriceApi.PaymentElem{
						PayType:          2,
						BusinessConstSet: 0,
						MixedPayType:     0,
						ChannelName:      proto.StrPtr("个人支付"),
						IsSelected:       proto.Int32Ptr(1),
						NeedSeparate:     proto.Int32Ptr(0),
						Disabled:         proto.Int32Ptr(0),
					},
				},
			},
			BillInfo: &PriceApi.EstimateNewFormBillInfo{
				EstimateId:                       "a82a1221e8d9676145f2635e3dd1c410",
				DriverMetre:                      9838,
				DriverMinute:                     20,
				RouteIdList:                      make([]string, 0, 0),
				Currency:                         "CNY",
				DynamicTotalFee:                  4,
				TotalFee:                         4,
				PreTotalFee:                      0,
				TotalFeeWithoutDiscount:          0,
				CapPrice:                         4,
				CountPriceType:                   0,
				DynamicDiffPrice:                 0,
				DynamicTimes:                     0,
				DynamicMemberReduce:              0,
				IsHitDynamicCapping:              proto.BoolPtr(false),
				DynamicPriceWithoutMemberCapping: proto.Float64Ptr(0),
				DynamicInfo:                      nil,
				TaxiSpDiscountFee:                0,
				InfoFee:                          0,
				HighwayFee:                       0,
				CrossCityFee:                     0,
				DesignatedDriverFee:              0,
				RebookServiceFee:                 0,
				BasicTotalFee:                    4,
				PricePrivilegeType:               proto.Int32Ptr(0),
				TripCloudDiscountFee:             0,
				TicketSeatInfo:                   "",
				DupsDynamicRaise:                 0,
				DupsDynamicTimes:                 0,
				DynamicCapping:                   0,
				DynamicMinimum:                   0,
			},
			DiscountSet:            &PriceApi.EstimateNewFormDiscountSet{},
			EstimateFee:            4,
			PersonalEstimateFee:    4,
			ExactEstimateFee:       4,
			CombineEstimateFee:     4,
			CombineDynamicTotalFee: 4,
			ExternalEid:            "",
			ExtendList:             nil,
		})

		//mock方法
		patch1 := gomonkey.ApplyFunc(smart_bus.GetPriceInfoDescList, func() []*proto.NewFormFeeDesc {
			return []*proto.NewFormFeeDesc{}
		})
		defer patch1.Reset()

		// 测试正常情况：product 和 FeeDetail 都不为空
		resp := oldVersionRender.RenderByProduct(ctx, product)
		convey.So(resp, convey.ShouldNotBeNil)
	})
}

func Test_ExtendDataRender_old(t *testing.T) {
	convey.Convey("Test_ExtendDataRender_old", t, func() {
		// 初始化测试数据
		ctx := context.Background()

		// 创建 BaseReqData 实例
		baseReqData := &models.BaseReqData{
			CommonInfo: models.CommonInfo{
				PageType: page_type.PageTypeSmartBusEstimate,
				FromType: 1,
			},
			CommonBizInfo: models.CommonBizInfo{
				MiniBusData: models.MiniBusData{
					TokenInfo: &models.TokenInfo{
						MapinfoStartCacheToken: "70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1",
						MapinfoDestCacheToken:  "70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2",
					},
				},
			},
			AreaInfo: models.AreaInfo{
				Area: 1,
			},
		}

		// 创建 ProductInfoFull 实例
		product := &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					City:                   1,
					District:               "010",
					FromCounty:             110108,
					ToCounty:               110114,
					FromCityName:           "北京市",
					ToCityName:             "北京市",
					FromLat:                40.06770268197495,
					FromLng:                116.27487162642947,
					FromAddress:            "北京市海淀区唐家岭北环路6号院1号楼",
					FromName:               "滴滴新橙海大厦-北门(便利店旁)",
					FromPoiID:              "1701534285283987456_wx4evyher0_90002504934100_100",
					FromPoiType:            "",
					ToLat:                  40.07132,
					ToLng:                  116.31932743377136,
					ToAddress:              "地铁13B号线;地铁13号线",
					ToName:                 "龙泽地铁站A口",
					ToPoiID:                "2000000000000019690_3886879904103399424_9000180146096_200",
					ToPoiType:              "",
					CurLng:                 116.274712566,
					CurLat:                 40.067380885,
					Lng:                    0,
					Lat:                    0,
					Area:                   1,
					ToArea:                 1,
					MapType:                "",
					AbstractDistrict:       "010,110108",
					StartingName:           "滴滴新橙海大厦-北门(便利店旁)",
					DestName:               "龙泽地铁站A口",
					FromCountyName:         "海淀区",
					ToCountyName:           "昌平区",
					StopoverPointInfo:      nil,
					TripCountry:            "CN",
					MetroFenceID:           0,
					MapInfoCacheToken:      "",
					MapInfoStartCacheToken: "70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1",
					MapInfoDestCacheToken:  "70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2",
					ChooseFSearchid:        "65d5fe8567d78c630000455d180639d3",
					ChooseTSearchid:        "6a040d0b67d78c860000455d180639ea",
					FromCityInfo:           "",
					DistrictV2:             nil,
				},
				CommonInfo: models.CommonInfo{
					AppVersion:              "7.0.6",
					MenuID:                  "dache_anycar",
					PageType:                51,
					SourceID:                0,
					AccessKeyID:             2,
					OriginID:                0,
					CallCarType:             0,
					Channel:                 20,
					ScreenPixels:            "",
					ScreenScale:             0,
					OrderType:               0,
					PlatformType:            0,
					Lang:                    "zh-CN",
					ClientType:              1,
					Imei:                    "",
					AgentType:               "",
					TerminalID:              0,
					RouteID:                 0,
					From:                    "",
					DepartureTime:           **********,
					DepartureRange:          nil,
					ActivityID:              0,
					PaymentsType:            2,
					LuxurySelectCarlevels:   "",
					LuxurySelectDriver:      "",
					AirportType:             0,
					CallCarPhone:            "",
					UserType:                0,
					BusinessOption:          "",
					TrafficNumber:           "",
					TrafficDepTime:          "",
					AirportId:               0,
					FlightDepCode:           "",
					FlightDepTerminal:       "",
					CompareEstimateTraceId:  "",
					CompareEstimateId:       "",
					TabList:                 "",
					TabId:                   "",
					StopoverPoints:          nil,
					PreferenceFilterId:      0,
					ChooseFSearchID:         "",
					ChooseTSearchID:         "",
					SourceChannel:           "",
					PreferredRouteId:        "",
					Xpsid:                   "",
					XpsidRoot:               "",
					EstimateStyleType:       0,
					PricingBoxData:          nil,
					EventKey:                nil,
					IsScanCode:              "",
					ScanCodeShiftId:         "",
					MainEstimateTraceId:     "",
					Dchn:                    "adp3m5j",
					FromType:                2,
					DisableUnione:           false,
					SelectedCarlevel:        "",
					SixSeatSelectedCarlevel: "",
					DesignatedDriver:        "",
					SixSeatDesignatedDriver: "",
					GuideTraceId:            "",
					FontScaleType:           0,
					IsEstimateV2:            false,
					IsDRN:                   false,
					RiskCode:                0,
				},
				PassengerInfo: models.PassengerInfo{},
				DriverInfo:    models.DriverInfo{},
				CommonBizInfo: models.CommonBizInfo{
					MiniBusData: models.MiniBusData{
						TokenInfo: &models.TokenInfo{
							MapinfoStartCacheToken: "70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1",
							MapinfoDestCacheToken:  "70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2",
						},
					},
				},
				SendOrder:           models.SendOrder{},
				RawUserSelectOption: &models.UserOption{},
			},
			Product: &models.Product{
				EstimateID:            "a82a1221e8d9676145f2635e3dd1c410",
				ProductCategory:       1510,
				OrderType:             0,
				ProductID:             8999,
				BusinessID:            8999,
				RequireLevel:          "23999",
				RequireLevelInt:       23999,
				CarpoolType:           12,
				LevelType:             110,
				SpaciousCarAlliance:   0,
				ComboType:             4,
				SceneType:             0,
				IsSpecialPrice:        false,
				CarpoolPriceType:      0,
				IsDualCarpoolPrice:    false,
				AirportType:           0,
				RailwayType:           0,
				HotelType:             0,
				StationServiceControl: 0,
				OType:                 0,
				PaymentsType:          0,
				SubGroupId:            0,
				LongRentType:          0,
				CommercialtType:       0,
				EmergencyServiceType:  0,
				RouteType:             0,
				ExamType:              0,
				IsPickOnTime:          0,
				RouteID:               "",
				ShiftID:               "",
				BizInfo: &models.PrivateBizInfo{
					FormShowType:               0,
					ComboID:                    0,
					CarpoolSeatNum:             1,
					MaxCarpoolSeatNum:          0,
					TimeSpan:                   nil,
					DepartureRange:             nil,
					DepartureTime:              0,
					MatchRoutes:                nil,
					RouteType:                  0,
					RouteInfo:                  nil,
					Ranking:                    0,
					WaitTime:                   0,
					CompensationInfo:           nil,
					CarpoolRouteInfo:           nil,
					CarpoolCommuteCard:         nil,
					CustomFeatureList:          nil,
					TaxiSps:                    nil,
					IsTaxiPeakFeeUserSelected:  false,
					IsSwitchIntel:              false,
					IsHoliday:                  false,
					PinchecheEtsInfo:           nil,
					InviteInfo:                 nil,
					CheckStatus:                0,
					VcardData:                  nil,
					UfsFeatures:                nil,
					UfsTripCloudAuthBusinessID: nil,
					UfsWycAuthBusinessID:       nil,
					IsButianNeedAuth:           false,
					IsNeedDefaultAuth:          false,
					NeedAuthList:               nil,
					AthenaEstimateEtpEtdInfo:   nil,
					MiniBusDataPrivate: models.MiniBusDataPrivate{
						MiniBusPreMatch:  nil,
						MiniBusDistLimit: 0,
					},
					WaitMinuteSendEmptyCarTime: 0,
					IsFission:                  false,
					PetsInfo:                   nil,
					SendOrderId:                0,
					SmartBusDataPrivate: models.SmartBusDataPrivate{
						SmartBusPreMatch: &CarpoolApi.PrematchMiniBusRes{
							ErrorCode: 0,
							ErrorMsg:  "OK",
							ProductInfo: &CarpoolApi.ProductType{
								ProductID:    proto.StrPtr("8999"),
								ComboType:    proto.Int16Ptr(4),
								RequireLevel: proto.StrPtr("23999"),
								OrderNTuple: map[string]string{
									"estimate_id":           "a82a1221e8d9676145f2635e3dd1c410",
									"invitation_type":       "0",
									"level_type":            "110",
									"require_level":         "23999",
									"combo_type":            "4",
									"route_type":            "0",
									"carpool_type":          "12",
									"order_type":            "0",
									"product_id":            "8999",
									"menu_id":               "dache_anycar",
									"is_dual_carpool_price": "",
									"carpool_price_type":    "0",
								},
							},
							EtdInfo: &CarpoolApi.CarpoolEtdInfo{
								EtdLeftMargin:  1521,
								EtdRightMargin: 1881,
							},
							EtpInfo: &CarpoolApi.CarpoolEtpInfo{
								EtpTimeDuration: 60,
							},
							MapinfoCacheToken:      proto.StrPtr("70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1-70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2"),
							MapinfoStartCacheToken: proto.StrPtr("70368747142053_1701534285283987456_wx4evyher0_90002504934100_100_17421794282377569_eyJwb2lfaWQiOiIxNzAxNTM0Mjg1MjgzOTg3NDU2X3d4NGV2eWhlcjBfOTAwMDI1MDQ5MzQxMDBfMTAwIiwiZGlzcGxheW5hbWUiOiLmu7Tmu7TmlrDmqZnmtbflpKfljqYt5YyX6ZeoKOS.v.WIqeW6l.aXgSkiLCJhZGRyZXNzIjoi5YyX5Lqs5biC5rW35reA5Yy65ZSQ5a625bKt5YyX546v6LevNuWPt.mZojHlj7fmpbwiLCJsbmciOjExNi4yNzQ4NzE2MjY0Mjk0NywibGF0Ijo0MC4wNjc3MDI2ODE5NzQ5NSwibGlua19pZCI6IjkwMDAyNTA0OTM0MTAwIiwiaXNfc2hvd19zdGF0aW9uIjp0cnVlLCJtaW5pX2J1c19yYWRpdXMiOjB9_1"),
							MapinfoDestCacheToken:  proto.StrPtr("70368747142053_2000000000000019690_3886879904103399424_9000180146096_200_17421794631676264_eyJwb2lfaWQiOiIyMDAwMDAwMDAwMDAwMDE5NjkwXzM4ODY4Nzk5MDQxMDMzOTk0MjRfOTAwMDE4MDE0NjA5Nl8yMDAiLCJkaXNwbGF5bmFtZSI6Ium.meazveWcsOmTgeermUHlj6MiLCJhZGRyZXNzIjoi5Zyw6ZOBMTNC5Y.357q~O.WcsOmTgTEz5Y.357q~IiwibG5nIjoxMTYuMzE5MzI3NDMzNzcxMzYsImxhdCI6NDAuMDcxMzIsImxpbmtfaWQiOiI5MDAwMTgwMTQ2MDk2MSIsImlzX3Nob3dfc3RhdGlvbiI6dHJ1ZSwibWluaV9idXNfcmFkaXVzIjowfQ_2"),
						},
						WalkDist: nil,
					},
					GuideStationBusData: models.GuideStationBusData{
						IsBestShift:              0,
						OptimalShiftDistance:     0,
						IsIntercitySurpriseAlone: false,
						DepartureRangeStr:        "",
					},
					ServiceFeeInfo:   "",
					OverseaExtraInfo: nil,
				},
			},
		}
		product.SetHu(&PriceApi.EstimateNewFormData{
			EstimateId:      "a82a1221e8d9676145f2635e3dd1c410",
			ProductCategory: 1510,
			PaymentInfo: &PriceApi.EstimateNewFormPaymentInfo{
				DefaultPayType: 2,
				Payment: []*PriceApi.PaymentElem{
					&PriceApi.PaymentElem{
						PayType:          2,
						BusinessConstSet: 0,
						MixedPayType:     0,
						ChannelName:      proto.StrPtr("个人支付"),
						IsSelected:       proto.Int32Ptr(1),
						NeedSeparate:     proto.Int32Ptr(0),
						Disabled:         proto.Int32Ptr(0),
					},
				},
			},
			BillInfo: &PriceApi.EstimateNewFormBillInfo{
				EstimateId:                       "a82a1221e8d9676145f2635e3dd1c410",
				DriverMetre:                      9838,
				DriverMinute:                     20,
				RouteIdList:                      make([]string, 0, 0),
				Currency:                         "CNY",
				DynamicTotalFee:                  4,
				TotalFee:                         4,
				PreTotalFee:                      0,
				TotalFeeWithoutDiscount:          0,
				CapPrice:                         4,
				CountPriceType:                   0,
				DynamicDiffPrice:                 0,
				DynamicTimes:                     0,
				DynamicMemberReduce:              0,
				IsHitDynamicCapping:              proto.BoolPtr(false),
				DynamicPriceWithoutMemberCapping: proto.Float64Ptr(0),
				DynamicInfo:                      nil,
				TaxiSpDiscountFee:                0,
				InfoFee:                          0,
				HighwayFee:                       0,
				CrossCityFee:                     0,
				DesignatedDriverFee:              0,
				RebookServiceFee:                 0,
				BasicTotalFee:                    4,
				PricePrivilegeType:               proto.Int32Ptr(0),
				TripCloudDiscountFee:             0,
				TicketSeatInfo:                   "",
				DupsDynamicRaise:                 0,
				DupsDynamicTimes:                 0,
				DynamicCapping:                   0,
				DynamicMinimum:                   0,
			},
			DiscountSet:            &PriceApi.EstimateNewFormDiscountSet{},
			EstimateFee:            4,
			PersonalEstimateFee:    4,
			ExactEstimateFee:       4,
			CombineEstimateFee:     4,
			CombineDynamicTotalFee: 4,
			ExternalEid:            "",
			ExtendList:             nil,
		})

		products := []*biz_runtime.ProductInfoFull{product}

		// 创建 UnifiedPriceRender 实例
		oldVersionRender := &OldVersionRender{
			BaseRender: BaseRender{},
		}

		// 创建 MiniBusEstimateData 实例
		resp := &proto.MiniBusEstimateData{
			EstimateData: make([]*proto.EstimateFromData, 0),
		}

		//mock方法
		patch := gomonkey.ApplyFunc(dcmp.GetDcmpContent, func() string {
			return "{\"icon\":\"https://s3-gz01.didistatic.com/packages-mait/img/wHiYSPHQNQ1741845733334.png\",\"title\":\"智能小巴公交专线\",\"subtitle\":\"该线路由%s承运\",\"confirm_button_title\":\"确认呼叫\",\"confirm_button_title_scan_in_car_none_tp_need_pay\":\"已与司机协商，去支付车费\",\"confirm_button_subtitle\":\"\",\"confirm_button_toast_scan_in_car\":\"当前不支持发单\",\"show_h5\":\"\",\"confirm_h5\":\"\",\"mark_icon\":\"https://dpubstatic.udache.com/static/dpubimg/ZI9r8-B-rs6B7oC57Dwne.png\",\"mark_icon_notp\":\"https://dpubstatic.udache.com/static/dpubimg/jMjZ8nfdXsaSjZBb7sEEF.png\",\"back_ground_icon\":\"\",\"default_icon\":\"https://dpubstatic.udache.com/static/dpubimg/0A2J776ZZFRJeF-c8F5wh.png\",\"default_icon_scan_in_car\":\"https://view.didistatic.com/static/dcms/144p2rsypam608avuj_315x315.png\",\"default_text\":\"暂时没有找到车辆，稍后刷新试试\",\"default_text_scan_in_car\":\"车辆当前行程与您的目的地不顺路，您可与司机协商是否继续乘坐，或选择就近下车\",\"fee_detail_url\":\"https://page.udache.com/passenger/apps/estimate-v4/index.html\",\"back_icon\":\"https://img-hxy021.didistatic.com/static/starimg/img/ZYuOlo4WYq1693323367142.png\",\"jump_link\":\"normal\",\"retry_button_text\":\"刷新\",\"select_bg_color\":\"#FF6435\",\"default_company_name\":\"公交集团\",\"route_not_match_icon\":\"https://dpubstatic.udache.com/static/dpubimg/0A2J776ZZFRJeF-c8F5wh.png\",\"route_not_match_text\":\"车辆当前行程与您的目的地不顺路，您可与司机协商是否继续乘坐，或选择就近下车\",\"start_dest_overlap_icon\":\"https://dpubstatic.udache.com/static/dpubimg/0A2J776ZZFRJeF-c8F5wh.png\",\"start_dest_overlap_text\":\"当前发单起终点距离过近，请修改您的【起点】或【终点】后再尝试发单\",\"start_dest_overlap_text_confirm_button_title\":\"确认呼叫网约公交\",\"scan_code_pay_link\":\"https://v.didi.cn/p/DMBER5a?product_id={{product_id}}&city_id={{city_id}}&plate_id={{plate_id}}\"}"
		})
		defer patch.Reset()

		// 情况一：正常情况，包含估计数据
		resp.EstimateData = append(resp.EstimateData, &proto.EstimateFromData{
			ExtraMap: &proto.MiniBusExtraMap{
				Etp: 120,
			},
		})
		oldVersionRender.ExtendDataRender(ctx, resp, baseReqData, products)

		convey.So(resp.MapCurveInfo.IsDrawCurve, convey.ShouldEqual, 1)
		convey.So(*resp.MapCurveInfo.CurveType, convey.ShouldEqual, 2)
		convey.So(resp.PageType, convey.ShouldEqual, int16(page_type.PageTypeSmartBusEstimate))
		convey.So(resp.BackButton, convey.ShouldNotBeNil)
	})
}

func TestOldVersionGetRefreshInterval(t *testing.T) {
	tests := []struct {
		name        string
		refreshTime string
		want        *int32
	}{
		{
			name:        "Test SmartBus Estimate Page",
			refreshTime: "60",
			want:        util.Int32Ptr(60),
		},
		{
			name:        "Test Other Page Type",
			refreshTime: "",
			want:        nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock Apollo config
			timeMock := mockey.Mock(apollo.GetConfigItem).Return(tt.refreshTime).Build()
			defer timeMock.UnPatch()

			r := &OldVersionRender{}
			got := r.GetRefreshInterval()
			if got == nil {
				assert.Nil(t, tt.want)
			} else {
				assert.Equal(t, *tt.want, *got)
			}
		})
	}
}
