package smart_bus_estimate

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	smart_bus_estimate "git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"strings"
	"time"
)

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
}

func CheckParams(ctx context.Context, req *proto.MiniBusEstimateRequest) (err error) {
	if req == nil {
		return BizError.ErrInvalidArgument
	}

	//车上扫码场景
	if err = checkForScanInCar(ctx, req); nil != err {
		return err
	}

	return nil
}

func InitLogic(ctx context.Context, passenger *passport.UserInfo, req *proto.MiniBusEstimateRequest) (*BizLogic, error) {
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:  req.AppVersion,
			AccessKeyID: req.AccessKeyId,
			Channel:     util.IntParseWithDefault(req.Channel, 0),
			Lang:        req.Lang,
			MenuID:      "dache_anycar",
			PageType:    req.GetPageType(),
			FromType:    req.FromType,
			ClientType:  req.ClientType,
			Dchn:        req.GetDchn(),
		}).
		SetGEOInfo(&models.GEOInfo{
			CurrLat:                req.Lat,
			CurrLng:                req.Lng,
			FromLat:                req.FromLat,
			FromLng:                req.FromLng,
			FromPOIID:              req.FromPoiId,
			FromName:               req.FromName,
			FromAddress:            req.FromAddress,
			ToLat:                  req.ToLat,
			ToLng:                  req.ToLng,
			ToPOIID:                req.ToPoiId,
			ToName:                 req.ToName,
			ToAddress:              req.ToAddress,
			MapInfoStartCacheToken: req.MapinfoStartCacheToken,
			MapInfoDestCacheToken:  req.MapinfoDestCacheToken,
			ChooseFSearchid:        req.ChooseFSearchid,
			ChooseTSearchid:        req.ChooseTSearchid,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			UserType: 1,
			OriginID: passenger.OriginId,
		}).
		SetDriverInfo(&models.DriverData{
			DriverId: req.DriverId,
		}).
		SetUserOption(&models.UserOption{
			PaymentsType:        2,
			MultiRequireProduct: req.MultiRequireProduct,
			CarpoolSeatNum:      req.CarpoolSeatNum,
			DepartureTime:       time.Now().Unix(),
		}).
		TryBuild(ctx)
	if err != nil {
		return nil, BizError.ErrSystem
	}
	// 如果传入了trans_data改写page_type、from_type
	setTransData(ctx, req, baseReq)

	productsGen, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pSmartBusEstimate"),
		biz_runtime.WithBaseReq(baseReq),

		//smart_bus_data
		biz_runtime.WithInitSmartBusData(ctx),
		biz_runtime.WithSmartBusStationInfo(ctx, req.StartStationInfo, req.DestStationInfo),
		biz_runtime.WithSmartBusExtData(ctx, req.ExtMap),
	)

	if err != nil {
		return nil, BizError.ErrInvalidArgument
	}

	productsGen.SetNeedMember(true)

	productsGen.SetSendReqKafka(true)

	productsGen.RegisterAfterDdsNoBaseProductsRpcProcess(
		// 预匹配信息，预匹配改动很少，复用滴滴小巴
		rpc_process.NewSmartBusPreMatchRPC(productsGen.BaseReqData),
	)

	if fissionOption := after_dds_fission.NewExpressLineShiftFission(baseReq); fissionOption != nil {
		productsGen.RegisterAfterDdsFission(fissionOption)
	}

	// 预匹配过滤小巴
	if SbFilter := smart_bus_estimate.NewSmartBusFilter(); SbFilter != nil {
		productsGen.RegisterAfterPriceFilter(SbFilter)
	}

	// 获取快线站点详情
	if PrfsRpc := rpc_process.NewSmartBusExpressLineStationDetailRpc(baseReq); PrfsRpc != nil {
		productsGen.RegisterAfterPriceRPCProcess(PrfsRpc)
	}

	if RbRPC := rpc_process.NewSmartBusWalkRoutePlanRPC(ctx, baseReq); RbRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(RbRPC)
	}

	return &BizLogic{generator: productsGen}, nil
}

func setTransData(_ context.Context, req *proto.MiniBusEstimateRequest, baseReq *models.BaseReqData) {
	transData := &proto.TransData{}
	if req.TransData == nil {
		return
	}
	err := json.Unmarshal([]byte(*req.TransData), &transData)
	if err == nil {
		baseReq.CommonInfo.PageType = transData.PageType
		baseReq.CommonInfo.Dchn = transData.GetDchn()
		baseReq.CommonInfo.GuideTraceId = transData.GuideTraceId
	}
}

// SmartBusPageType 从minibus的controller进来的时候，判断是否是智能小巴来源
func SmartBusPageType(_ context.Context, req *proto.MiniBusEstimateRequest) bool {
	if req.PageType != nil && *req.PageType == page_type.PageTypeSmartBusEstimate {
		return true
	}
	if req.TransData != nil {
		transData := &proto.TransData{}
		err := json.Unmarshal([]byte(*req.TransData), &transData)
		if err == nil && transData.PageType == page_type.PageTypeSmartBusEstimate {
			return true
		}
	}
	return false
}

func checkForScanInCar(ctx context.Context, req *proto.MiniBusEstimateRequest) error {
	//车上扫码场景check
	if from_type.FromTypeFromTypeScanInCar != req.FromType {
		return nil
	}

	//司机信息
	if 0 == req.DriverId {
		log.Trace.Warnf(ctx, consts.TagErrSmartBusEstimateParam, "scene[scan_in_car] estimate param err: driver_id=%v", req.DriverId)
		return BizError.ErrInvalidArgument
	}

	//起点信息校验(不能存在token && 起点站点信息不能缺失）
	if startInfo := strings.TrimSpace(req.StartStationInfo); 0 != len(strings.TrimSpace(req.MapinfoStartCacheToken)) || (consts.EmptyObjectJson == startInfo || 0 == len(startInfo)) {
		log.Trace.Warnf(ctx, consts.TagErrSmartBusEstimateParam, "scene[scan_in_car] estimate param err: start_cache_token=%v || startingStationInfo=%v", req.MapinfoStartCacheToken, req.StartStationInfo)
		return BizError.ErrInvalidArgument
	}

	//终点信息校验(终点站点信息 站点token 只能存在一个）
	if destInfo := strings.TrimSpace(req.DestStationInfo); (consts.EmptyObjectJson != destInfo && 0 != len(destInfo)) && 0 != len(strings.TrimSpace(req.MapinfoDestCacheToken)) {
		log.Trace.Warnf(ctx, consts.TagErrSmartBusEstimateParam, "scene[scan_in_car] estimate param err: mapinfo_dest_cache_token and dest_station_info both presence，except only one")
		return BizError.ErrInvalidArgument
	}

	if destInfo := strings.TrimSpace(req.DestStationInfo); (consts.EmptyObjectJson == destInfo || 0 == len(destInfo)) && 0 == len(strings.TrimSpace(req.MapinfoDestCacheToken)) {
		log.Trace.Warnf(ctx, consts.TagErrSmartBusEstimateParam, "scene[scan_in_car] estimate param err: mapinfo_dest_cache_token and dest_station_info both presence，except only one")
		return BizError.ErrInvalidArgument
	}
	return nil
}
