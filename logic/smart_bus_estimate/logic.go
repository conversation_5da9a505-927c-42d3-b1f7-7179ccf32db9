package smart_bus_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"sort"
	"time"
)

const (
	DefaultEtp    = 60 // etp最小兜底，以second为单位
	NoReadyTravel = 0
)

func (b *BizLogic) DoBizLogic(ctx context.Context, req *proto.MiniBusEstimateRequest) (*proto.MiniBusEstimateResp, error) {
	resp := &proto.MiniBusEstimateResp{}
	products, err := b.generator.GenProducts(ctx)
	if err != nil {
		return resp, err
	}
	resp.Data, err = RenderProductList(ctx, b.generator.BaseReqData, products)

	if err != nil {
		return resp, err
	}

	defer func() {
		AddPublicLog(ctx, products, req, b.generator.BaseReqData)
	}()
	return resp, err
}

type card struct {
	index int
	etp   int64
}

func genETP(product *biz_runtime.ProductInfoFull) int64 {
	var realEtp int64
	if product.GetSmartBusEtp() == -1 {
		return product.GetSmartBusEtp()
	}
	var etp int64 = DefaultEtp
	if product == nil || product.Product == nil || product.Product.BizInfo == nil ||
		product.Product.BizInfo.SmartBusPreMatch == nil || product.Product.BizInfo.SmartBusPreMatch.EtpInfo == nil {
		return etp
	}
	preMatch := product.Product.BizInfo.SmartBusPreMatch
	if level_type.IsSmartBusExpressLine(product.Product.LevelType) {
		realEtp = preMatch.EtpInfo.EtpTimeDuration - time.Now().Unix()
	} else {
		realEtp = preMatch.EtpInfo.EtpTimeDuration
	}
	etp = max(etp, realEtp)
	return etp
}

func genCardList(products []*biz_runtime.ProductInfoFull) []*card {
	var res = make([]*card, 0, len(products))
	for i, product := range products {
		res = append(res, &card{
			index: i,
			etp:   genETP(product),
		})
	}

	return res
}

func genSortCards(products []*biz_runtime.ProductInfoFull) []*card {
	cardList := genCardList(products)
	// 候补(etp < 0)的卡片排在最后，非候补的按照etp从小到大排
	sort.SliceStable(cardList, func(i, j int) bool {
		if cardList[i].etp < 0 && cardList[j].etp >= 0 {
			return false
		}
		if cardList[i].etp >= 0 && cardList[j].etp < 0 {
			return true
		}
		return cardList[i].etp < cardList[j].etp
	})
	return cardList
}
