package smart_bus_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const OperaKeyEstimateData = "g_order_cap_multi_estimate_price"

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, req *proto.MiniBusEstimateRequest, baseReqData *models.BaseReqData) {
	if util.IsPressureTraffic(ctx) {
		return
	}
	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}
		logInfo := make(map[string]interface{})
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = product.GetEstimateID()

		if req != nil {
			logInfo["xpsid"] = req.Xpsid
			logInfo["xpsid_root"] = req.XpsidRoot
		}

		//地理位置信息
		areaInfo := product.GetAreaInfo()
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["district"] = areaInfo.District
		logInfo["current_lat"] = areaInfo.CurLat
		logInfo["current_lng"] = areaInfo.CurLng
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)
		if req != nil {
			logInfo["from_poi_id"] = req.FromPoiId
			logInfo["to_poi_id"] = req.ToPoiId
			logInfo["choose_f_searchid"] = req.ChooseFSearchid
			logInfo["choose_t_searchid"] = req.ChooseTSearchid
			logInfo["from_poi_type"] = req.FromPoiType
			logInfo["to_poi_type"] = req.ToPoiType
		}

		//端信息
		commonInfo := product.GetClientInfo()
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel"] = commonInfo.Channel
		logInfo["page_type"] = commonInfo.PageType
		logInfo["lang"] = commonInfo.Lang
		logInfo["dchn"] = commonInfo.Dchn

		//产品信息
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["product_id"] = product.Product.ProductID
		logInfo["level_type"] = product.GetLevelType()
		if product.GetBizInfo() != nil {
			logInfo["combo_id"] = product.GetBizInfo().ComboID
		}
		if level_type.IsSmartBusExpressLine(product.GetLevelType()) {
			if product.GetSmartBusPreMatch() != nil && product.GetSmartBusPreMatch().ShiftInfo != nil {
				shiftInfo := product.GetSmartBusPreMatch().ShiftInfo
				logInfo["shift_id"] = shiftInfo.ShiftID
				if shiftInfo.RouteID != nil {
					logInfo["combo_id"] = shiftInfo.RouteID
				}
				if shiftInfo.StartStation != nil {
					logInfo["start_station_id"] = shiftInfo.StartStation.StationID
				}
				if shiftInfo.EndStation != nil {
					logInfo["end_station_id"] = shiftInfo.EndStation.StationID
				}
			}
		}

		//用户信息
		logInfo["pid"] = product.GetUserInfo().PID
		if product.Product.BizInfo != nil {
			logInfo["carpool_seat_num"] = product.Product.BizInfo.CarpoolSeatNum
			if product.Product.BizInfo.SmartBusPreMatch != nil && product.Product.BizInfo.SmartBusPreMatch.GetEtpInfo() != nil {
				logInfo["etp_time_duration"] = product.Product.BizInfo.SmartBusPreMatch.GetEtpInfo().EtpTimeDuration
				if respTime, ok := product.Product.BizInfo.SmartBusPreMatch.ExtMap["wait_answer_time"]; ok {
					logInfo["candidate_ets"] = respTime
				}
			}
		}
		logInfo["from_type"] = req.FromType
		if req.TransData != nil && "" != baseReqData.CommonInfo.GuideTraceId {
			logInfo["guide_trace_id"] = baseReqData.CommonInfo.GuideTraceId
		}
		log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
	}
}
