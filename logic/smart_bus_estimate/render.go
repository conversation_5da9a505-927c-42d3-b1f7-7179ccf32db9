package smart_bus_estimate

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

func RenderProductList(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) (*proto.MiniBusEstimateData, error) {
	var (
		renderService = data.NewRender(ctx, baseReqData)
		estimateData  = make([]*proto.EstimateFromData, 0)
		resp          = &proto.MiniBusEstimateData{}
	)

	//render前 check
	if err := renderCheck(ctx, baseReqData, products); nil != err {
		return nil, err
	}

	//render
	if len(products) > 0 {
		sortCards := genSortCards(products)

		for _, card := range sortCards {
			if card == nil {
				continue
			}
			if len(products) <= card.index {
				continue
			}
			if product := renderService.RenderByProduct(ctx, products[card.index]); product != nil {
				// 获取座位数
				estimateData = append(estimateData, product)
			}
		}
		data.AdjustSelected(estimateData)
		resp.EstimateData = estimateData
	}

	renderService.ExtendDataRender(ctx, resp, baseReqData, products)

	resp.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	resp.RefreshInterval = renderService.GetRefreshInterval()
	return resp, nil
}

// renderCheck
func renderCheck(ctx context.Context, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) error {
	if renderExpressLineCheck(products) {
		return nil
	}

	//map token check
	if err := renderMapTokenDependencyCheck(ctx, baseReqData); nil != err {
		return err
	}

	//车上扫码场景
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		if 1 < len(products) {
			log.Trace.Warnf(ctx, consts.TagErrSmartBusScanInCarTpCount, "products:%v", products)
			return BizError.ErrInvalidArgument
		}
	}

	return nil
}

func renderExpressLineCheck(products []*biz_runtime.ProductInfoFull) bool {
	// 涉及到快线品类不做token校验
	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}
		if level_type.IsSmartBusExpressLine(product.Product.LevelType) {
			return true
		}
	}

	return false
}

// renderMapTokenDependencyCheck 依赖map_token渲染 check
func renderMapTokenDependencyCheck(ctx context.Context, baseReqData *models.BaseReqData) error {
	if from_type.FromTypeFromTypeScanInCar == baseReqData.CommonInfo.FromType {
		return nil
	}

	if baseReqData.CommonBizInfo.TokenInfo == nil || baseReqData.CommonBizInfo.TokenInfo.MapinfoStartCacheToken == "" || baseReqData.CommonBizInfo.TokenInfo.MapinfoDestCacheToken == "" {
		log.Trace.Warnf(ctx, consts.TagErrMapToken, "token:%v", baseReqData.CommonBizInfo.TokenInfo)
		return BizError.ErrInvalidArgument
	}
	return nil
}
