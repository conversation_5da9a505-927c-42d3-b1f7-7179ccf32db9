package data

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_prefix_icon"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"math"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	commonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	product2 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	bargain_range "git.xiaojukeji.com/gulfstream/mamba/logic/bargain_range_estimate/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/minibus"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_bargain"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	category_unione2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/is_hide_price"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/route_id_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/selection"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/sub_title_list"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"
	"github.com/spf13/cast"
)

const HitNormalNoAnswerCompensation = "hit_normal_no_answer_compensation"

type Render struct {
}

//type AssembleResp struct {
//	EstimateData    *proto.NewFormEstimateData
//	RawEstimateData *proto.RawEstimateData
//}

func NewRender(ctx context.Context) (r *Render) {
	// 初始化配置
	r = &Render{}
	return
}

func (r *Render) PreBuilder(ctx context.Context, products []*biz_runtime.ProductInfoFull) context.Context {
	var (
		fastPrice                       float64
		pricingByMeterPrice             float64
		isHitNormalNoAnswerCompensation bool
	)

	ctx = knife.New(ctx)
	for _, pFull := range products {
		if pFull.GetProductCategory() == ProductCategory.ProductCategoryFast {
			fastPrice = pFull.GetEstimateFee()
		} else if pFull.GetProductCategory() == ProductCategory.ProductCategoryUnione {
			pricingByMeterPrice = pFull.GetEstimateFee()
		}

		privateBizInfo := pFull.GetPrivateBizInfo()
		if privateBizInfo != nil && privateBizInfo.CompensationInfo != nil {
			if decision, ok := privateBizInfo.CompensationInfo[compensation.NormalNoAnswerCompensation]; ok && decision != nil && decision.Decision == 1 {
				isHitNormalNoAnswerCompensation = true
			}
		}
	}

	for _, pFull := range products {
		pFull.BaseReqData.CommonBizInfo.FastCarPrice = fastPrice
		pFull.BaseReqData.CommonBizInfo.PricingByMeterPrice = pricingByMeterPrice
	}

	knife.Set(ctx, HitNormalNoAnswerCompensation, isHitNormalNoAnswerCompensation)
	return ctx
}

func RenderByProduct(ctx context.Context, input interface{}) (interface{}, error) {
	product, ok := input.(*biz_runtime.ProductInfoFull)
	if !ok {
		return nil, errors.New("product req error ok")
	}

	if product == nil || product.Product == nil {
		return nil, errors.New("product req error")
	}
	prov := &AnyCarV3Adapter{ProductInfoFull: product}

	var (
		feeType int
	)

	// 渲染数据
	resp := &proto.NewFormEstimateData{
		EstimateId:      product.Product.EstimateID,
		ProductCategory: product.Product.ProductCategory,
	}

	buildScenes(ctx, resp, product)
	// 车型信息
	resp.CarTitle = car_info.GetCarName(ctx, prov)
	resp.CarIcon = car_info.GetCarIcon(ctx, prov)
	resp.SubTitleList = sub_title_list.GetSubTitleList(ctx, prov)
	// 价格信息
	feeInfo := fee_detail_info.GetFeeInfo(ctx, prov, product)
	resp.FeeMsg, resp.FeeAmount, feeType = feeInfo.FeeMsg, feeInfo.FeeAmount, feeInfo.FeeType
	resp.FeeMsgPrefixIcon = fee_prefix_icon.GetFeeMsgPrefixIcon(ctx, prov)
	resp.MinFeeAmount, resp.FeeRangeTemplate = &feeInfo.MinFeeAmount, &feeInfo.FeeRangeTemplate
	resp.FeeDescList = getPriceInfoDescList(ctx, prov, product, prov.GetPageType())
	resp.MultiPriceList = fee_info_render.GetMultiPriceDesc(ctx, prov, prov.GetUserSelectSeatNum())
	multiPriceList := resp.MultiPriceList
	resp.IsHidePrice = is_hide_price.GetResult(ctx, prov)
	resp.NeedPayFeeAmount = category_bargain.GetNeedPayFeeAmount(ctx, prov)

	// 其他信息
	resp.IsSelected = selection.GetSelection(ctx, prov)
	resp.CarpoolSeatList = anycar_v3.GetCarpoolSeatNum(ctx, prov)
	resp.UserPayInfo = anycar_v3.GetPayInfo(ctx, prov)
	resp.RouteIdList = route_id_list.GetRouteIdList(ctx, prov)
	//resp.IsSingleRoute = single_route.GetIsSingleRoute(ctx, prov)
	resp.SideExtra = anycar_v3.GetSideEstimateData(ctx, product, prov)
	resp.ExtraInfo = anycar_v3.GetExtraInfoData(ctx, prov, feeType)
	resp.PNewOrderParams = anycar_v3.PNewOrderParamsData(ctx, prov)
	resp.ExtraMap = anycar_v3.GetExtraMapAuth(ctx, prov)
	resp.CarpoolSeatModule = minibus.GetCarpoolSeatModule(ctx, product, prov)
	resp.BargainRangePopup = anycar_v3.GetFastRangePopup(ctx, prov)
	resp.LevelType = product.Product.LevelType

	//// 互斥逻辑：sideExtra.RightSubTitle > subTitleList
	//if resp.SideExtra != nil && resp.SideExtra.RightSubTitle != nil {
	//	resp.SubTitleList = nil
	//}

	// 价格差
	baseFeeInfoBody := &anycar_v3.BaseFeeInfoBody{
		FeeMsg:           feeInfo.FeeMsg,
		FeeAmount:        feeInfo.FeeAmount,
		FeeType:          feeInfo.FeeType,
		FeeTemplate:      feeInfo.FeeTemplate,
		MinFeeAmount:     feeInfo.MinFeeAmount,
		FeeRangeTemplate: feeInfo.FeeRangeTemplate,
		SendCarMaxPrice:  getSendOrderMaxPrice(product),
		Product:          product,
		MultiPriceList:   multiPriceList,
	}
	feeDiffAmount, feeDiffMsg := anycar_v3.GetDiffInfo(ctx, baseFeeInfoBody)
	resp.FeeDiffAmount, resp.FeeDiffMsg = &feeDiffAmount, &feeDiffMsg
	resp.FeeDiffDescList = anycar_v3.GetDiffPriceInfoDescList(ctx, baseFeeInfoBody)
	resp.MultiPriceList = anycar_v3.GetDiffMultiPriceList(ctx, baseFeeInfoBody)

	// 纯数据
	rawResp := &proto.RawEstimateData{
		EstimateId:          product.Product.EstimateID,
		ProductCategory:     product.GetProductCategory(),
		ProductId:           product.GetProductId(),
		BusinessId:          product.GetBusinessID(),
		RequireLevel:        product.GetRequireLevel(),
		ComboType:           product.GetComboType(),
		LevelType:           product.GetLevelType(),
		CarpoolType:         product.GetCarpoolType(),
		CountPriceType:      product.GetCountPriceType(),
		IsSpecialPrice:      util.ToInt32(product.GetIsSpecialPrice()),
		SpaciousCarAlliance: product.Product.SpaciousCarAlliance,
		ComboId:             prov.GetComboID(),
		CarpoolPriceType:    prov.GetCarpoolPriceType(),
		IsDualCarpoolPrice:  prov.IsDualCarpoolPrice(),

		EstimateFee:      util.ToFloat64(feeInfo.FeeAmount),
		DynamicTotalFee:  prov.GetDynamicTotalFee(),
		CapPrice:         prov.GetCapPrice(),
		BasicTotalFee:    prov.GetBasicTotalFee(),
		DynamicDiffPrice: prov.GetNewDynamicDiffPrice(),
		ExactEstimateFee: prov.GetExactEstimateFee(),
		DynamicTimes:     prov.GetDynamicTimes(),
		PreTotalFee:      prov.GetPreTotalFee(),

		RouteIdList:   prov.GetBillRouteIdList(),
		DriverMetre:   prov.GetBillDriverMetre(),
		SubGroupId:    prov.GetSubGroupId(),
		IsDefaultAuth: anycar_v3.GetIsDefaultAuth(ctx, prov),
	}

	_, rawResp.RedPacket = prov.GetBillFeeDetailInfoFee(price_info_desc_list.RedPacketFee)
	rawResp.DiscountSet = anycar_v3.GetDiscountSet(ctx, prov)
	rawResp.MultiPriceData = anycar_v3.GetMultiPriceData(ctx, prov)
	rawResp.PaymentId = anycar_v3.GetPayInfo(ctx, prov).PaymentId
	rawResp.Etp = int32(prov.GetEtp())
	if product.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
		rawResp.UndisplayInLayout = true
	}

	assembleResp := &biz_runtime.AssembleResp{
		EstimateData:    resp,
		RawEstimateData: rawResp,
	}

	return assembleResp, nil
}

func buildScenes(ctx context.Context, resp *proto.NewFormEstimateData, product *biz_runtime.ProductInfoFull) {

	resp.SceneInfo = &proto.SceneInfo{
		BusinessId:         product.Product.BusinessID,
		RequireLevel:       product.Product.RequireLevel,
		ComboType:          product.Product.ComboType,
		LevelType:          product.Product.LevelType,
		IsDualCarpoolPrice: product.Product.IsDualCarpoolPrice,
		CarpoolPriceType:   product.Product.CarpoolPriceType,
		CarpoolType:        product.Product.CarpoolType,
		CountPriceType:     product.GetCountPriceType(),
		DynamicTimes:       product.GetDynamicTimes(),
		DynamicTotalFee:    product.GetDynamicTotalFee(),
	}

	if product.GetNewDynamicDiffPrice() > 0 {
		resp.HitDynamicPrice = 1
	}

	if len(product.GetBillFeeDetailInfo()) > 0 && product.GetBillFeeDetailInfo()["red_packet"] > 0 {
		resp.HitShowH5Type = 1
	}

	resp.IsTripcloud = product.Product.IsTripcloudProduct(ctx)
	//todo 之后可以下掉
	resp.ExtraMap = &proto.NewFormExtraMap{
		BusinessId:   product.Product.BusinessID,
		RequireLevel: product.Product.RequireLevel,
		ComboType:    product.Product.ComboType,
		LevelType:    product.Product.LevelType,
		SubGroupId:   product.Product.SubGroupId,
	}
}

func (r *Render) buildScenes(ctx context.Context, resp *proto.NewFormEstimateData, product *biz_runtime.ProductInfoFull) {

	resp.SceneInfo = &proto.SceneInfo{
		BusinessId:         product.Product.BusinessID,
		RequireLevel:       product.Product.RequireLevel,
		ComboType:          product.Product.ComboType,
		LevelType:          product.Product.LevelType,
		IsDualCarpoolPrice: product.Product.IsDualCarpoolPrice,
		CarpoolPriceType:   product.Product.CarpoolPriceType,
		CarpoolType:        product.Product.CarpoolType,
		CountPriceType:     product.GetCountPriceType(),
		DynamicTimes:       product.GetDynamicTimes(),
		DynamicTotalFee:    product.GetDynamicTotalFee(),
	}

	if product.GetNewDynamicDiffPrice() > 0 {
		resp.HitDynamicPrice = 1
	}

	if len(product.GetBillFeeDetailInfo()) > 0 && product.GetBillFeeDetailInfo()["red_packet"] > 0 {
		resp.HitShowH5Type = 1
	}

	resp.IsTripcloud = product.Product.IsTripcloudProduct(ctx)
}

func (r *Render) ExtendDataRender(ctx context.Context,
	resp *proto.NewFormEstimateResponse, gen *biz_runtime.ProductsGenerator) {

	// 费用详情页
	resp.FeeDetailUrl = fee_detail_info.GetDetailUrl(ctx)

}

func getPriceInfoDescList(ctx context.Context, prov *AnyCarV3Adapter, product *biz_runtime.ProductInfoFull, pageType int32) []*proto.NewFormFeeDesc {
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain &&
		product.GetBizInfo().FixPrice != nil {
		if cast.ToFloat64(product.GetBizInfo().FixPrice.DiscountAmount) > 0 { // 司乘议价改价有券
			if conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc", "coupon"); len(conf) > 0 {
				a := util.FormatPrice(cast.ToFloat64(product.GetBizInfo().FixPrice.DiscountAmount), 2)
				return []*proto.NewFormFeeDesc{{
					BorderColor: conf["border_color"].String(),
					Icon:        conf["icon"].String(),
					Content:     dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": cast.ToString(a)}),
					TextColor:   util.StringPtr("#999999")},
				}
			}
		} else { // 司乘议价改价无券

			return []*proto.NewFormFeeDesc{}
		}
	}
	if prov.GetProductCategory() == ProductCategory.ProductCategoryLowPriceCarpool {
		return category_carpool.GetPriceInfoDescListPincheche(ctx, prov, pageType)
	} else if prov.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		// 两口价v3
		return category_carpool.GetPriceInfoDescList(ctx, prov, prov.GetPageType())
	} else if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车
		return category_unione2.GetPriceInfoDescList(ctx, prov)
	} else if product2.IsHongKongProduct(commonConsts.ProductID(prov.GetProductId())) { // HongKong Product
		return category_unione2.GetPriceInfoDescListHarbour(ctx, prov)
	}
	return fee_info_render.GetPriceInfoDescList(ctx, prov)
}

func getSendOrderMaxPrice(product *biz_runtime.ProductInfoFull) float64 {
	if product == nil || product.GetSendOrderMultiRequiredProduct() == nil {
		return 0
	}
	var maxPrice float64

	multiRequireProducts := product.GetSendOrderMultiRequiredProduct()
	for _, productItem := range multiRequireProducts {
		tmpPrice := productItem.EstimateFee
		// 惠选车使用已发单的最高价
		if productItem.ProductCategory == estimate_pc_id.EstimatePcIdHuiXuanCar && productItem.PassengerBargainRangeInfo != "" {
			if rightPrice, err := bargain_range.GetBargainRangeOrderPrice(productItem.PassengerBargainRangeInfo, consts.MaxPrice); err == nil {
				tmpPrice = rightPrice
			}
		}

		maxPrice = math.Max(maxPrice, tmpPrice)
	}
	return maxPrice
}
