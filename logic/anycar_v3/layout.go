package anycar_v3

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	CommonBuilder "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder"
)

type Layout struct {
	gen              *biz_runtime.ProductsGenerator
	BaseReqData      *models.BaseReqData
	estimateData     map[int64]*LayoutConsts.SimpleEstimateData
	productsMap      map[int64]*biz_runtime.ProductInfoFull
	groupProductsMap map[int64]*biz_runtime.ProductInfoFull
	subProductMap    map[int32]map[int64]*biz_runtime.ProductInfoFull
	isUsed           map[int64]bool
}

func New(ctx context.Context, BaseReqData *models.BaseReqData, estimateData map[int64]*proto.NewFormEstimateData, productsMap map[int64]*biz_runtime.ProductInfoFull) *Layout {
	var (
		simpleEstimateData = make(map[int64]*LayoutConsts.SimpleEstimateData, 0)
	)

	for product, item := range estimateData {
		simpleEstimateData[product] = &LayoutConsts.SimpleEstimateData{
			FeeDescList: item.FeeDescList,
		}
	}

	return &Layout{
		BaseReqData:      BaseReqData,
		estimateData:     simpleEstimateData,
		productsMap:      productsMap,
		subProductMap:    make(map[int32]map[int64]*biz_runtime.ProductInfoFull),
		groupProductsMap: make(map[int64]*biz_runtime.ProductInfoFull),
	}
}

func (l *Layout) BuildLayout(ctx context.Context) []*proto.NewFormLayout {
	var (
		layoutList []func(context.Context) CommonBuilder.IBuilder
	)

	commonLayout := layout.NewLayout(ctx, l.BaseReqData, l.estimateData, l.productsMap)

	layoutList = []func(context.Context) CommonBuilder.IBuilder{
		commonLayout.GetBargainNormalBuilder,
		commonLayout.GetdUnitaxiBoxBuilder,
		commonLayout.GetTaxiPricingBoxBuilder,
		commonLayout.GetShortDistanceCarAllianceBuilder,
		commonLayout.GetFarMustCheapLayoutBuilder,
		commonLayout.GetWellWorthPriceLayoutBuilder,
		commonLayout.GetHongKongThirtyNormalBuilder,
		commonLayout.GetHongKongThirtyComfortBuilder,
		commonLayout.GetSpaciousCarLayoutBuilder,
	}

	return commonLayout.BuildCommonLayout(ctx, layoutList)
}
